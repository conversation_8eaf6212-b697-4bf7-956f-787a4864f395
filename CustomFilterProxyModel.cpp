﻿#include "CustomFilterProxyModel.h"
#include <QFileSystemModel>
#include <QFileInfo>
#include <QDebug>
#include "FileManage.h"

CustomFilterProxyModel::CustomFilterProxyModel(QObject* parent)
	: QSortFilterProxyModel(parent)
	, m_filterDate(QDate::currentDate()) // 默认使用当前日期
	, m_filterKeyword("")               // 默认关键字为空
	, m_enableDateFilter(false)         // 默认禁用日期过滤
	, m_enableKeywordFilter(false)      // 默认禁用关键字过滤
	, m_useCustomFilter(false) // 默认不启用自定义过滤逻辑
{
}

void CustomFilterProxyModel::setFilterDate(const QDate& date)
{
	if (m_filterDate != date) { // 如果日期发生变化
		m_filterDate = date;
		reapplyFilters(); // 重新应用过滤器
	}
}

void CustomFilterProxyModel::setFilterKeyword(const QString& keyword)
{
	if (m_filterKeyword != keyword) { // 如果关键字发生变化
		m_filterKeyword = keyword;
		reapplyFilters(); // 重新应用过滤器
	}
}

void CustomFilterProxyModel::enableDateFilter(bool enable)
{
	if (m_enableDateFilter != enable) { // 如果状态发生变化
		m_enableDateFilter = enable;
		reapplyFilters(); // 重新应用过滤器
	}
}

void CustomFilterProxyModel::enableKeywordFilter(bool enable)
{
	if (m_enableKeywordFilter != enable) { // 如果状态发生变化
		m_enableKeywordFilter = enable;
        reapplyFilters(); // 重新应用过滤器

	}
}

void CustomFilterProxyModel::reapplyFilters()
{
	invalidateFilter(); // 重新应用过滤器
}

void CustomFilterProxyModel::setUseCustomFilter(bool use)
{
	if (m_useCustomFilter != use) {
		m_useCustomFilter = use;
		invalidateFilter(); // 重新应用过滤器
	}
}

bool CustomFilterProxyModel::filterAcceptsRow(int sourceRow, const QModelIndex& sourceParent) const
{
	if (!m_useCustomFilter) {
		// 如果不启用自定义过滤逻辑，调用基类的默认过滤逻辑
		return QSortFilterProxyModel::filterAcceptsRow(sourceRow, sourceParent);
	}

	QFileSystemModel* fileSystemModel = qobject_cast<QFileSystemModel*>(sourceModel());
	if (!fileSystemModel) {
		return true; // 如果源模型不是 QFileSystemModel，则默认接受该行
	}

	QModelIndex index = fileSystemModel->index(sourceRow, 0, sourceParent);
	QFileInfo fileInfo = fileSystemModel->fileInfo(index);

	// 获取当前文件夹路径
	QString currentFolderPath = static_cast<FileManage*>(parent())->getCurrentFolderPath();

	// 调试信息：输出文件路径和当前文件夹路径
	qDebug() << "Checking file:" << fileInfo.filePath();
	qDebug() << "Current folder path:" << currentFolderPath;

	// 如果文件路径不是当前文件夹或其子文件夹，直接拒绝
	if (!fileInfo.filePath().startsWith(currentFolderPath)) {
		qDebug() << "File is not in current folder. Rejected.";
		return false;
	}

	// 如果是当前文件夹本身，直接接受
	if (fileInfo.filePath() == currentFolderPath) {
		qDebug() << "File is the current folder itself. Accepted.";
		return true;
	}

	// 检查是否启用日期过滤
	bool matchesDate = true;
	if (m_enableDateFilter) {
		matchesDate = fileInfo.lastModified().date() == m_filterDate;
		qDebug() << "File last modified date:" << fileInfo.lastModified().toString("yyyy-MM-dd");
		qDebug() << "Filter date:" << m_filterDate.toString("yyyy-MM-dd");
		qDebug() << "Matches date:" << matchesDate;
	}

	// 检查是否启用关键字过滤
	bool matchesKeyword = true;
	if (m_enableKeywordFilter) {
		matchesKeyword = fileInfo.fileName().contains(m_filterKeyword, Qt::CaseInsensitive);
		qDebug() << "File name:" << fileInfo.fileName();
		qDebug() << "Filter keyword:" << m_filterKeyword;
		qDebug() << "Matches keyword:" << matchesKeyword;
	}

	// 返回是否满足当前启用的过滤条件
	bool accepted = matchesDate && matchesKeyword;
	qDebug() << "File accepted:" << accepted;
	return accepted;
}
