#include "CustomProgressDialog.h"

CustomProgressDialog::CustomProgressDialog(QWidget *parent)
    : CustomDialog(parent)
    , m_contentLayout(nullptr)
    , m_statusLabel(nullptr)
    , m_progressBar(nullptr)
    , m_speedLabel(nullptr)
    , m_cancelButton(nullptr)
    , m_wasCanceled(false)
{
    setupProgressUI();
    setDialogSize(450, 220);
    setModal(true);
}

CustomProgressDialog::~CustomProgressDialog()
{
}

void CustomProgressDialog::setupProgressUI()
{
    // 创建内容容器
    QWidget *contentWidget = new QWidget(this);
    m_contentLayout = new QVBoxLayout(contentWidget);
    m_contentLayout->setSpacing(15);
    m_contentLayout->setContentsMargins(20, 20, 20, 20);
    
    // 创建状态标签
    m_statusLabel = new QLabel("正在处理...", contentWidget);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_statusLabel->setStyleSheet(
        "QLabel {"
        "    font-size: 14px;"
        "    color: #333333;"
        "    background: transparent;"
        "}"
    );
    
    // 创建进度条
    m_progressBar = new QProgressBar(contentWidget);
    m_progressBar->setRange(0, 100);
    m_progressBar->setValue(0);
    m_progressBar->setFixedHeight(25);
    m_progressBar->setStyleSheet(
        "QProgressBar {"
        "    border: 1px solid #cccccc;"
        "    border-radius: 12px;"
        "    background-color: #f0f0f0;"
        "    text-align: center;"
        "    font-size: 12px;"
        "    color: #333333;"
        "}"
        "QProgressBar::chunk {"
        "    background-color: #0078d4;"
        "    border-radius: 11px;"
        "}"
    );
    
    // 创建速度标签
    m_speedLabel = new QLabel("", contentWidget);
    m_speedLabel->setObjectName("speedLabel"); // 设置对象名称以便查找
    m_speedLabel->setAlignment(Qt::AlignCenter);
    m_speedLabel->setStyleSheet(
        "QLabel {"
        "    font-size: 12px;"
        "    color: #666666;"
        "    background: transparent;"
        "}"
    );
    
    // 创建取消按钮
    m_cancelButton = new QPushButton("取消", contentWidget);
    m_cancelButton->setFixedSize(80, 30);
    m_cancelButton->setStyleSheet(
        "QPushButton {"
        "    background-color: #f0f0f0;"
        "    border: 1px solid #cccccc;"
        "    border-radius: 4px;"
        "    font-size: 12px;"
        "    color: #333333;"
        "}"
        "QPushButton:hover {"
        "    background-color: #e0e0e0;"
        "    border-color: #999999;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #d0d0d0;"
        "}"
    );
    
    // 连接取消按钮信号
    connect(m_cancelButton, &QPushButton::clicked, this, &CustomProgressDialog::onCancelClicked);
    
    // 创建按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_cancelButton);
    buttonLayout->addStretch();
    
    // 添加到内容布局
    m_contentLayout->addWidget(m_statusLabel);
    m_contentLayout->addWidget(m_progressBar);
    m_contentLayout->addWidget(m_speedLabel);
    m_contentLayout->addStretch();
    m_contentLayout->addLayout(buttonLayout);
    
    // 设置内容
    setDialogContent(contentWidget);
}

void CustomProgressDialog::setRange(int minimum, int maximum)
{
    if (m_progressBar) {
        m_progressBar->setRange(minimum, maximum);
    }
}

void CustomProgressDialog::setValue(int value)
{
    if (m_progressBar) {
        m_progressBar->setValue(value);
    }
}

int CustomProgressDialog::value() const
{
    return m_progressBar ? m_progressBar->value() : 0;
}

void CustomProgressDialog::setLabelText(const QString &text)
{
    if (m_statusLabel) {
        m_statusLabel->setText(text);
    }
}

void CustomProgressDialog::setSpeedText(const QString &text)
{
    if (m_speedLabel) {
        m_speedLabel->setText(text);
    }
}

void CustomProgressDialog::setCancelButtonText(const QString &text)
{
    if (m_cancelButton) {
        m_cancelButton->setText(text);
    }
}

void CustomProgressDialog::setCancelButtonVisible(bool visible)
{
    if (m_cancelButton) {
        m_cancelButton->setVisible(visible);
    }
}

bool CustomProgressDialog::wasCanceled() const
{
    return m_wasCanceled;
}

void CustomProgressDialog::onCancelClicked()
{
    m_wasCanceled = true;
    emit canceled();
    reject();
}
