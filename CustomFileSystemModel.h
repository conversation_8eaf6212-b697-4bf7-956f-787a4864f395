﻿// CustomFileSystemModel.h
#ifndef CUSTOMFILESYSTEMMODEL_H
#define CUSTOMFILESYSTEMMODEL_H
#include <QSettings>
#include <QFileSystemModel>
#include <QMap>

#include "mainwindow.h"


class CustomFileSystemModel : public QFileSystemModel
{
	Q_OBJECT

public:


	explicit CustomFileSystemModel(QObject* parent = nullptr);

	// 重写 headerData 方法
	QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;

	// 重写 data 方法
	QVariant data(const QModelIndex& index, int role = Qt::DisplayRole) const override;

	// 自定义方法：获取文件类型
	QString getFileType(const QModelIndex& index) const;

	// 自定义方法：格式化文件大小
	QString formatFileSize(const QModelIndex& index) const;

	QString Languagetext(int index, QString text)const;

private:
    // 创建默认的中文INI文件
    void createDefaultChineseIni(const QString& filePath) const;

    // 获取默认中文文本
    QString getDefaultChineseText(const QString& key) const;
};


#endif // CUSTOMFILESYSTEMMODEL_H
