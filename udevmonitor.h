#ifndef UDEVMONITOR_H
#define UDEVMONITOR_H

#include <QObject>
#include <QSocketNotifier>

// 前向声明，避免直接包含 udev.h 污染全局命名空间
struct udev;
struct udev_monitor;

class UdevMonitor : public QObject {
    Q_OBJECT
public:
    explicit UdevMonitor(QObject *parent = nullptr);
    ~UdevMonitor();

    bool isValid() const { return m_udev && m_monitor; }

signals:
    // 当输入设备（如鼠标）插拔时发出信号
    void deviceChanged();

private slots:
    void handleUdevEvent();

private:
    udev *m_udev = nullptr;
    udev_monitor *m_monitor = nullptr;
    QSocketNotifier *m_notifier = nullptr;
};

#endif // UDEVMONITOR_H
