#ifndef VIRTUALKEYBOARD_H
#define VIRTUALKEYBOARD_H

#include <QWidget>
#include <QLineEdit>
#include <QSignalMapper>
#include <QPushButton>
#include <QGraphicsOpacityEffect>
#include <QPropertyAnimation>

class VirtualKeyboard : public QWidget
{
    Q_OBJECT

public:
    explicit VirtualKeyboard(QWidget *parent = nullptr);
    void attachTo(QLineEdit *lineEdit);
    void showAtOptimalPosition(QWidget *targetWidget);

signals:
    void keyPressed(const QString &text);  // 添加这行声明信号

protected:
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;

private slots:
    void handleKeyPress(const QString &text);
    void handleBackspace();
    void handleSpace();
    void handleEnter();
    void handleShift();
    void handleClose();

private:
    void createKeyboard();
    void toggleShift();
    void addButtonAnimation(QPushButton *button);

    QLineEdit *m_attachedLineEdit;
    QSignalMapper *m_signalMapper;
    QList<QPushButton *> m_letterButtons;
    bool m_shiftActive;
    bool m_dragging;
    QPoint m_dragPosition;
};

#endif // VIRTUALKEYBOARD_H
