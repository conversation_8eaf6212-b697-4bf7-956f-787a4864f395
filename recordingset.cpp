#include "recordingset.h"
#include "ui_recordingset.h"
#include <QDir>
#include <QFileDialog>
#include "mainwindow.h"
#include <QStorageInfo>
RecordingSet::RecordingSet(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::RecordingSet)
{
    ui->setupUi(this);
    setWindowFlags(Qt::FramelessWindowHint);
    //setWindowModality(Qt::ApplicationModal);
    //setWindowFlag(Qt::WindowStaysOnTopHint, true);
    show();
    //activateWindow();
    //raise(); // 确保窗口被提升到最顶层



    QButtonGroup *group1 = new QButtonGroup(this);
    QButtonGroup *group2 = new QButtonGroup(this);
    group1->addButton(ui->radioButton_time, 0);
    group1->addButton(ui->radioButton_byte, 1);

    group2->addButton(ui->radioButton_high, 0);
    group2->addButton(ui->radioButton_middle, 1);
    group2->addButton(ui->radioButton_low, 2);
    ui->radioButton_middle->setChecked(true);

    ui->radioButton_time->setEnabled(false);
    ui->radioButton_byte->setEnabled(false);



    ui->spinBox_storagelimit->setMinimum(0);
    ui->spinBox_time->setMinimum(0);
    ui->spinBox_byte->setMinimum(0);


    // 加载当前通道的设置
    loadChannelSettings();

    // 初始化时检查一次存储空间
    spaceCheckTimer();


    // 初始化虚拟键盘
    m_virtualKeyboard = new VirtualKeyboard(this);
    m_textEditProxy = new QLineEdit(this);
    m_textEditProxy->hide();

    // 设置你的QTextEdit控件
    setupTextEditKeyboard(ui->textEdit_prefix);
    setupSpinBoxKeyboard(ui->spinBox_time);
    setupSpinBoxKeyboard(ui->spinBox_byte);
    setupSpinBoxKeyboard(ui->spinBox_storagelimit);

    // 添加SpinBox值变化监听，自动限制最大值
    connect(ui->spinBox_time, QOverload<int>::of(&QSpinBox::valueChanged), this, [this](int value) {
        if (value > 120) {
            ui->spinBox_time->setValue(120);
        }
    });

    connect(ui->spinBox_byte, QOverload<int>::of(&QSpinBox::valueChanged), this, [this](int value) {
        if (value > 2048) {
            ui->spinBox_byte->setValue(2048);
        }
    });
}

RecordingSet::~RecordingSet()
{
    // MainWindow* m_mainWindow = qobject_cast<MainWindow*>(parent());
    // m_mainWindow->is_videosetopen=false;
    // 安全释放键盘资源
    if (m_virtualKeyboard) {
        // 先停止键盘可能正在进行的操作
        m_virtualKeyboard->hide();
        m_virtualKeyboard->disconnect(); // 断开所有信号连接
        delete m_virtualKeyboard;
    }

    // 释放代理控件
    if (m_textEditProxy) {
        m_textEditProxy->disconnect();
        delete m_textEditProxy;
    }


    delete ui;
}

// 加载当前通道的设置
void RecordingSet::loadChannelSettings()
{
    // 获取MainWindow指针
    MainWindow* m_mainWindow = qobject_cast<MainWindow*>(parent());
    if (!m_mainWindow) {
        return;
    }

    // 获取当前通道号
    int currentChannel = m_mainWindow->get_curchannel();
    // 检查通道号有效性
    if (currentChannel < 0 || currentChannel >= 4) {
        qWarning("无效的通道号: %d", currentChannel);
        return;
    }

    // 设置默认基础路径（简化路径配置）
    if(m_mainWindow->RecordingSettings[currentChannel].isinit.isEmpty())
    {
        // 使用基础路径而不是通道特定路径
        QString basePath = m_mainWindow->basePath;
        if (basePath.isEmpty()) {
            basePath = "/mnt/nvme";
        }
        // 同时更新basePath字段
        m_mainWindow->RecordingSettings[currentChannel].basePath = basePath;
    }
    //前缀名设置
    ui->textEdit_prefix->setText(m_mainWindow->RecordingSettings[currentChannel].name);

    // 设置存储路径 - 优先使用basePath（简化路径配置）
    QString displayPath;
    if (!m_mainWindow->RecordingSettings[currentChannel].basePath.isEmpty()) {
        displayPath = m_mainWindow->RecordingSettings[currentChannel].basePath;
    } else if (!m_mainWindow->RecordingSettings[currentChannel].path.isEmpty()) {
        // 向后兼容：如果没有basePath但有path，则使用path
        displayPath = m_mainWindow->RecordingSettings[currentChannel].path;
    }


    
    // 设置分段录像选项
    ui->checkBox_segrecord->setChecked(m_mainWindow->RecordingSettings[currentChannel].segmentedVideo);
    
    // 设置分段方式
    if (m_mainWindow->RecordingSettings[currentChannel].recordingByTime) {
        ui->radioButton_time->setChecked(true);

    } else if (m_mainWindow->RecordingSettings[currentChannel].byFileSize) {
        ui->radioButton_byte->setChecked(true);
    }
    
    // 设置分段参数 - 添加限制检查
    int segmentedTime = m_mainWindow->RecordingSettings[currentChannel].segmentedTime;
    int segmentedSize = m_mainWindow->RecordingSettings[currentChannel].segmentedSize;

    // 限制时间分段最大值为120分钟
    if (segmentedTime > 120) {
        segmentedTime = 120;
        m_mainWindow->RecordingSettings[currentChannel].segmentedTime = 120;  // 同时更新配置
    }

    // 限制大小分段最大值为2048MB
    if (segmentedSize > 2048) {
        segmentedSize = 2048;
        m_mainWindow->RecordingSettings[currentChannel].segmentedSize = 2048;  // 同时更新配置
    }

    ui->spinBox_time->setValue(segmentedTime);
    ui->spinBox_byte->setValue(segmentedSize);
    
    // 设置录像画质
    switch (m_mainWindow->RecordingSettings[currentChannel].recordingQuality) {
        case 0:
            ui->radioButton_high->setChecked(true);
            break;
        case 1:
            ui->radioButton_middle->setChecked(true);
            break;
        case 2:
            ui->radioButton_low->setChecked(true);
            break;
        default:
            ui->radioButton_middle->setChecked(true);
            break;
    }
    
    // 设置存储限制
    ui->checkBox_storagelimit->setChecked(m_mainWindow->RecordingSettings[currentChannel].videoStorage);
    if(m_mainWindow->RecordingSettings[currentChannel].storageSize==0){
        ui->spinBox_storagelimit->setValue(10);
    }
    else{
        ui->spinBox_storagelimit->setValue(m_mainWindow->RecordingSettings[currentChannel].storageSize);
    }
    
    // 调用槽函数来设置初始状态
    Sectional_recording();  // 处理分段录像状态
    video_storage();        // 处理存储限制状态
}


void RecordingSet::spaceCheckTimer()
{
    // 获取MainWindow指针
    MainWindow* m_mainWindow = qobject_cast<MainWindow*>(parent());
    if (!m_mainWindow) {
        qWarning("Failed to get MainWindow pointer.");
        ui->label_freespace->setText("Failed to get MainWindow pointer.");
        return;
    }

    // 获取当前通道号
    int currentChannel = m_mainWindow->get_curchannel();
    if (currentChannel < 0 || currentChannel >= 4) {
        qWarning("Invalid channel number: %d", currentChannel);
        ui->label_freespace->setText("Invalid channel number.");
        return;
    }

    // 获取当前通道的存储路径
    QString storagePath = "/mnt/nvme";
    if (storagePath.isEmpty()) {
        // 如果UI中的路径为空，使用配置中的路径
        storagePath = m_mainWindow->RecordingSettings[currentChannel].path;
    }

    // 如果路径仍然为空，使用默认路径
    if (storagePath.isEmpty()) {
        storagePath = "/mnt/nvme/";
    }

    // 获取指定路径的可用空间
    QStorageInfo storageInfo(storagePath);
    if (!storageInfo.isValid()) {
        ui->label_freespace->setText("Invalid storage path.");
        return;
    }

    qint64 availableSize = storageInfo.bytesAvailable(); // 可用大小（字节）
    double availableSizeGB = availableSize / (1024.0 * 1024 * 1024); // 转换为 GB

    // 更新 UI
    ui->label_freespace->setText(QString(" %1 GB").arg(availableSizeGB, 0, 'f', 2));

    // 计算其他通道的存储限制总和
    double otherChannelsStorageSize = 0.0;
    for (int i = 0; i < 4; ++i) {
        if (i != currentChannel) { // 跳过当前通道
            otherChannelsStorageSize += m_mainWindow->RecordingSettings[i].storageSize;
        }
    }

    // 计算当前通道的最大存储限制
    double maxStorageLimit = availableSizeGB - 10.0 - otherChannelsStorageSize;
    if (maxStorageLimit < 0) {
        maxStorageLimit = 0; // 如果计算结果为负数，则设置为 0
    }

    // 设置存储限制的最大值
    ui->spinBox_storagelimit->setMaximum(maxStorageLimit);
}

// ================== TextEdit键盘设置 ==================
void RecordingSet::setupTextEditKeyboard(QTextEdit* textEdit)
{
    if (!textEdit) {
        qWarning() << "Invalid QTextEdit pointer!";
        return;
    }

    textEdit->installEventFilter(this);
    textEdit->viewport()->installEventFilter(this);  // 关键！必须监听视口
    textEdit->setFocusPolicy(Qt::StrongFocus);
    textEdit->setAttribute(Qt::WA_InputMethodEnabled, true);  // 启用中文输入
}

// ================== SpinBox键盘设置 ==================
void RecordingSet::setupSpinBoxKeyboard(QSpinBox* spinBox)
{
    if (!spinBox) {
        qWarning() << "Invalid QSpinBox pointer!";
        return;
    }

    // 获取内置的LineEdit
    QLineEdit* lineEdit = spinBox->findChild<QLineEdit*>();
    if (!lineEdit) {
        qWarning() << "Cannot find LineEdit in QSpinBox!";
        return;
    }

    // 安装事件过滤器
    spinBox->installEventFilter(this);
    lineEdit->installEventFilter(this);

    // 设置数值范围验证
    lineEdit->setValidator(new QIntValidator(spinBox->minimum(), spinBox->maximum(), this));

    // 调试样式（正式发布时可移除）
    lineEdit->setStyleSheet("border: 1px solid #2ecc71; background: #f0fff0;");
}

// ================== 事件过滤器核心逻辑 ==================
bool RecordingSet::eventFilter(QObject* obj, QEvent* event)
{
    // 1. 处理QTextEdit点击
    QTextEdit* textEdit = qobject_cast<QTextEdit*>(obj);
    if (!textEdit) {
        if (obj == ui->textEdit_prefix->viewport()) {
            textEdit = ui->textEdit_prefix;
        } else {
            // 2. 处理SpinBox点击
            QSpinBox* spinBox = qobject_cast<QSpinBox*>(obj);
            QLineEdit* spinBoxLineEdit = nullptr;

            if (spinBox) {
                spinBoxLineEdit = spinBox->findChild<QLineEdit*>();
            } else if (QLineEdit* le = qobject_cast<QLineEdit*>(obj)) {
                if ((spinBox = qobject_cast<QSpinBox*>(le->parent()))) {
                    spinBoxLineEdit = le;
                }
            }

            if (spinBoxLineEdit && event->type() == QEvent::MouseButtonRelease) {
                // 关键修改：添加启用状态检查
                if (!spinBox->isEnabled()) {
                    return QDialog::eventFilter(obj, event);
                }

                QMouseEvent* mouseEvent = static_cast<QMouseEvent*>(event);

                // 获取SpinBox的上下按钮区域
                QStyleOptionSpinBox opt;
                opt.initFrom(spinBox);
                QRect upRect = spinBox->style()->subControlRect(
                    QStyle::CC_SpinBox, &opt, QStyle::SC_SpinBoxUp, spinBox);
                QRect downRect = spinBox->style()->subControlRect(
                    QStyle::CC_SpinBox, &opt, QStyle::SC_SpinBoxDown, spinBox);

                // 如果点击位置不在按钮区域内，才触发键盘
                if (!upRect.contains(mouseEvent->pos()) &&
                    !downRect.contains(mouseEvent->pos())) {
                    spinBoxLineEdit->setFocus();
                    m_virtualKeyboard->attachTo(spinBoxLineEdit);
                    m_virtualKeyboard->showAtOptimalPosition(spinBox);
                    return true;
                }
            }

            return QDialog::eventFilter(obj, event);
        }
    }

    // QTextEdit处理逻辑
    if (event->type() == QEvent::MouseButtonRelease) {
        // 关键修改：添加QTextEdit启用状态检查
        if (!textEdit->isEnabled()) {
            return QDialog::eventFilter(obj, event);
        }

        textEdit->setFocus(Qt::MouseFocusReason);
        m_currentTextEdit = textEdit;

        // 获取当前文本和选择
        QString currentText = textEdit->toPlainText();
        QTextCursor cursor = textEdit->textCursor();
        int selectionStart = cursor.selectionStart();
        int selectionEnd = cursor.selectionEnd();
        bool hasSelection = cursor.hasSelection();

        m_textEditProxy->setText(currentText);

        // 如果有选择，设置QLineEdit的选择状态
        if (hasSelection) {
            m_textEditProxy->setSelection(selectionStart, selectionEnd - selectionStart);
        } else {
            m_textEditProxy->setCursorPosition(cursor.position());
        }

        m_textEditProxy->setFocus();

        // 断开之前的连接，避免重复连接
        disconnect(m_textEditProxy, &QLineEdit::textChanged, nullptr, nullptr);

        // 重新连接textChanged信号
        connect(m_textEditProxy, &QLineEdit::textChanged, this, [this, textEdit]() {
            if (!textEdit) return;

            // 获取当前QLineEdit的状态
            QString newText = m_textEditProxy->text();
            int cursorPos = m_textEditProxy->cursorPosition();
            int selectionStart = m_textEditProxy->selectionStart();
            int selectionLength = m_textEditProxy->selectedText().length();

            // 开始编辑块，避免多次触发事件
            textEdit->blockSignals(true);
            QTextCursor cursor = textEdit->textCursor();

            // 如果有选中文本，替换选中部分
            if (selectionLength > 0) {
                cursor.beginEditBlock();
                cursor.setPosition(selectionStart);
                cursor.setPosition(selectionStart + selectionLength, QTextCursor::KeepAnchor);
                cursor.removeSelectedText();
                cursor.insertText(newText);
                cursor.endEditBlock();
            }
            // 没有选中文本，直接设置整个文本
            else {
                textEdit->setPlainText(newText);
                cursor.setPosition(cursorPos);
                textEdit->setTextCursor(cursor);
            }

            textEdit->blockSignals(false);
        });

        m_virtualKeyboard->attachTo(m_textEditProxy);
        m_virtualKeyboard->showAtOptimalPosition(textEdit);
        return true;
    }
    return QDialog::eventFilter(obj, event);
}

// ================== SpinBox专用处理 ==================
bool RecordingSet::handleSpinBoxKeyboard(QSpinBox* spinBox, QLineEdit* lineEdit)
{
    qDebug() << "SpinBox clicked, setting up numeric keyboard...";

    // 设置焦点
    spinBox->setFocus();
    lineEdit->setFocus(Qt::MouseFocusReason);
    lineEdit->selectAll();


    // 绑定键盘
    m_virtualKeyboard->attachTo(lineEdit);

    // 数值同步逻辑
    disconnect(lineEdit, &QLineEdit::textChanged, nullptr, nullptr);
    connect(lineEdit, &QLineEdit::textChanged, this, [spinBox, lineEdit]() {
        bool ok;
        int value = lineEdit->text().toInt(&ok);
        if (ok) {
            spinBox->setValue(value);
            qDebug() << "SpinBox value updated to:" << value;
        }
    });

    // 定位键盘
    QPoint pos = spinBox->mapToGlobal(QPoint(0, spinBox->height()));
    m_virtualKeyboard->move(pos.x(), pos.y() + 5);  // 下移5像素避免遮挡
    m_virtualKeyboard->show();
    m_virtualKeyboard->raise();

    return true;
}

void RecordingSet::PathSelect()
{
    MainWindow* mainWindow = qobject_cast<MainWindow*>(parent()); // 获取父窗口指针
    QString path = "/mnt/nvme";
    if (path.isEmpty()) {
        path = QDir::homePath();
    }
    // 创建自定义文件对话框
    QFileDialog dialog(this);
    dialog.setWindowTitle(mainWindow->systemmanager->getTranslatedText("choose_storage_directory",mainWindow->currentLanguageIndex,"recordingset"));
    dialog.setDirectory(path);
    dialog.setFileMode(QFileDialog::Directory);
    dialog.setOptions(QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);

    // 设置对话框的颜色
    dialog.setStyleSheet("QFileDialog { background-color: #f0f0f0; color: #333333; border: 1px solid #cccccc; } "
                         "QFileDialog::item { background-color: #ffffff; } "
                         "QFileDialog::item:selected { background-color: #a8c7f7; color: #ffffff; } "
                         "QDialogButtonBox QPushButton { background-color: #ffffff; color: #333333; } "
                         "QDialogButtonBox QPushButton:hover { background-color: #f0f0f0; } "
                         "QLineEdit { background-color: #ffffff; color: #333333; border: 1px solid #cccccc; } "
                         "QListView { background-color: #f5f5f5; border: 1px solid #cccccc; color: #333333; } "
                         "QTreeView { background-color: #f5f5f5; color: #333333; border: 1px solid #cccccc; } "
                         "QTreeView::item { background-color: #ffffff; color: #333333; } "
                         "QTreeView::item:selected { background-color: #a8c7f7; color: #ffffff; } "
                         "QTreeView::branch { background-color: #f5f5f5; } "
                         "QScrollBar { background-color: #f0f0f0; } "
                         "QScrollBar::handle { background-color: #cccccc; } "
                         "QFileDialog::separator { background-color: #cccccc; } "
                         "QLabel { background-color: #f0f0f0; color: #333333; } "
                         "QStatusBar { background-color: #f0f0f0; color: #333333; border: 1px solid #cccccc; } "
                         "QHeaderView { background-color: #f0f0f0; color: #333333; } "
                         "QHeaderView::section { background-color: #e0e0e0; color: #333333; border: 1px solid #cccccc; } "
                         "QTableView { background-color: #ffffff; color: #333333; gridline-color: #cccccc; } "
                         "QTableView::item { background-color: #ffffff; color: #333333; } "
                         "QTableView::item:selected { background-color: #a8c7f7; color: #ffffff; } "
                         "QMenu { background-color: #ffffff; border: 1px solid #cccccc; border-radius: 4px; padding: 4px; } "
                         "QMenu::item { background-color: #ffffff; color: #333333; padding: 6px 20px; border-radius: 2px; } "
                         "QMenu::item:selected { background-color: #a8c7f7; color: #ffffff; } "
                         "QMenu::item:pressed { background-color: #9bb8f0; color: #ffffff; } "
                         "QMessageBox { background-color: #ffffff; border: 1px solid #cccccc; } "
                         "QMessageBox QLabel { background-color: #ffffff; color: #333333; padding: 10px; } "
                         "QMessageBox QPushButton { background-color: #f0f0f0; color: #333333; border: 1px solid #cccccc; border-radius: 4px; padding: 6px 12px; min-width: 60px; } "
                         "QMessageBox QPushButton:hover { background-color: #e0e0e0; } "
                         "QMessageBox QPushButton:pressed { background-color: #d0d0d0; } ");

    if (dialog.exec() == QDialog::Accepted) {
        QStringList selectedFiles = dialog.selectedFiles();
        if (!selectedFiles.isEmpty()) {
            QString directory = selectedFiles.first();
            // 目录更改后，重新检查存储空间
            spaceCheckTimer();
        }
    }
}


//录像设置
void RecordingSet::recordingSettings()
{

        MainWindow* m_mainWindow = qobject_cast<MainWindow*>(parent());

        // 从MainWindow获取当前通道号
        int currentChannel = m_mainWindow->get_curchannel();
        // 检查通道号有效性
        if (currentChannel < 0 || currentChannel >= 4) {
            qWarning("Invalid channel number: %d", currentChannel);
            return;
        }

        // 保存设置到对应通道的RecordingSettings
        QString inputPath = "/mnt/nvme";

        // 同步更新所有通道的基础路径（简化路径配置）
        for (int i = 0; i < 4; ++i) {
            m_mainWindow->RecordingSettings[i].basePath = inputPath;
            // 为了向后兼容，也保存到path字段
            m_mainWindow->RecordingSettings[i].path = inputPath;
        }

        // 同步更新全局基础路径
        m_mainWindow->basePath = inputPath;

        // 同步更新文件管理的路径
        m_mainWindow->Picturepath = inputPath.endsWith("/") ? inputPath : inputPath + "/";

        // 保存当前通道的其他设置
        m_mainWindow->RecordingSettings[currentChannel].name = ui->textEdit_prefix->toPlainText();

        printf("路径同步更新: 所有通道和文件管理路径已更新为 %s\n", inputPath.toUtf8().constData());

        // 分段录像设置
        m_mainWindow->RecordingSettings[currentChannel].segmentedVideo = ui->checkBox_segrecord->isChecked();
        m_mainWindow->RecordingSettings[currentChannel].recordingByTime = ui->radioButton_time->isChecked();
        m_mainWindow->RecordingSettings[currentChannel].byFileSize = ui->radioButton_byte->isChecked();

        // 分段参数 - 添加限制检查
        int segmentedTime = ui->spinBox_time->value();
        int segmentedSize = ui->spinBox_byte->value();

        // 限制时间分段最大值为120分钟
        if (segmentedTime > 120) {
            segmentedTime = 120;
            ui->spinBox_time->setValue(120);  // 同时更新UI显示
        }

        // 限制大小分段最大值为2048MB
        if (segmentedSize > 2048) {
            segmentedSize = 2048;
            ui->spinBox_byte->setValue(2048);  // 同时更新UI显示
        }

        m_mainWindow->RecordingSettings[currentChannel].segmentedTime = segmentedTime;
        m_mainWindow->RecordingSettings[currentChannel].segmentedSize = segmentedSize;

        // 录像画质设置
        if (ui->radioButton_high->isChecked()) {
            m_mainWindow->RecordingSettings[currentChannel].recordingQuality = 0; // 高质量
        } else if (ui->radioButton_middle->isChecked()) {
            m_mainWindow->RecordingSettings[currentChannel].recordingQuality = 1; // 中质量
        } else if (ui->radioButton_low->isChecked()) {
            m_mainWindow->RecordingSettings[currentChannel].recordingQuality = 2; // 低质量
        }

        // 存储限制设置
        m_mainWindow->RecordingSettings[currentChannel].videoStorage = ui->checkBox_storagelimit->isChecked();
        m_mainWindow->RecordingSettings[currentChannel].storageSize = ui->spinBox_storagelimit->value();


        m_mainWindow->RecordingSettings[currentChannel].isinit = "open";

        m_mainWindow->saveChannelSettingsToIni(m_mainWindow->channelSettings,m_mainWindow->iniPath);

        // 关闭对话框
        accept();
}

//是否分段录像
void RecordingSet::Sectional_recording()
{
    // 启用或禁用相关控件
    if (ui->checkBox_segrecord->isChecked())
    {
        ui->radioButton_time->setEnabled(true); // 如果复选框被勾选，允许选择
        ui->radioButton_byte->setEnabled(true);
        // 恢复启用状态的样式
        ui->radioButton_time->setStyleSheet("color: rgb(255, 255, 255);");
        ui->radioButton_byte->setStyleSheet("color: rgb(255, 255, 255);");
        time_segment();
        byte_segment();
    }
    else
    {
        ui->radioButton_time->setEnabled(false); // 如果复选框未勾选，不允许选择
        ui->radioButton_byte->setEnabled(false);
        // 设置禁用状态的样式 - 使用灰色显示
        ui->radioButton_time->setStyleSheet("color: rgb(128, 128, 128);");
        ui->radioButton_byte->setStyleSheet("color: rgb(128, 128, 128);");
        // 当checkBox_segrecord取消选中时，同时禁用spinBox控件
        ui->spinBox_time->setEnabled(false);
        ui->spinBox_byte->setEnabled(false);
        // 取消选中时，将radioButton也设为未选中状态
        ui->radioButton_time->setChecked(false);
        ui->radioButton_byte->setChecked(false);
    }
}
//按时间分段
void RecordingSet::time_segment()
{
    if(ui->radioButton_time->isChecked())
    {
        ui->spinBox_time->setEnabled(true);
        if(ui->spinBox_time->value() <=0)
            ui->spinBox_time->setValue(1);
        // 限制时间分段最大值为120分钟
        else if(ui->spinBox_time->value() > 120)
            ui->spinBox_time->setValue(120);
        // 当选择按时间分段时，禁用按字节分段的spinBox
        ui->spinBox_byte->setEnabled(false);
        ui->spinBox_byte->setValue(0);
    }
    else
    {
        ui->spinBox_time->setEnabled(false);
        ui->spinBox_time->setValue(0);
    }
}
//按字节分段
void RecordingSet::byte_segment()
{
    if(ui->radioButton_byte->isChecked())
    {
        ui->spinBox_byte->setEnabled(true);
        if(ui->spinBox_byte->value() <=0)
            ui->spinBox_byte->setValue(1024);
        // 限制大小分段最大值为2048MB
        else if(ui->spinBox_byte->value() > 2048)
            ui->spinBox_byte->setValue(2048);
        // 当选择按字节分段时，禁用按时间分段的spinBox
        ui->spinBox_time->setEnabled(false);
        ui->spinBox_time->setValue(0);
    }
    else
    {
        ui->spinBox_byte->setEnabled(false);
        ui->spinBox_byte->setValue(0);
    }
}
//录像存储限制
void RecordingSet::video_storage()
{
    if(ui->checkBox_storagelimit->isChecked())
    {
        ui->spinBox_storagelimit->setEnabled(true);
    }
    else
    {
        ui->spinBox_storagelimit->setEnabled(false);
    }
}


