[{"codec": "UTF-8", "excluded": [], "includePaths": ["/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "/home/<USER>/Qt/5.15.2/gcc_64/include", "/home/<USER>/Qt/5.15.2/gcc_64/include/QtWidgets", "/home/<USER>/Qt/5.15.2/gcc_64/include/QtGui", "/home/<USER>/Qt/5.15.2/gcc_64/include/QtCore", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS"], "projectFile": "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS.pro", "sources": ["/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/CustomFileSystemModel.cpp", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/CustomFileSystemModel.h", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/CustomFilterProxyModel.cpp", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/CustomFilterProxyModel.h", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/FileManage.cpp", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/FileManage.h", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/SysComboBoxStyle.h", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/aboutus.cpp", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/aboutus.h", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/aboutus.ui", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/camera_param.cpp", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/camera_param.h", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/camerastream.cpp", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/camerastream.h", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/filemanage.ui", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/main.cpp", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/mainwindow.cpp", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/mainwindow.h", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/mainwindow.ui", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/recordingset.cpp", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/recordingset.h", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/recordingset.ui", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/systemmanager.cpp", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/systemmanager.h", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/systemset.cpp", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/systemset.h", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/systemset.ui", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/udevmonitor.cpp", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/udevmonitor.h", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/virtualkeyboard.cpp", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/virtualkeyboard.h"], "translations": ["/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_zh_CN.ts", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_ar_DZ.ts", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_de_AT.ts", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_en_AS.ts", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_es_AR.ts", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_fr_DZ.ts", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_it_IT.ts", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_ja_JP.ts", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_ko_CN.ts", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_pt_AO.ts", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_ru_BY.ts", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_tr_CY.ts", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_zh_HK.ts", "/home/<USER>/Qt/project/RWS-project/RWS-7.24/RWS/RWS_zh_CN.ts"]}]