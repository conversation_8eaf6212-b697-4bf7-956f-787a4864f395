#include "udevmonitor.h"
#include <libudev.h>
#include <unistd.h>
#include <QDebug>

UdevMonitor::UdevMonitor(QObject *parent) : QObject(parent) {
    m_udev = udev_new();
    if (!m_udev) {
        qWarning("Failed to create udev context");
        return;
    }

    m_monitor = udev_monitor_new_from_netlink(m_udev, "udev");
    if (!m_monitor) {
        qWarning("Failed to create udev monitor");
        return;
    }

    // 只监控输入设备（鼠标、键盘等）
    udev_monitor_filter_add_match_subsystem_devtype(m_monitor, "input", nullptr);
    udev_monitor_enable_receiving(m_monitor);

    int fd = udev_monitor_get_fd(m_monitor);
    m_notifier = new QSocketNotifier(fd, QSocketNotifier::Read, this);
    connect(m_notifier, &QSocketNotifier::activated, this, &UdevMonitor::handleUdevEvent);
}

UdevMonitor::~UdevMonitor() {
    if (m_monitor) udev_monitor_unref(m_monitor);
    if (m_udev) udev_unref(m_udev);
}

void UdevMonitor::handleUdevEvent() {
    udev_device *dev = udev_monitor_receive_device(m_monitor);
    if (!dev) return;

    const char *action = udev_device_get_action(dev);
    const char *devpath = udev_device_get_devpath(dev);
    const char *subsystem = udev_device_get_subsystem(dev);

    if (action && devpath && subsystem && strcmp(subsystem, "input") == 0) {
        qDebug() << "Input device event:" << action << devpath;
        emit deviceChanged(); // 通知 MainWindow 检查鼠标状态
    }

    udev_device_unref(dev);
}
