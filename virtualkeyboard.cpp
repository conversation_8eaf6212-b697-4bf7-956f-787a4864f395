#include "virtualkeyboard.h"
#include <QKeyEvent>
#include <QApplication>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QScreen>
#include <mainwindow.h>
extern int keybod;
VirtualKeyboard::VirtualKeyboard(QWidget *parent)
    : QWidget(parent), m_attachedLineEdit(nullptr), m_shiftActive(false), m_dragging(false)
{
    setWindowFlags(Qt::Tool | Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);
    setAttribute(Qt::WA_TranslucentBackground);
    setFixedSize(580, 220);

    setStyleSheet(R"(
    VirtualKeyboard {
        background-color: rgba(240, 240, 240, 240);
        border-radius: 8px;
        border: 1px solid #aaa;
    }
    VirtualKeyboard > QWidget {
        background-color: rgba(240, 240, 240, 240);
    }
    QPushButton {
        background-color: #f8f8f8;
        border: 1px solid #d0d0d0;
        border-radius: 4px;
        font-size: 13px;
        min-width: 30px;
        min-height: 30px;
        padding: 2px;
    }
    QPushButton:pressed {
        background-color: #d0d0d0;
        border: 1px solid #a0a0a0;
        padding-top: 3px;
        padding-left: 3px;
        color: #000;
    }
    QPushButton#special {
        background-color: #e8e8e8;
        font-weight: bold;
    }
    QPushButton#special:pressed {
        background-color: #c8c8c8;
    }
    #titleBar {
        background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e0e0e0, stop:1 #d0d0d0);
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        padding: 0;
    }
    #titleLabel {
        font-weight: bold;
        color: #333;
        font-size: 11px;
        margin: 0;
        padding: 0;
    }
    #closeButton {
        background-color: #ff6b6b;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 0px 6px;
        font-weight: bold;
        min-width: 16px;
        min-height: 16px;
        font-size: 12px;
    }
    #closeButton:hover {
        background-color: #ff5252;
    }
    #closeButton:pressed {
        background-color: #e04141;
    }
    )");

    m_signalMapper = new QSignalMapper(this);
    connect(m_signalMapper, SIGNAL(mappedString(QString)), this, SLOT(handleKeyPress(QString)));

    createKeyboard();
}

void VirtualKeyboard::attachTo(QLineEdit *lineEdit)
{
    m_attachedLineEdit = lineEdit;
}

void VirtualKeyboard::showAtOptimalPosition(QWidget *targetWidget)
{
    if (!targetWidget) return;

    // 获取目标控件的全局位置和大小
    QPoint targetGlobalPos = targetWidget->mapToGlobal(QPoint(0, 0));
    QSize targetSize = targetWidget->size();
    QSize keyboardSize = this->size();

    // 获取屏幕可用区域
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->availableGeometry();

    QPoint optimalPos;

    // 1. 优先尝试右侧显示
    optimalPos = QPoint(targetGlobalPos.x() + targetSize.width() + 10, targetGlobalPos.y());
    if (optimalPos.x() + keyboardSize.width() <= screenGeometry.right()) {
        // 检查垂直位置是否需要调整
        if (optimalPos.y() + keyboardSize.height() > screenGeometry.bottom()) {
            optimalPos.setY(screenGeometry.bottom() - keyboardSize.height());
        }
        if (optimalPos.y() < screenGeometry.top()) {
            optimalPos.setY(screenGeometry.top());
        }
        move(optimalPos);
        show();
        return;
    }

    // 2. 尝试左侧显示
    optimalPos = QPoint(targetGlobalPos.x() - keyboardSize.width() - 10, targetGlobalPos.y());
    if (optimalPos.x() >= screenGeometry.left()) {
        // 检查垂直位置是否需要调整
        if (optimalPos.y() + keyboardSize.height() > screenGeometry.bottom()) {
            optimalPos.setY(screenGeometry.bottom() - keyboardSize.height());
        }
        if (optimalPos.y() < screenGeometry.top()) {
            optimalPos.setY(screenGeometry.top());
        }
        move(optimalPos);
        show();
        return;
    }

    // 3. 尝试下方显示
    optimalPos = QPoint(targetGlobalPos.x(), targetGlobalPos.y() + targetSize.height() + 10);
    if (optimalPos.y() + keyboardSize.height() <= screenGeometry.bottom()) {
        // 检查水平位置是否需要调整
        if (optimalPos.x() + keyboardSize.width() > screenGeometry.right()) {
            optimalPos.setX(screenGeometry.right() - keyboardSize.width());
        }
        if (optimalPos.x() < screenGeometry.left()) {
            optimalPos.setX(screenGeometry.left());
        }
        move(optimalPos);
        show();
        return;
    }

    // 4. 最后尝试上方显示
    optimalPos = QPoint(targetGlobalPos.x(), targetGlobalPos.y() - keyboardSize.height() - 10);
    if (optimalPos.y() >= screenGeometry.top()) {
        // 检查水平位置是否需要调整
        if (optimalPos.x() + keyboardSize.width() > screenGeometry.right()) {
            optimalPos.setX(screenGeometry.right() - keyboardSize.width());
        }
        if (optimalPos.x() < screenGeometry.left()) {
            optimalPos.setX(screenGeometry.left());
        }
        move(optimalPos);
        show();
        return;
    }

    // 5. 如果所有位置都不合适，则居中显示
    optimalPos = QPoint(
        screenGeometry.center().x() - keyboardSize.width() / 2,
        screenGeometry.center().y() - keyboardSize.height() / 2
    );
    move(optimalPos);
    show();
}

void VirtualKeyboard::createKeyboard()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(0);
    mainLayout->setContentsMargins(1, 1, 1, 1);

    // 标题栏
    QWidget *titleBar = new QWidget(this);
    titleBar->setObjectName("titleBar");
    titleBar->setFixedHeight(24);

    QHBoxLayout *titleLayout = new QHBoxLayout(titleBar);
    titleLayout->setContentsMargins(8, 2, 8, 2);
    titleLayout->setSpacing(5);

    QLabel *titleLabel = new QLabel("Virtual Keyboard", titleBar);
    titleLabel->setObjectName("titleLabel");
    titleLabel->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
    titleLabel->setFixedHeight(20);

    QPushButton *closeButton = new QPushButton("×", titleBar);
    closeButton->setObjectName("closeButton");
    closeButton->setFixedSize(18, 18);
    connect(closeButton, &QPushButton::clicked, this, &VirtualKeyboard::handleClose);

    titleLayout->addWidget(titleLabel);
    titleLayout->addStretch();
    titleLayout->addWidget(closeButton);

    mainLayout->addWidget(titleBar);

    // 键盘主体
    QWidget *keyboardWidget = new QWidget(this);
    keyboardWidget->setStyleSheet("background-color: rgba(240, 240, 240, 240);");
    QGridLayout *m_layout = new QGridLayout(keyboardWidget);
    m_layout->setSpacing(4);
    m_layout->setContentsMargins(8, 8, 8, 8);

    // 按键定义
    QStringList rows[4] = {
        {"1", "2", "3", "4", "5", "6", "7", "8", "9", "0", "-", "="},
        {"q", "w", "e", "r", "t", "y", "u", "i", "o", "p", "[", "]"},
        {"a", "s", "d", "f", "g", "h", "j", "k", "l", ";", "'", "\\"},
        {"z", "x", "c", "v", "b", "n", "m", ",", ".", "/"}
    };

    // 添加字母和数字键
    for (int row = 0; row < 4; ++row) {
        for (int col = 0; col < rows[row].size(); ++col) {
            QPushButton *button = new QPushButton(rows[row][col]);
            button->setSizePolicy(QSizePolicy::MinimumExpanding, QSizePolicy::MinimumExpanding);
            connect(button, SIGNAL(clicked()), m_signalMapper, SLOT(map()));
            m_signalMapper->setMapping(button, rows[row][col]);
            m_layout->addWidget(button, row, col);
            m_letterButtons.append(button);

            // 为按钮添加动画效果
            addButtonAnimation(button);
        }
    }

    // 功能键
    QPushButton *shiftButton = new QPushButton("Shift");
    shiftButton->setObjectName("special");
    shiftButton->setSizePolicy(QSizePolicy::MinimumExpanding, QSizePolicy::MinimumExpanding);
    connect(shiftButton, &QPushButton::clicked, this, &VirtualKeyboard::handleShift);
    m_layout->addWidget(shiftButton, 4, 0, 1, 2);
    addButtonAnimation(shiftButton);

    QPushButton *spaceButton = new QPushButton("Space");
    spaceButton->setObjectName("special");
    spaceButton->setSizePolicy(QSizePolicy::MinimumExpanding, QSizePolicy::MinimumExpanding);
    connect(spaceButton, &QPushButton::clicked, this, &VirtualKeyboard::handleSpace);
    m_layout->addWidget(spaceButton, 4, 2, 1, 6);
    addButtonAnimation(spaceButton);

    QPushButton *backspaceButton = new QPushButton("Back");
    backspaceButton->setObjectName("special");
    backspaceButton->setSizePolicy(QSizePolicy::MinimumExpanding, QSizePolicy::MinimumExpanding);
    connect(backspaceButton, &QPushButton::clicked, this, &VirtualKeyboard::handleBackspace);
    m_layout->addWidget(backspaceButton, 4, 8, 1, 2);
    addButtonAnimation(backspaceButton);

    QPushButton *enterButton = new QPushButton("Enter");
    enterButton->setObjectName("special");
    enterButton->setSizePolicy(QSizePolicy::MinimumExpanding, QSizePolicy::MinimumExpanding);
    connect(enterButton, &QPushButton::clicked, this, &VirtualKeyboard::handleEnter);
    m_layout->addWidget(enterButton, 4, 10, 1, 2);
    addButtonAnimation(enterButton);

    mainLayout->addWidget(keyboardWidget);
}

void VirtualKeyboard::addButtonAnimation(QPushButton *button)
{
    QGraphicsOpacityEffect *effect = new QGraphicsOpacityEffect(button);
    button->setGraphicsEffect(effect);

    QPropertyAnimation *anim = new QPropertyAnimation(effect, "opacity");
    anim->setDuration(100);
    anim->setStartValue(1.0);
    anim->setEndValue(0.7);
    anim->setEasingCurve(QEasingCurve::OutQuad);

    QObject::connect(button, &QPushButton::pressed, [anim]() {
        anim->setDirection(QAbstractAnimation::Forward);
        anim->start();
    });

    QObject::connect(button, &QPushButton::released, [anim]() {
        anim->setDirection(QAbstractAnimation::Backward);
        anim->start();
    });
}

void VirtualKeyboard::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragging = true;
        m_dragPosition = event->globalPos() - frameGeometry().topLeft();
        event->accept();
    }
}

void VirtualKeyboard::mouseMoveEvent(QMouseEvent *event)
{
    if (m_dragging && (event->buttons() & Qt::LeftButton)) {
        move(event->globalPos() - m_dragPosition);
        event->accept();
    }
}

void VirtualKeyboard::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragging = false;
        event->accept();
    }
}

void VirtualKeyboard::handleKeyPress(const QString &text)
{
    if (m_attachedLineEdit) {
        QString finalText = text;
        // 如果是字母，才根据 Shift 状态转换大小写
        if (text.length() == 1 && text.at(0).isLetter()) {
            finalText = m_shiftActive ? text.toUpper() : text.toLower();
        }
        m_attachedLineEdit->insert(finalText);
    }
    emit keyPressed(text);
}

void VirtualKeyboard::handleBackspace()
{
    if (m_attachedLineEdit) {
        m_attachedLineEdit->backspace();
    }
}

void VirtualKeyboard::handleSpace()
{
    if (m_attachedLineEdit) {
        m_attachedLineEdit->insert(" ");
    }
}

void VirtualKeyboard::handleEnter()
{
    if (m_attachedLineEdit) {
        QKeyEvent *pressEvent = new QKeyEvent(QEvent::KeyPress, Qt::Key_Return, Qt::NoModifier);
        QApplication::postEvent(m_attachedLineEdit, pressEvent);

        QKeyEvent *releaseEvent = new QKeyEvent(QEvent::KeyRelease, Qt::Key_Return, Qt::NoModifier);
        QApplication::postEvent(m_attachedLineEdit, releaseEvent);
    }
    if(keybod ==0)
    {
        MainWindow* mainWindow = qobject_cast<MainWindow*>(parent()); // 获取父窗口指针
        mainWindow->startSystemDoubleClickMonitor();
    }
    this->hide();
}

void VirtualKeyboard::handleShift()
{
    toggleShift();
}

void VirtualKeyboard::handleClose()
{
    if(keybod ==0)
    {
        MainWindow* mainWindow = qobject_cast<MainWindow*>(parent()); // 获取父窗口指针
        mainWindow->startSystemDoubleClickMonitor();
    }

    this->hide();
}

void VirtualKeyboard::toggleShift()
{
    m_shiftActive = !m_shiftActive;
    foreach (QPushButton *button, m_letterButtons) {
        QString text = button->text();
        button->setText(m_shiftActive ? text.toUpper() : text.toLower());
    }
}
