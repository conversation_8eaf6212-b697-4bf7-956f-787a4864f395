#include "CustomDialog.h"
#include <QApplication>

CustomDialog::CustomDialog(QWidget *parent)
    : QDialog(parent)
    , m_mainLayout(nullptr)
    , m_titleLayout(nullptr)
    , m_titleLabel(nullptr)
    , m_closeButton(nullptr)
    , m_contentWidget(nullptr)
    , m_dragging(false)
{
    setupUI();
}

CustomDialog::~CustomDialog()
{
}

void CustomDialog::setupUI()
{
    // 移除窗口边框和标题栏
    setWindowFlags(Qt::FramelessWindowHint | Qt::Dialog);
    setAttribute(Qt::WA_TranslucentBackground);
    
    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);
    
    // 创建标题栏布局
    m_titleLayout = new QHBoxLayout();
    m_titleLayout->setContentsMargins(10, 5, 5, 5);
    m_titleLayout->setSpacing(0);
    
    // 创建标题标签
    m_titleLabel = new QLabel("对话框", this);
    m_titleLabel->setStyleSheet(
        "QLabel {"
        "    color: white;"
        "    font-weight: bold;"
        "    font-size: 14px;"
        "    background: transparent;"
        "}"
    );
    
    // 创建关闭按钮
    m_closeButton = new QPushButton("×", this);
    m_closeButton->setFixedSize(30, 25);
    m_closeButton->setStyleSheet(
        "QPushButton {"
        "    background-color: transparent;"
        "    border: none;"
        "    color: white;"
        "    font-size: 18px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: #e81123;"
        "    color: white;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #f1707a;"
        "}"
    );
    
    // 连接关闭按钮信号
    connect(m_closeButton, &QPushButton::clicked, this, &CustomDialog::onCloseButtonClicked);
    
    // 添加到标题栏布局
    m_titleLayout->addWidget(m_titleLabel);
    m_titleLayout->addStretch();
    m_titleLayout->addWidget(m_closeButton);
    
    // 创建标题栏容器
    QWidget *titleWidget = new QWidget(this);
    titleWidget->setLayout(m_titleLayout);
    titleWidget->setFixedHeight(35);
    titleWidget->setStyleSheet(
        "QWidget {"
        "    background-color: #0078d4;"
        "    border-top-left-radius: 8px;"
        "    border-top-right-radius: 8px;"
        "}"
    );
    
    // 添加到主布局
    m_mainLayout->addWidget(titleWidget);
    
    // 设置默认样式
    setStyleSheet(
        "CustomDialog {"
        "    background-color: white;"
        "    border: 1px solid #cccccc;"
        "    border-radius: 8px;"
        "}"
    );
}

void CustomDialog::setDialogTitle(const QString &title)
{
    if (m_titleLabel) {
        m_titleLabel->setText(title);
    }
}

void CustomDialog::setDialogContent(QWidget *content)
{
    if (m_contentWidget) {
        m_mainLayout->removeWidget(m_contentWidget);
        m_contentWidget->deleteLater();
    }
    
    m_contentWidget = content;
    if (m_contentWidget) {
        m_contentWidget->setParent(this);
        m_contentWidget->setStyleSheet(
            "QWidget {"
            "    background-color: white;"
            "    border-bottom-left-radius: 8px;"
            "    border-bottom-right-radius: 8px;"
            "}"
        );
        m_mainLayout->addWidget(m_contentWidget);
    }
}

void CustomDialog::setDialogSize(int width, int height)
{
    setFixedSize(width, height);
}

void CustomDialog::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // 绘制阴影效果
    painter.setPen(Qt::NoPen);
    painter.setBrush(QColor(0, 0, 0, 50));
    painter.drawRoundedRect(rect().adjusted(2, 2, 0, 0), 8, 8);
    
    // 绘制主体
    painter.setBrush(Qt::white);
    painter.setPen(QPen(QColor(204, 204, 204), 1));
    painter.drawRoundedRect(rect().adjusted(0, 0, -2, -2), 8, 8);
}

void CustomDialog::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        // 检查是否点击在标题栏区域
        if (event->y() <= 35) {
            m_dragging = true;
            m_dragPosition = event->globalPos() - frameGeometry().topLeft();
            event->accept();
        }
    }
    QDialog::mousePressEvent(event);
}

void CustomDialog::mouseMoveEvent(QMouseEvent *event)
{
    if (event->buttons() & Qt::LeftButton && m_dragging) {
        move(event->globalPos() - m_dragPosition);
        event->accept();
    }
    QDialog::mouseMoveEvent(event);
}

void CustomDialog::onCloseButtonClicked()
{
    reject();
}
