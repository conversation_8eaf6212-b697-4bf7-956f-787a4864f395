#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QMouseEvent>
#include <QSet>
#include <QPair>
#include <QSlider>
#include <QSpinBox>
#include "camera_param.h"
#include "camerastream.h"
#include "recordingset.h"
#include "FileManage.h"
#include "systemset.h"
#include "systemmanager.h"
#include <thread>
#include <QSocketNotifier>  // 添加这行
#include <linux/input.h>    // 添加这行
#include <unistd.h>         // 添加这行
#include <fcntl.h>          // 添加这行

#include <QElapsedTimer>
#include <QPointer>

#include <QTranslator>
#include "virtualkeyboard.h"
#include "udevmonitor.h"
#include "CustomMessageBox.h"
#include "aboutus.h"


QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
        // bool eventFilter(QObject *obj, QEvent *event);
    QString iniPath = "/data/camera_settings.ini";

    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

    QElapsedTimer m_elapsedTimer;
    void updatetimelabel();
    void updateCpuUsage();
    void openvideodevice(int channel);

    void clearWhichVideo(int channel);//清理gstreamer资源

    int get_curchannel(){return curchannel;}

    // 添加两个控制UI元素启用/禁用的方法
    void enableCameraControls();  // 启用所有相机控制
    void disableCameraControls(); // 禁用所有相机控制
    void enableCloseButton();
    void disableCloseButton();

    // 创建必要的文件夹结构
    void createDirectoryStructure();

private:
    //UI相关
    void initUI();
    UdevMonitor *m_udevMonitor;//鼠标 拔除检测

    VirtualKeyboard *m_virtualKeyboard;
    void setupSpinBoxKeyboard(QSpinBox* spinBox);
    void showKeyboardForSpinBox(QSpinBox* spinBox);

    //检查录像设置
    bool checkRecordingLimits(int channel);
    int calculateRecordingLoad(int channel);// 计算当前录像负载

private:
    int currentLoad=0; //录像总负载
    QPoint m_dragPosition;
    bool m_isDragging = false;
    bool m_isPotentialDrag = false;
    QTimer m_longPressTimer;
    QWidget* m_dragWidget; // 指向你的可拖动区域

    // 新增：鼠标位置检查和按钮显示控制相关成员变量
    QTimer* m_mouseCheckTimer = nullptr;    // 检查鼠标位置的定时器
    QTimer* m_buttonHideTimer = nullptr;    // 控制按钮隐藏的定时器
    bool m_mouseInVideoArea = false;        // 鼠标是否在视频区域内的标志
    //错误对话框
    QMap<int, QPointer<CustomMessageBox>> errorDialogs; // 每个通道独立的错误对话框

protected:
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    //void closeEvent(QCloseEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void showEvent(QShowEvent *event) override;
    //void changeEvent(QEvent *event) override;
    bool eventFilter(QObject *watched, QEvent *event) override;

public:
    Ui::MainWindow *ui;

    bool isFullscreen = false;
    int full=0;
    //音频相关
    QVector<bool> curaudiostate;  // 录音状态数组

    QVector<bool> curmotionstate; // 移动侦测状态数组

    QVector<bool> timewatermark;

    //按钮同步
    void syncButtonGroups(QButtonGroup *source, QButtonGroup *target);
    void updateDeviceLabels(int pageIndex);
    void updateCompositePageLabels(int leftIndex, int rightIndex, bool updateLeft, bool updateRight);
    void updateGridPageLabels(int tlIndex, int trIndex, int blIndex, int brIndex,
                              bool updateTL, bool updateTR, bool updateBL, bool updateBR);
    QWidget* setupDeviceInfoLabels(QWidget* parent, int pageIndex,int fontSize);

    int curchannel=0;//通道
    QWidget* videoContainer =nullptr;
    //void camerabind(int channel);//相机绑定录像位置

    //void UIinit();
    void deviceinit();
    void timerinit();

    // 保存当前选择的设备、格式和分辨率
    QString currentDevicePath = ""; //路径
    QVariantMap currentFormat;      //格式
    QVariantMap currentResolution;  //帧率

    // 存储每个通道的摄像头设置
    struct ChannelSettings {
        bool isOpen = false;          // 摄像头是否打开
        QString devicePath = "";      // 设备路径
        QVariantMap format;           // 格式
        QVariantMap resolution;       // 分辨率
        int videoIndex = -1;          // 视频设备索引
        int formatIndex = -1;         // 格式索引
        int resolutionIndex = -1;     // 分辨率索引
        QString devicename = "";           //设备名
        QString FBLname="";           //分辨率名字
        QString UsbInterface = "";   // USB接口
        QString ProductVid = "";     // 设备VID

        // 音频设备相关字段
        QString audioDeviceName = "";     // 音频设备名称
        QString audioDevicePath = "";     // 音频设备路径
        int audioDeviceIndex = -1;        // 音频设备索引
        bool audioEnabled = false;        // 音频是否启用

        // 热插拔相关字段
        bool wasRunningBeforeUnplug = false;  // 拔掉前是否在运行
        QString unpluggedUsbInterface = "";   // 被拔掉的USB接口
        QString unpluggedProductVid = "";     // 被拔掉的设备VID
        QString unpluggedDeviceName = "";     // 被拔掉的设备名
        QVariantMap unpluggedFormat;          // 被拔掉时的格式
        QVariantMap unpluggedResolution;      // 被拔掉时的分辨率
        int unpluggedFormatIndex = -1;        // 被拔掉时的格式索引
        int unpluggedResolutionIndex = -1;    // 被拔掉时的分辨率索引
        QString unpluggedFBLname = "";        // 被拔掉时的分辨率名字

        // 热插拔音频设备相关字段
        QString unpluggedAudioDeviceName = "";  // 被拔掉时的音频设备名称
        QString unpluggedAudioDevicePath = "";  // 被拔掉时的音频设备路径
        int unpluggedAudioDeviceIndex = -1;     // 被拔掉时的音频设备索引
        bool unpluggedAudioEnabled = false;     // 被拔掉时音频是否启用
    };

    ChannelSettings channelSettings[4]; // 保存四个通道的设置
    bool isRecording[4] = {false,false,false,false};

    struct RecordingSettings {
        QString basePath;       // 基础路径（如 "/sdcard"）
        QString path;           // 录制文件保存路径（保留用于向后兼容）
        QString isinit;         //是否初始化
        QString name;           // 录制前缀文件名
        bool segmentedVideo;    // 是否分段录制视频
        bool recordingByTime;   //按时间分段录制视频
        int  segmentedTime;     // 分段录制时间间隔
        bool byFileSize;        // 按文件大小分段录制视频
        int  segmentedSize;     // 分段录制文件大小
        int  recordingQuality;  // 0：高质量 1：中质量  2：低质量
        bool videoStorage;      // 是否设置存储限制
        int  storageSize;       // 存储限制大小 GB
    };
    RecordingSettings RecordingSettings[4]= {
        {"/mnt/nvme", "/mnt/nvme/Recording/CH1", "", "", false, false, 0, false, 0, 0, false, 0},
        {"/mnt/nvme", "/mnt/nvme/Recording/CH2", "", "", false, false, 0, false, 0, 0, false, 0},
        {"/mnt/nvme", "/mnt/nvme/Recording/CH3", "", "", false, false, 0, false, 0, 0, false, 0},
        {"/mnt/nvme", "/mnt/nvme/Recording/CH4", "", "", false, false, 0, false, 0, 0, false, 0}
    };


    QString basePath = "/mnt/nvme";        // 统一的基础路径
    QString Picturepath = "/mnt/nvme/";    // 保留用于向后兼容

    CameraStream *cameraStream1;
    CameraStream *cameraStream2;
    CameraStream *cameraStream3;
    CameraStream *cameraStream4;

    //全屏wayland
    int mouseFd = -1;
    QSocketNotifier* mouseNotifier = nullptr;
    qint64 lastClickTime = 0;
    int clickCount = 0;
    const int doubleClickInterval = 250; // 双击间隔时间(毫秒)
    bool isSubWindowOpen = false;  // 新增标志位

    // 全屏模式背景相关 - 使用独立窗口
    QVector<QWidget*> fullscreenBgWindows; // 存储所有背景窗口
    void createFullscreenBackgrounds();
    void updateFullscreenBackgrounds();
    void clearFullscreenBackgrounds();

    bool isMouseDevice(const QString &devicePath);
    QPair<QString, QString> findRealMouseDevice();
    void startSystemDoubleClickMonitor();
    void setupMouseNotifier();
    void handleMouseEvent();
    void stopSystemDoubleClickMonitor();

    GstElement *pipeline;


    //重启程序的ini配置文件
    //写入配置
    QString extractOddVideoName(const QString &devicePath);
    QString findUsbInterfaceForVideo(const QString &videoName);
    QString getProductVidFromInterface(const QString &usbInterface);
    void saveChannelSettingsToIni(const ChannelSettings (&settings)[4], const QString &iniPath);
    //读取配置
    void loadSettingsFromIni(ChannelSettings (&settings)[4], const QString &iniPath);
    QString findMatchingVideoDevice(const QString &targetInterface, const QString &targetVid);

    void reopendevice();

    // 热插拔相关函数
    void saveHotplugDataToIni(int channel, const QString &iniPath);
    bool loadHotplugDataFromIni(int channel, const QString &iniPath);
    void clearHotplugDataFromIni(int channel, const QString &iniPath);

    // 新增：控制视频控制按钮显示/隐藏的相关函数
    void initControlButtonsVisibility();
    void updateControlButtonsVisibility();
    bool isCameraOpen() const;

    //语言切换
    int currentLanguageIndex = 0;
    SystemManager *systemmanager;
    QTranslator translator;
    void retranslateUi();

private:
    struct WidgetProperties {
        QSizePolicy sizePolicy;
        QSize minimumSize;
        QSize maximumSize;
    };

    QMap<QString, WidgetProperties> widgetPropertiesMap;
    QSizePolicy originalSizePolicy;
    QSize originalMinimumSize;
    QSize originalMaximumSize;

    void saveWidgetProperties(QWidget* widget, const QString& name);
    void restoreWidgetProperties(QWidget* widget, const QString& name);

    void updateAudioDevicesForCamera(const QString &usbInterface);
    void updateAudioCheckboxState();

private slots:
    void onCloseButtonClicked();
    //全屏
    void toggleVideoFullscreen();
    void toggleVideoFullscreen2();
    void toggleVideoFullscreen4();

    //右上角按钮
    void onMinimizeClicked();
    void onMaximizeClicked();
    void updateMaximizeButtonIcon();

    void checkForNewDevices();
    void select_video(int index);
    void select_format(int index);
    void select_resolution(int index);
    void select_audio(int index);
    //设置摄像头参数
    void select_brightness();
    void select_contrast();
    void select_saturation();
    void select_hue();
    void select_sharpness();
    void select_gamma();
    void select_backlightcompensation();
    void select_gain();
    void select_whitebalance();
    void select_whitebalance_auto();
    void select_exposure();
    void select_exposure_auto();
    void select_focus();
    void select_focus_auto();
    void select_zoom();
    void select_pan();
    void select_tilt();
    //设置默认值
    void set_default1();
    void set_default2();


    void VideoSetPage();
    void PictureSetPage();
    void CameraControlPage();
    void TextSetPage();

    //录像相关
    // void startRecording();
    // void stopRecording();
    // void choosestartrecord(int channel);
    // void choosestoprecord(int channel);

    //切换显示摄像头
    void Signals1();
    void Signals2();
    void Signals3();
    void Signals4();
    void Signals5();
    void Signals6();
    void Signals7();

    void videoset();
    void filemanage();
    void systemset();

    // 新增通道录像控制函数
    void startRecordingByChannel(int channel);
    void stopRecordingByChannel(int channel);

    // 新增：检查鼠标位置并控制按钮显示的槽函数
    void checkMousePositionAndUpdateButtons();
    void hideControlButtons();

    void rotateCamera(int cameraId);
    void takePhoto(int cameraId);

    //是否录音
    void isRecordAudio(bool reaudio);

    void aboutus();

private:

    // 摄像头参数管理对象
    CameraParams *cameraParams;

    // 摄像头流对象（替代原来的GStreamer代码）


    //获取设备名
    QString curdevicename[4];
    QString curfblname[4];



    QWidget* videoWidget;  // 假设这是您的ui->video控件
    void parseResolution(const QString& resStr, int& width, int& height);
    QRect calculateAspectRatioRect(int srcWidth, int srcHeight, const QRect& targetArea);

    QStringList prevCpuTimes;

    void selectPreferredFormat();


    void initControl();
    void resetcontrol();
    void updateCameraParameter(QObject* senderObj, QSlider* slider, QSpinBox* spinBox, int controlId);
    void setuiCameraParam(QSlider* slider, QSpinBox* spinBox,int &min, int &max, int &step, int &defaultValue);



    struct ButtonInfo {
        QString text;
        QIcon image;
        QString iconName; // 添加图标名字字段
    };
    struct RecordingButtonData {
        QPushButton* button;
        QIcon defaultIcon;
        bool isRecording = false;
        QLabel* timeLabel = nullptr;   // 新增：时间显示标签
        QTimer* recordingTimer = nullptr; // 新增：计时器
        int elapsedTime = 0;           // 新增：已记录时间(秒)

        RecordingButtonData(QPushButton* btn, const QIcon& icon)
            : button(btn), defaultIcon(icon) {}
    };
    QVector<QVector<QWidget*>> pageBackgrounds; // 7个页面，每个页面有1-4个背景控件

    QMap<int, QVector<ButtonInfo>> pagesButtons;
    QVector<RecordingButtonData*> camera1Buttons;
    QVector<RecordingButtonData*> camera2Buttons;
    QVector<RecordingButtonData*> camera3Buttons;
    QVector<RecordingButtonData*> camera4Buttons;


    void setupbutton();
    void setupSingleCameraPage(QWidget* page, int cameraIndex);
    void setupCompositePage(QWidget* page, int leftCamIndex, int rightCamIndex);
    void setupGridPage(QWidget* page, int tlIndex, int trIndex, int blIndex, int brIndex);
    QFrame* createSeparatorLine(Qt::Orientation orientation, int width);

    QWidget* setupDeviceInfoLabels(QWidget* parent, int pageIndex);


    QWidget* setupCameraButtons(QWidget* parent, const QVector<ButtonInfo>& buttonsInfo, int cameraId);

    // 添加鼠标悬停控制相关成员
    QMap<QWidget*, QPair<QWidget*, QWidget*>> hoverControls; // 视频区域 -> (标签容器, 按钮容器)




private:
    std::pair<int, bool> getCurrentMouseAreaState() const;

    //更新按钮和标签容器的可见性
    void updateSinglePageVisibility(QWidget* page, bool visible);

    void restoreSelection(); //恢复选择

    void initCameraBackgrounds();//添加背景
    void updateCameraBackgrounds();

    // 处理GStreamer错误的槽函数
    void handleGstreamerError(const QString &errorMessage, const QString &errorDetails, int channelId);

    // 在 MainWindow 类声明中添加以下内容

private:
    // 设备选择和状态结构体
    struct CurrentSelection {
        QString devicePath;
        QString format;
        QString deviceName;
        int deviceIndex;
        int formatIndex;
    };

    struct DeviceChanges {
        bool currentRemoved;
        bool anyRemoved;
        bool newAdded;
    };

    // 设备检测相关函数
    CurrentSelection saveCurrentSelection();
    QSet<QString> getCurrentDevicePaths();
    DeviceChanges checkDeviceChanges(const QSet<QString>& currentDevices,
                                     const QSet<QString>& newDevicePaths,
                                     const CurrentSelection& selection);

    // 设备移除处理函数
    void handleCurrentDeviceRemoved(int channel);
    void resetChannelSettings(int channel);
    void resetRecordingButtons(int channel);
    void checkAllChannelsForRemovedDevices(const QSet<QString>& newDevicePaths);
    void handleChannelCleanup(int channel, const QString& reason);

    // 热插拔恢复函数
    void handleHotplugRecovery(const QList<QPair<QString, QString>>& newDevices);
    void recoverHotplugDevice(int channel, const QString& devicePath, const QString& deviceName);
    void recoverCurrentChannel(int channel, const QString& devicePath, const QString& deviceName);
    void recoverBackgroundChannel(int channel, const QString& devicePath);

    // 预览管理函数
    void managePreviewStates(int channel);

    // UI更新函数
    void updateDeviceListUI(const CurrentSelection& selection, const QList<QPair<QString, QString>>& newDevices);




private slots:
    void toggleRecordingForCamera(int cameraId);
    void TimeWatermark();
    void onMouseDeviceChanged(); // 当 UdevMonitor 检测到设备变化时调用
};

#endif // MAINWINDOW_H
