#include "FileManage.h"
#include "ui_filemanage.h"
#include "mainwindow.h"
#include <QDesktopServices>
#include <QUrl>

#include <QMenu>
#include <QFileInfo>
#include <QInputDialog>
#include <QTimer>
#include <QHeaderView>
#include <QScreen>
#include <QProcess>

#include "camerastream.h"

FileManage::FileManage(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::FileManage)
    , m_fileSystemModel(nullptr) // 初始化为 nullptr，延迟初始化
    , m_currentFolderPath("") // 初始化当前文件夹路径为空
    , m_filterProxyModel(new CustomFilterProxyModel(this)) // 初始化过滤代理模型
    , m_progressDialog(nullptr)
    , m_progressBar(nullptr)
    , m_progressLabel(nullptr)
    , m_speedLabel(nullptr)
    , m_progressTimer(nullptr)
    , m_animationTimer(nullptr)
    , m_totalBytes(0)
    , m_targetPath("")
    , m_transferDurationMs(0)
    , m_realProgress(0)
    , m_animationOffset(0)
{
    ui->setupUi(this);

    setWindowFlags(Qt::FramelessWindowHint);
    show();
    // activateWindow();
    // raise(); // 确保窗口被提升到最顶层

    QLocale locale(QLocale::Chinese);
    ui->calendarWidget->setLocale(locale);

    ui->widget_4->setVisible(false);//隐藏日期选择

    // 设置 QListView 和 QTreeView 的初始状态
    ui->listView->setVisible(false); // 默认隐藏
    ui->treeView->setVisible(true);  // 默认显示

    ui->lineEdit_search->setVisible(false);//搜索栏隐藏

    // 初始化状态
    //m_currentFolder = "";
    QButtonGroup *group1 = new QButtonGroup(this);
    QButtonGroup *group2 = new QButtonGroup(this);

    group1->addButton(ui->picture, 0);
    group1->addButton(ui->video, 1);
    group1->addButton(ui->tfcard, 2);
    ui->video->setChecked(true);

    group2->addButton(ui->CH1, 0);
    group2->addButton(ui->CH2, 1);
    group2->addButton(ui->CH3, 2);
    group2->addButton(ui->CH4, 3);

    // 检查TF卡目录是否存在，决定是否启用TF卡按钮
    QString tfcardPath = "/mnt/sdcard";
    QDir tfcardDir(tfcardPath);
    if (tfcardDir.exists()) {
        qDebug() << "TF卡目录存在，启用TF卡按钮:" << tfcardPath;
        ui->tfcard->setEnabled(true);
    } else {
        qDebug() << "TF卡目录不存在，禁用TF卡按钮:" << tfcardPath;
        ui->tfcard->setEnabled(false);
        // 设置禁用状态的样式
        ui->tfcard->setStyleSheet(ui->tfcard->styleSheet() +
                                  "QPushButton:disabled { "
                                  "color: rgb(128, 128, 128); "
                                  "background-color: rgb(64, 64, 64); "
                                  "border: 1px solid rgb(128, 128, 128); "
                                  "}");
    }

    // 检查文件夹是否存在 - 使用basePath而不是Picturepath
    QString mcf = getBasePath();
    QString cameraPath = mcf + "/Picture";

    qDebug() << "初始化FileManage，基础路径:" << mcf;
    qDebug() << "检查Picture路径:" << cameraPath;

    // 始终初始化文件系统模型，即使文件夹不存在
    initializeFileSystemModel();

    if (QDir(cameraPath).exists()) {
        qDebug() << "Picture文件夹存在，点击video按钮";
        ui->video->click();
    }
    else {
        qDebug() << "Picture文件夹不存在:" << cameraPath << "，尝试创建";
        // 尝试创建Picture目录
        QDir baseDir(mcf);
        if (baseDir.exists() && baseDir.mkdir("Picture")) {
            qDebug() << "成功创建Picture目录";
            ui->video->click();
        } else {
            qDebug() << "无法创建Picture目录，使用默认显示";
        }
    }
    // 连接双击事件
    connect(ui->treeView, &QTreeView::doubleClicked, this, &FileManage::onFileDoubleClicked);
    connect(ui->listView, &QListView::doubleClicked, this, &FileManage::onFileDoubleClicked);

    // 设置 QTreeView 和 QListView 的右键菜单策略
    ui->treeView->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(ui->treeView, &QTreeView::customContextMenuRequested, this, &FileManage::showContextMenu);

    ui->listView->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(ui->listView, &QListView::customContextMenuRequested, this, &FileManage::showContextMenu);

    // 初始化虚拟键盘
    m_virtualKeyboard = new VirtualKeyboard(this);
    // 为搜索框设置键盘支持
    setupLineEditKeyboard(ui->lineEdit_search);
    // 连接搜索框文本变化信号到搜索函数
    connect(ui->lineEdit_search, &QLineEdit::textChanged, this, &FileManage::SearchFile);
}

FileManage::~FileManage()
{
    // 清理进度相关资源
    if (m_progressTimer) {
        m_progressTimer->stop();
        m_progressTimer->deleteLater();
    }

    if (m_animationTimer) {
        m_animationTimer->stop();
        m_animationTimer->deleteLater();
    }

    if (m_progressDialog) {
        m_progressDialog->close();
        m_progressDialog->deleteLater();
    }

    // m_speedLabel会随着m_progressDialog一起被清理，不需要单独处理

    // 安全释放键盘资源
    if (m_virtualKeyboard) {
        // 先停止键盘可能正在进行的操作
        m_virtualKeyboard->hide();
        m_virtualKeyboard->disconnect(); // 断开所有信号连接
        delete m_virtualKeyboard;
    }
    delete m_fileSystemModel; // 释放 QFileSystemModel
    delete m_filterProxyModel;
    delete ui;
}

void FileManage::setupLineEditKeyboard(QLineEdit *lineEdit)
{
    if (!lineEdit) {
        qWarning() << "Invalid QLineEdit pointer!";
        return;
    }

    lineEdit->installEventFilter(this);
    lineEdit->setFocusPolicy(Qt::StrongFocus);
}

bool FileManage::eventFilter(QObject *obj, QEvent *event)
{
    QLineEdit *lineEdit = qobject_cast<QLineEdit*>(obj);
    if (!lineEdit) {
        return QWidget::eventFilter(obj, event);
    }

    if (event->type() == QEvent::MouseButtonRelease) {
        lineEdit->setFocus(Qt::MouseFocusReason);

        // 连接虚拟键盘
        m_virtualKeyboard->attachTo(lineEdit);

        // 使用智能定位显示键盘
        m_virtualKeyboard->showAtOptimalPosition(lineEdit);

        return true;
    }

    return QWidget::eventFilter(obj, event);
}

void FileManage::SearchFile() {
    ui->lineEdit_search->setVisible(true);

    if (!m_fileSystemModel) return;

    QString searchKeyword = ui->lineEdit_search->text().trimmed();

    if (searchKeyword.isEmpty()) {
        // 完全重置过滤状态
        m_filterProxyModel->setFilterWildcard("");  // 清空通配符
        m_filterProxyModel->setFilterRegExp("");    // 清空正则
        m_filterProxyModel->setFilterFixedString("");// 清空固定字符串

        // 如果使用自定义过滤逻辑
        m_filterProxyModel->setUseCustomFilter(false);
        m_filterProxyModel->enableKeywordFilter(false);
    } else {
        // 正常搜索逻辑
        m_filterProxyModel->setUseCustomFilter(true);
        m_filterProxyModel->setFilterKeyword(searchKeyword);
        m_filterProxyModel->enableKeywordFilter(true);
    }

    // 刷新视图
    QModelIndex rootIndex = m_fileSystemModel->index(m_currentFolderPath);
    ui->treeView->setRootIndex(m_filterProxyModel->mapFromSource(rootIndex));
    ui->listView->setRootIndex(m_filterProxyModel->mapFromSource(rootIndex));
}


void FileManage::showContextMenu(const QPoint& pos) {
    QWidget* sender = qobject_cast<QWidget*>(QObject::sender());
    QMenu contextMenu(this);

    // 获取当前点击位置的索引
    QModelIndex index;
    if (sender == ui->treeView) {
        index = ui->treeView->indexAt(pos);
    }
    else if (sender == ui->listView) {
        index = ui->listView->indexAt(pos);
    }

    if (index.isValid()) {
        // 设置右键菜单样式
        contextMenu.setStyleSheet(
            "QMenu {"
            "    background-color: white;"
            "    border: 1px solid #cccccc;"
            "    border-radius: 4px;"
            "    padding: 4px;"
            "}"
            "QMenu::item {"
            "    background-color: white;"
            "    color: black;"
            "    padding: 6px 20px;"
            "    border-radius: 2px;"
            "}"
            "QMenu::item:selected {"
            "    background-color: #e3f2fd;"
            "    color: black;"
            "}"
            "QMenu::item:pressed {"
            "    background-color: #bbdefb;"
            "}"
            );

        // 获取文件路径和信息
        QModelIndex sourceIndex = m_filterProxyModel->mapToSource(index);
        QString filePath = m_fileSystemModel->filePath(sourceIndex);
        QFileInfo fileInfo(filePath);
        bool isDir = fileInfo.isDir();

        MainWindow* pParent = qobject_cast<MainWindow*>(parent()); // 获取父窗口（假设是 MainWindow）
        if (!pParent || !pParent->systemmanager) {
            qDebug() << "Error: Invalid parent or translation system not initialized!";
            return;
        }

        // 使用 lambda 简化翻译调用
        auto tr = [&](const QString& key, const QString& fallback = "") {
            QString result = pParent->systemmanager->getTranslatedText(key, pParent->currentLanguageIndex, "filemanage");
            return (result.startsWith("Error:") ? fallback : result); // 如果翻译失败，使用 fallback
        };

        // 添加“打开”菜单项（仅对文件有效）
        if (!isDir) {
            QAction* openAction = contextMenu.addAction(tr("open_action", "打开")); // "open_action" 是翻译键
            connect(openAction, &QAction::triggered, this, [this, index]() {
                onFileDoubleClicked(index);
            });
        }

        // 添加“删除”菜单项
        QAction* deleteAction = contextMenu.addAction(tr("delete_action", "删除")); // "delete_action" 是翻译键
        connect(deleteAction, &QAction::triggered, this, [this, index]() {
            onContextMenuDelete(index);
        });

        // 添加“属性”菜单项
        QAction* propertiesAction = contextMenu.addAction(tr("properties_action", "属性")); // "properties_action" 是翻译键
        connect(propertiesAction, &QAction::triggered, this, [this, fileInfo]() {
            onContextMenuProperties(fileInfo);
        });

        // 添加“传送”菜单项
        QAction* transferAction = contextMenu.addAction(tr("transfer_action", "传送")); // "properties_action" 是翻译键
        connect(transferAction, &QAction::triggered, this, [this, fileInfo]() {
            onContextMenuTransfer(fileInfo);
        });

        // // 添加“重命名”菜单项
        // QAction* renameAction = contextMenu.addAction("重命名");
        // connect(renameAction, &QAction::triggered, this, [this, index]() {
        //     onContextMenuRename(index);
        // });
    }

    // 显示菜单
    contextMenu.exec(sender->mapToGlobal(pos));
}

//打开
void FileManage::onContextOpen(const QModelIndex& index) {
    QModelIndex sourceIndex = m_filterProxyModel->mapToSource(index);
    QString filePath = m_fileSystemModel->filePath(sourceIndex);
    QDesktopServices::openUrl(QUrl::fromLocalFile(filePath));
}

// 检查文件是否正在被录像使用
bool FileManage::isFileBeingRecorded(const QString& filePath) {
    MainWindow* pParent = qobject_cast<MainWindow*>(parent());
    if (!pParent) {
        return false;
    }

    // 获取所有CameraStream对象
    CameraStream* cameraStreams[4] = {
        pParent->cameraStream1,
        pParent->cameraStream2,
        pParent->cameraStream3,
        pParent->cameraStream4
    };

    // 检查每个通道是否正在录像
    for (int i = 0; i < 4; i++) {
        if (pParent->isRecording[i] && cameraStreams[i]) {
            // 检查是否启用了分段录像
            bool isSegmented = pParent->RecordingSettings[i].segmentedVideo &&
                               (pParent->RecordingSettings[i].recordingByTime ||
                                pParent->RecordingSettings[i].byFileSize);

            if (isSegmented) {
                // 分段录像模式：直接比较当前分段文件名
                QString currentSegmentFile = cameraStreams[i]->currentSegmentFilename;
                if (!currentSegmentFile.isEmpty() && currentSegmentFile == filePath) {
                    return true;
                }
            } else {
                // 非分段录像模式：直接比较文件路径
                QString recordingFile = cameraStreams[i]->recordingfilename;
                if (!recordingFile.isEmpty() && recordingFile == filePath) {
                    return true;
                }
            }
        }
    }
    return false;
}

// 递归检查文件夹中是否有正在录像的文件
bool FileManage::hasRecordingFilesInDirectory(const QString& dirPath) {
    QDir dir(dirPath);
    if (!dir.exists()) {
        return false;
    }

    // 获取目录中的所有文件和子目录
    QFileInfoList entries = dir.entryInfoList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);

    for (const QFileInfo& entry : entries) {
        if (entry.isFile()) {
            // 检查文件是否正在被录像
            if (isFileBeingRecorded(entry.absoluteFilePath())) {
                return true;
            }
        } else if (entry.isDir()) {
            // 递归检查子目录
            if (hasRecordingFilesInDirectory(entry.absoluteFilePath())) {
                return true;
            }
        }
    }
    return false;
}
//删除
void FileManage::onContextMenuDelete(const QModelIndex& index) {
    QModelIndex sourceIndex = m_filterProxyModel->mapToSource(index);
    QString filePath = m_fileSystemModel->filePath(sourceIndex);
    QFileInfo fileInfo(filePath);

    MainWindow* pParent = qobject_cast<MainWindow*>(parent());

    qDebug()<<"12314564123213"<<pParent;
    // 检查是否正在录像
    if (fileInfo.isDir()) {
        // 如果是文件夹，递归检查文件夹中是否有正在录像的文件
        if (hasRecordingFilesInDirectory(filePath)) {
            CustomMessageBox::warning(this,
                                      pParent->systemmanager->getTranslatedText("delete_failed_title",pParent->currentLanguageIndex,"filemanage"),
                                      pParent->systemmanager->getTranslatedText("delete_folder_recording_error",pParent->currentLanguageIndex,"filemanage"));
            return;
        }
    } else {
        // 如果是文件，检查是否正在被录像
        if (isFileBeingRecorded(filePath)) {
            CustomMessageBox::warning(this,
                                      pParent->systemmanager->getTranslatedText("delete_failed_title",pParent->currentLanguageIndex,"filemanage"),
                                      pParent->systemmanager->getTranslatedText("delete_file_recording_error",pParent->currentLanguageIndex,"filemanage"));
            return;
        }
    }

    // 弹出确认对话框
    CustomMessageBox::StandardButton reply = CustomMessageBox::question(this,
                                                                        pParent->systemmanager->getTranslatedText("confirm_title",pParent->currentLanguageIndex,"filemanage"),
                                                                        pParent->systemmanager->getTranslatedText("confirm_delete",pParent->currentLanguageIndex,"filemanage"),
                                                                        CustomMessageBox::Yes | CustomMessageBox::No);

    if (reply == CustomMessageBox::Yes) {
        if (fileInfo.isDir()) {
            if (QDir(filePath).removeRecursively()) {
                qDebug() << "文件夹删除成功：" << filePath;
            }
        }
        else {
            if (QFile::remove(filePath)) {
                qDebug() << "文件删除成功：" << filePath;
            }

        }
    }
}

//属性相关
void FileManage::onContextMenuProperties(const QFileInfo& fileInfo) {
    MainWindow* pParent = qobject_cast<MainWindow*>(parent());
    QString message = "";
    message += QString("<b>%1</b>%2<br>")
                   .arg(pParent->systemmanager->getTranslatedText("file_name", pParent->currentLanguageIndex, "filemanage"))
                   .arg(fileInfo.fileName());
    message += QString("<b>%1</b>%2<br>")
                   .arg(pParent->systemmanager->getTranslatedText("path", pParent->currentLanguageIndex, "filemanage"))
                   .arg(fileInfo.absoluteFilePath());

    qDebug()<<"____________________________________________________________"<<pParent->systemmanager->getTranslatedText("path", pParent->currentLanguageIndex, "filemanage");
    if (fileInfo.isDir()) {
        // 计算文件夹大小
        qint64 folderSize = calculateFolderSize(fileInfo.absoluteFilePath());
        if (folderSize >= 0) {
            message += QString("<b>%1</b>%2<br>")
            .arg(pParent->systemmanager->getTranslatedText("file_size_label", pParent->currentLanguageIndex, "filemanage"))
                .arg(getReadableSize(folderSize));
        }
        else {
            message += QString("<b>%1</b>%2<br>")
            .arg(pParent->systemmanager->getTranslatedText("file_size_label", pParent->currentLanguageIndex, "filemanage"))
                .arg(pParent->systemmanager->getTranslatedText("cannot_calculate", pParent->currentLanguageIndex, "filemanage"));
        }
    }
    else {
        qint64 fileSize = fileInfo.size();
        message += QString("<b>%1</b>%2<br>")
                       .arg(pParent->systemmanager->getTranslatedText("file_size_label", pParent->currentLanguageIndex, "filemanage"))
                       .arg(getReadableSize(fileSize));
    }

    message += QString("<b>%1</b>%2<br>")
                   .arg(pParent->systemmanager->getTranslatedText("modified_date_label", pParent->currentLanguageIndex, "filemanage"))
                   .arg(fileInfo.lastModified().toString("yyyy-MM-dd hh:mm:ss"));
    message += QString("<b>%1</b>%2")
                   .arg(pParent->systemmanager->getTranslatedText("file_type_label", pParent->currentLanguageIndex, "filemanage"))
                   .arg(fileInfo.suffix());

    // 显示文件属性信息
    CustomMessageBox::information(this,
                                  pParent->systemmanager->getTranslatedText("file_properties_title",pParent->currentLanguageIndex,"filemanage"),
                                  message);
}

void FileManage::onContextMenuTransfer(const QFileInfo& fileInfo)
{
    // 获取父窗口以访问翻译系统
    MainWindow* pParent = qobject_cast<MainWindow*>(parent());
    if (!pParent || !pParent->systemmanager) {
        qDebug() << "Error: Invalid parent or translation system not initialized!";
        return;
    }

    // 目标目录
    QString targetDir = "/mnt/sdcard/";

    // 检查目标目录是否存在
    QDir dir(targetDir);
    if (!dir.exists()) {
        QString errorText = pParent->systemmanager->getTranslatedText("tf_card_not_detected", pParent->currentLanguageIndex, "filemanage");

        CustomMessageBox::warning(this, "error", errorText);
        return;
    }

    // 构造目标路径
    QString targetPath = targetDir + fileInfo.fileName();

    // 检查目标文件是否已存在
    if (QFileInfo::exists(targetPath)) {
        QString confirmText = pParent->systemmanager->getTranslatedText("transfer_confirm_overwrite", pParent->currentLanguageIndex, "filemanage");

        CustomMessageBox::StandardButton reply = CustomMessageBox::question(this,
                                                                            pParent->systemmanager->getTranslatedText("confirm_title", pParent->currentLanguageIndex, "filemanage"),
                                                                            confirmText,
                                                                            CustomMessageBox::Yes | CustomMessageBox::No);

        if (reply != CustomMessageBox::Yes) {
            return;
        }

        // 删除已存在的文件或文件夹
        if (QFileInfo(targetPath).isDir()) {
            QDir(targetPath).removeRecursively();
        } else {
            QFile::remove(targetPath);
        }
    }

    // 计算总文件大小
    m_totalBytes = 0;
    m_copiedBytes = 0;

    if (fileInfo.isDir()) {
        m_totalBytes = calculateFolderSize(fileInfo.absoluteFilePath());
    } else {
        m_totalBytes = fileInfo.size();
    }

    if (m_totalBytes < 0) {
        m_totalBytes = 0;
    }

    // 获取翻译文本
    QString progressTitle = pParent->systemmanager->getTranslatedText("transfer_progress_title", pParent->currentLanguageIndex, "filemanage");

    QString progressText = pParent->systemmanager->getTranslatedText("transfer_progress_text", pParent->currentLanguageIndex, "filemanage");

    // 创建自定义进度对话框
    m_progressDialog = new CustomProgressDialog(this);
    m_progressDialog->setDialogTitle(progressTitle);
    m_progressDialog->setLabelText(progressText + "\n" + fileInfo.fileName());
    m_progressDialog->setRange(0, 100);
    m_progressDialog->setValue(0);
    m_progressDialog->setCancelButtonVisible(false); // 隐藏取消按钮

    // 获取内部进度条以便后续操作
    m_progressBar = m_progressDialog->findChild<QProgressBar*>();
    if (m_progressBar) {
        m_progressBar->setTextVisible(true);
        // 设置自定义滚动样式
        m_progressBar->setStyleSheet(
            "QProgressBar {"
            "    border: 2px solid #ddd;"
            "    border-radius: 8px;"
            "    text-align: center;"
            "    background-color: #f5f5f5;"
            "    height: 30px;"
            "    font-size: 14px;"
            "    font-weight: bold;"
            "    color: #333;"
            "}"
            "QProgressBar::chunk {"
            "    background: repeating-linear-gradient("
            "        45deg,"
            "        #4CAF50 0px,"
            "        #4CAF50 4px,"
            "        #81C784 4px,"
            "        #81C784 8px"
            "    );"
            "    border-radius: 6px;"
            "    margin: 1px;"
            "}"
            );
    }

    // 初始化动画相关变量
    m_realProgress = 0;
    m_animationOffset = 0;

    // 设置一个小的初始进度值，确保能看到chunk部分
    if (m_progressBar) {
        m_progressBar->setValue(0);
    }

    // 创建滚动动画定时器
    m_animationTimer = new QTimer(this);
    connect(m_animationTimer, &QTimer::timeout, this, [this]() {
        updateScrollingAnimation();
    });

    // 启动滚动动画（一直运行）
    qDebug() << "启动滚动动画定时器";
    m_animationTimer->start(100); // 每100ms更新一次滚动效果

    // 设置速度信息
    QString preparationText = pParent->systemmanager->getTranslatedText("inPreparation", pParent->currentLanguageIndex, "filemanage");
    QString sizeInfo = QString(pParent->systemmanager->getTranslatedText("totalSize", pParent->currentLanguageIndex, "filemanage")).arg(getReadableSize(m_totalBytes));
    m_progressDialog->setSpeedText(preparationText + "\n" + sizeInfo);

    // 获取速度标签的引用以便后续更新
    m_progressLabel = m_progressDialog->findChild<QLabel*>("speedLabel");

    m_progressDialog->show();

    // 开始实时传输监控
    startRealTimeTransfer(fileInfo, targetPath);
}

// 设置真实进度
void FileManage::setRealProgress(int progress)
{
    if (progress < 0) progress = 0;
    if (progress > 100) progress = 100;

    m_realProgress = progress;

    // 更新进度条的值（显示真实百分比）
    if (m_progressBar) {
        m_progressBar->setValue(m_realProgress);
    }
    // 同时更新自定义对话框的进度
    if (m_progressDialog) {
        m_progressDialog->setValue(m_realProgress);
    }
}

// 更新滚动动画
void FileManage::updateScrollingAnimation()
{
    if (!m_progressBar) {
        return;
    }

    // 更新滚动偏移量 - 创建循环滚动效果
    m_animationOffset += 1; // 每次移动1像素，让滚动更平滑
    if (m_animationOffset >= 8) { // 当偏移量达到条纹周期时重置（8像素一个周期）
        m_animationOffset = 0;
    }

    // 更新进度条样式（包含滚动效果）
    updateProgressBarStyle(m_animationOffset);
}

// 更新进度条样式（带滚动条纹效果）
void FileManage::updateProgressBarStyle(int offset)
{
    if (!m_progressBar) {

        return;
    }

    QString style;

    if (m_realProgress >= 100) {
        // 完成时的样式（停止滚动，显示完成状态）
        style = QString(
            "QProgressBar {"
            "    border: 2px solid #4CAF50;"
            "    border-radius: 8px;"
            "    text-align: center;"
            "    background-color: #f5f5f5;"
            "    height: 30px;"
            "    font-size: 14px;"
            "    font-weight: bold;"
            "    color: #333;"
            "}"
            "QProgressBar::chunk {"
            "    background: #4CAF50;"
            "    border-radius: 6px;"
            "    margin: 1px;"
            "}"
            );
    } else {
        // 传输中的滚动条纹样式 - 使用简单的渐变滚动效果
        int pos1 = offset;
        int pos2 = (offset + 4) % 16;
        int pos3 = (offset + 8) % 16;
        int pos4 = (offset + 12) % 16;

        style = QString(
                    "QProgressBar {"
                    "    border: 2px solid #ddd;"
                    "    border-radius: 8px;"
                    "    text-align: center;"
                    "    background-color: #f5f5f5;"
                    "    height: 30px;"
                    "    font-size: 14px;"
                    "    font-weight: bold;"
                    "    color: #333;"
                    "}"
                    "QProgressBar::chunk {"
                    "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, "
                    "                stop:0.%1 #4CAF50, stop:0.%2 #66BB6A, "
                    "                stop:0.%3 #4CAF50, stop:0.%4 #66BB6A, stop:1 #4CAF50);"
                    "    border-radius: 6px;"
                    "    margin: 1px;"
                    "}"
                    ).arg(pos1 * 6).arg(pos2 * 6).arg(pos3 * 6).arg(pos4 * 6);
    }

    m_progressBar->setStyleSheet(style);
}

// 开始实时传输
void FileManage::startRealTimeTransfer(const QFileInfo& fileInfo, const QString& targetPath)
{
    MainWindow* pParent = qobject_cast<MainWindow*>(parent());
    m_targetPath = targetPath;

    // 记录传输开始时间
    m_transferStartTime = QDateTime::currentDateTime();
    qDebug() << "传输开始时间:" << m_transferStartTime.toString("yyyy-MM-dd hh:mm:ss.zzz");

    // 启动后台复制进程
    QProcess* copyProcess = new QProcess(this);

    QString command;
    QStringList arguments;

    if (fileInfo.isDir()) {
        // 使用cp命令复制目录
        command = "cp";
        arguments << "-r" << fileInfo.absoluteFilePath() << targetPath;
    } else {
        // 使用cp命令复制文件
        command = "cp";
        arguments << fileInfo.absoluteFilePath() << targetPath;
    }

    qDebug() << "执行复制命令:" << command << arguments.join(" ");

    // 启动进度监控
    monitorTransferProgress(targetPath, m_totalBytes);

    // 连接进程完成信号
    connect(copyProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            [=](int exitCode, QProcess::ExitStatus exitStatus) {

                // 记录传输结束时间并计算传输用时
                m_transferEndTime = QDateTime::currentDateTime();
                m_transferDurationMs = m_transferStartTime.msecsTo(m_transferEndTime);

                // 计算平均传输速度
                double averageSpeedBytesPerSec = 0.0;
                QString averageSpeedText = "未知";
                if (m_transferDurationMs > 0 && m_totalBytes > 0) {
                    averageSpeedBytesPerSec = (double)m_totalBytes * 1000.0 / m_transferDurationMs;
                    averageSpeedText = getReadableSize((qint64)averageSpeedBytesPerSec) + "/s";
                }

                // 停止进度监控
                if (m_progressTimer) {
                    m_progressTimer->stop();
                    m_progressTimer->deleteLater();
                    m_progressTimer = nullptr;
                }

                // 设置最终进度为100%并停止滚动动画
                setRealProgress(100);
                if (m_animationTimer) {
                    m_animationTimer->stop();
                    m_animationTimer->deleteLater();
                    m_animationTimer = nullptr;
                }

                // 关闭进度对话框
                if (m_progressDialog) {
                    m_progressDialog->close();
                    m_progressDialog->deleteLater();
                    m_progressDialog = nullptr;
                }

                // 显示结果
                bool success = (exitCode == 0 && exitStatus == QProcess::NormalExit);

                // 格式化传输时间显示
                QString timeInfo = formatTransferTime(m_transferDurationMs);

                // 计算并格式化平均传输速度
                QString speedInfo = "未知";
                if (m_transferDurationMs > 0 && m_totalBytes > 0) {
                    double averageSpeedBytesPerSec = (double)m_totalBytes * 1000.0 / m_transferDurationMs;
                    speedInfo = getReadableSize((qint64)averageSpeedBytesPerSec) + "/s";
                }

                if (success) {
                    QString successText = pParent->systemmanager->getTranslatedText("transfer_success", pParent->currentLanguageIndex, "filemanage");
                    QString detailedSuccessText = successText + "\n" +
                                                  QString(pParent->systemmanager->getTranslatedText("transfer_detail", pParent->currentLanguageIndex, "filemanage"))
                                                      .arg(timeInfo)
                                                      .arg(speedInfo)
                                                      .arg(getReadableSize(m_totalBytes));
                    CustomMessageBox::information(this, pParent->systemmanager->getTranslatedText("successful", pParent->currentLanguageIndex, "filemanage"), detailedSuccessText);
                } else {
                    QString errorText = pParent->systemmanager->getTranslatedText("transfer_error", pParent->currentLanguageIndex, "filemanage");
                    QString detailedErrorText = errorText + "\n" +
                                                QString(pParent->systemmanager->getTranslatedText("transfer_detail", pParent->currentLanguageIndex, "filemanage"))
                                                    .arg(timeInfo)
                                                    .arg(speedInfo)
                                                    .arg(getReadableSize(m_totalBytes));
                    CustomMessageBox::warning(this, pParent->systemmanager->getTranslatedText("error", pParent->currentLanguageIndex, "filemanage"), detailedErrorText);
                }
                copyProcess->deleteLater();
            });

    // 启动复制进程
    copyProcess->start(command, arguments);
}

// 监控传输进度
void FileManage::monitorTransferProgress(const QString& targetPath, qint64 totalSize)
{
    if (!m_progressDialog || !m_progressBar || !m_progressLabel) {
        return;
    }
    MainWindow* pParent = qobject_cast<MainWindow*>(parent());
    if (!pParent || !pParent->systemmanager) {
        qDebug() << "Error: Invalid parent or translation system not initialized!";
        return;
    }

    m_progressTimer = new QTimer(this);

    connect(m_progressTimer, &QTimer::timeout, [=]() {
        qint64 currentSize = 0;

        // 检查目标文件/目录的当前大小
        QFileInfo targetInfo(targetPath);
        if (targetInfo.exists()) {
            if (targetInfo.isDir()) {
                currentSize = calculateDirectorySize(targetPath);
            } else {
                currentSize = targetInfo.size();
            }
        }

        // 计算进度百分比
        int progress = 0;
        if (totalSize > 0) {
            progress = (int)((currentSize * 100) / totalSize);
            if (progress > 100) progress = 100;
        }

        // 更新真实进度（百分比数字）
        setRealProgress(progress);

        // 计算传输速度和剩余时间
        QDateTime currentTime = QDateTime::currentDateTime();
        qint64 elapsedMs = m_transferStartTime.msecsTo(currentTime);

        QString speedText = "";
        QString remainingTimeText = "";

        if (elapsedMs > 0 && currentSize > 0) {
            // 计算传输速度 (字节/秒)
            double speedBytesPerSec = (double)currentSize * 1000.0 / elapsedMs;
            speedText = QString(pParent->systemmanager->getTranslatedText("speed", pParent->currentLanguageIndex, "filemanage")).arg(getReadableSize((qint64)speedBytesPerSec));

            // 计算剩余时间
            if (speedBytesPerSec > 0 && progress < 100) {
                qint64 remainingBytes = totalSize - currentSize;
                qint64 remainingMs = (qint64)(remainingBytes * 1000.0 / speedBytesPerSec);
                remainingTimeText = QString(pParent->systemmanager->getTranslatedText("remaining_time", pParent->currentLanguageIndex, "filemanage")).arg(formatTransferTime(remainingMs));
            }
        }

        // 更新状态文本
        QString progressText = QString(pParent->systemmanager->getTranslatedText("hasBeenTransmitted", pParent->currentLanguageIndex, "filemanage"))
                                   .arg(getReadableSize(currentSize))
                                   .arg(getReadableSize(totalSize))
                                   .arg(progress);

        // 添加速度和剩余时间信息
        if (!speedText.isEmpty()) {
            progressText += "\n" + speedText;
        }
        if (!remainingTimeText.isEmpty()) {
            progressText += "  " + remainingTimeText;
        }

        // 更新自定义对话框的速度文本
        if (m_progressDialog) {
            m_progressDialog->setSpeedText(progressText);
        }
        // 兼容性：如果找到了标签也更新它
        if (m_progressLabel) {
            m_progressLabel->setText(progressText);
        }
    });

    // 每100ms检查一次进度
    m_progressTimer->start(100);
}

// 计算目录大小
qint64 FileManage::calculateDirectorySize(const QString& dirPath)
{
    qint64 totalSize = 0;
    QDir dir(dirPath);

    if (!dir.exists()) {
        return 0;
    }

    // 获取所有文件
    QFileInfoList files = dir.entryInfoList(QDir::Files);
    for (const QFileInfo& fileInfo : files) {
        totalSize += fileInfo.size();
    }

    // 递归计算子目录
    QFileInfoList dirs = dir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot);
    for (const QFileInfo& dirInfo : dirs) {
        totalSize += calculateDirectorySize(dirInfo.absoluteFilePath());
    }

    return totalSize;
}



bool FileManage::copyDirectoryRecursively(const QString& sourceDir, const QString& targetDir)
{
    // 保持原有的简单递归复制函数，用于向后兼容
    QDir source(sourceDir);
    if (!source.exists()) {
        return false;
    }

    QDir target(targetDir);
    if (!target.exists()) {
        if (!target.mkpath(".")) {
            return false;
        }
    }

    // 复制所有文件
    QFileInfoList files = source.entryInfoList(QDir::Files);
    for (const QFileInfo& fileInfo : files) {
        QString sourceFile = fileInfo.absoluteFilePath();
        QString targetFile = targetDir + QDir::separator() + fileInfo.fileName();

        if (QFile::exists(targetFile)) {
            QFile::remove(targetFile);
        }

        if (!QFile::copy(sourceFile, targetFile)) {
            return false;
        }
    }

    // 递归复制所有子目录
    QFileInfoList dirs = source.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot);
    for (const QFileInfo& dirInfo : dirs) {
        QString sourceDirPath = dirInfo.absoluteFilePath();
        QString targetDirPath = targetDir + QDir::separator() + dirInfo.fileName();

        if (!copyDirectoryRecursively(sourceDirPath, targetDirPath)) {
            return false;
        }
    }

    return true;
}

QString FileManage::getReadableSize(qint64 bytes) {
    if (bytes < 1024) {
        return QString("%1 B").arg(bytes);
    }
    else if (bytes < 1024 * 1024) {
        return QString("%1 KB").arg(bytes / 1024.0, 0, 'f', 2);
    }
    else if (bytes < 1024 * 1024 * 1024) {
        return QString("%1 MB").arg(bytes / 1024.0 / 1024.0, 0, 'f', 2);
    }
    else {
        return QString("%1 GB").arg(bytes / 1024.0 / 1024.0 / 1024.0, 0, 'f', 2);
    }
}

// 格式化传输时间显示
QString FileManage::formatTransferTime(qint64 durationMs) {
    if (durationMs < 1000) {
        return QString("%1 毫秒").arg(durationMs);
    }
    else if (durationMs < 60000) { // 小于1分钟
        double seconds = durationMs / 1000.0;
        return QString("%1 秒").arg(seconds, 0, 'f', 2);
    }
    else if (durationMs < 3600000) { // 小于1小时
        int minutes = durationMs / 60000;
        int seconds = (durationMs % 60000) / 1000;
        return QString("%1 分 %2 秒").arg(minutes).arg(seconds);
    }
    else { // 1小时或以上
        int hours = durationMs / 3600000;
        int minutes = (durationMs % 3600000) / 60000;
        int seconds = (durationMs % 60000) / 1000;
        return QString("%1 小时 %2 分 %3 秒").arg(hours).arg(minutes).arg(seconds);
    }
}

qint64 FileManage::calculateFolderSize(const QString& folderPath) {
    qint64 totalSize = 0;
    QDir dir(folderPath);
    QFileInfoList fileList = dir.entryInfoList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);

    foreach(const QFileInfo & fileInfo, fileList) {
        if (fileInfo.isDir()) {
            qint64 subFolderSize = calculateFolderSize(fileInfo.absoluteFilePath());
            if (subFolderSize == -1) {
                return -1;
            }
            totalSize += subFolderSize;
        }
        else {
            totalSize += fileInfo.size();
        }
    }

    return totalSize;
}

//重命名
void FileManage::onContextMenuRename(const QModelIndex& index) {
    // 获取文件路径和信息
    QModelIndex sourceIndex = m_filterProxyModel->mapToSource(index);
    QString filePath = m_fileSystemModel->filePath(sourceIndex);
    QFileInfo fileInfo(filePath);

    // 弹出对话框提示用户输入新名字
    bool ok;
    QString newName = QInputDialog::getText(
        this,
        "重命名",
        "请输入新名字:",
        QLineEdit::Normal,
        fileInfo.fileName(),
        &ok
        );

    if (ok && !newName.isEmpty()) {
        // 构造新路径
        QString newFilePath;
        if (fileInfo.isDir()) {
            newFilePath = fileInfo.path() + QDir::separator() + newName;
        }
        else {
            QString suffix = fileInfo.completeSuffix(); // 获取文件扩展名
            newFilePath = fileInfo.path() + QDir::separator() + newName + "." + suffix;
        }

        // 检查新名字是否与现有文件或文件夹冲突
        if (QFile::exists(newFilePath)) {
            CustomMessageBox::warning(this, "错误", "文件或文件夹已存在");
            return;
        }

        // 执行重命名操作
        if (fileInfo.isDir()) {
            if (QDir(filePath).rename(filePath, newFilePath)) {
                qDebug() << "文件夹重命名成功：" << newFilePath;
            }
            else {
                CustomMessageBox::warning(this, "错误", "无法重命名文件夹");
            }
        }
        else {
            if (QFile::rename(filePath, newFilePath)) {
                qDebug() << "文件重命名成功：" << newFilePath;
            }
            else {
                CustomMessageBox::warning(this, "错误", "无法重命名文件");
            }
        }
    }
}

void FileManage::TreeorListShow() {
    if (ui->SLT->isChecked()) {
        // 设置 QListView 和 QTreeView 的状态
        ui->listView->setVisible(true); // 隐藏
        ui->treeView->setVisible(false);  // 显示
    }
    else if (ui->LB->isChecked()) {
        // 设置 QListView 和 QTreeView 的状态
        ui->listView->setVisible(false); // 显示
        ui->treeView->setVisible(true);  // 隐藏
    }
}
void FileManage::SelectDate() {
    ui->widget_4->setVisible(true);
    MainWindow* mainWindow = qobject_cast<MainWindow*>(parent()); // 获取父窗口指针
    if (mainWindow) {
        if (mainWindow->currentLanguageIndex == 0) {
            QLocale locale(QLocale::Chinese);
            ui->calendarWidget->setLocale(locale);
        }
        if (mainWindow->currentLanguageIndex == 1) {
            QLocale locale(QLocale::English);
            ui->calendarWidget->setLocale(locale);
        }
        if (mainWindow->currentLanguageIndex == 2) {
            QLocale locale(QLocale::Arabic);
            ui->calendarWidget->setLocale(locale);
        }
        if (mainWindow->currentLanguageIndex == 3) {
            QLocale locale(QLocale::Chinese, QLocale::Taiwan);
            ui->calendarWidget->setLocale(locale); // 应用系统区域设置;
        }
        if (mainWindow->currentLanguageIndex == 4) {
            QLocale locale(QLocale::French);
            ui->calendarWidget->setLocale(locale);
        }
        if (mainWindow->currentLanguageIndex == 5) {
            QLocale locale(QLocale::German);
            ui->calendarWidget->setLocale(locale);
        }
        if (mainWindow->currentLanguageIndex == 6) {
            QLocale locale(QLocale::Italian);
            ui->calendarWidget->setLocale(locale);
        }
        if (mainWindow->currentLanguageIndex == 7) {
            QLocale locale(QLocale::Japanese);
            ui->calendarWidget->setLocale(locale);
        }
        if (mainWindow->currentLanguageIndex == 8) {
            QLocale locale(QLocale::Korean);
            ui->calendarWidget->setLocale(locale);
        }
        if (mainWindow->currentLanguageIndex == 9) {
            QLocale locale(QLocale::Portuguese);
            ui->calendarWidget->setLocale(locale);
        }
        if (mainWindow->currentLanguageIndex == 10) {
            QLocale locale(QLocale::Russian);
            ui->calendarWidget->setLocale(locale);
        }
        if (mainWindow->currentLanguageIndex == 11) {
            QLocale locale(QLocale::Spanish);
            ui->calendarWidget->setLocale(locale);
        }
        if (mainWindow->currentLanguageIndex == 12) {
            QLocale locale(QLocale::Turkish);
            ui->calendarWidget->setLocale(locale);
        }
    }
}
void FileManage::onPictureButtonClicked() {
    QString mcf = getBasePath();
    m_currentFolder = "Picture/"; // 设置当前文件夹为图片文件夹
    QString picturePath = mcf + "/" + m_currentFolder;

    // 确保Picture目录存在
    if (!ensureDirectoryExists(picturePath)) {
        qDebug() << "无法创建或访问Picture目录:" << picturePath;
        return;
    }

    navigateToFolder(picturePath); // 导航到 pictures 文件夹

    // 设置 QListView 和 QTreeView 的状态
    TreeorListShow();
}

void FileManage::onVideoButtonClicked()
{
    QString mcf = getBasePath();
    m_currentFolder = "Recording/"; // 设置当前文件夹为视频文件夹
    QString recordingPath = mcf + "/" + m_currentFolder;

    // 确保Recording目录存在
    if (!ensureDirectoryExists(recordingPath)) {
        qDebug() << "无法创建或访问Recording目录:" << recordingPath;
        return;
    }

    navigateToFolder(recordingPath); // 导航到 videos 文件夹
    // 设置 QListView 和 QTreeView 的状态
    TreeorListShow();
}

void FileManage::onChannel1ButtonClicked() {
    QString mcf = getBasePath();
    if (m_currentFolder == "Recording/") {
        // 检查 CH1 文件夹是否存在
        QString ch1Path = getAbsolutePath(mcf + "/" + m_currentFolder + "CH1");
        QDir ch1Dir(ch1Path);

        if (!ch1Dir.exists()) {
            // 如果文件夹不存在，直接返回
            return; // 不执行导航操作
        }

        // 重置过滤器
        resetFilters();
        navigateToFolder(mcf + "/" + m_currentFolder + "CH1");
    }
    else if (m_currentFolder == "Picture/") {
        // 检查 CH1 文件夹是否存在
        QString ch1Path = getAbsolutePath(mcf + "/" + m_currentFolder + "CH1");
        QDir ch1Dir(ch1Path);

        if (!ch1Dir.exists()) {
            // 如果文件夹不存在，直接返回
            return; // 不执行导航操作
        }

        // 重置过滤器
        resetFilters();
        navigateToFolder(mcf + "/" + m_currentFolder + "CH1");
    }
}
void FileManage::onTFcardButtonClicked()
{
    QString tfcardPath = "/mnt/sdcard";

    // 检查TF卡目录是否存在
    QDir tfcardDir(tfcardPath);
    if (!tfcardDir.exists()) {
        qDebug() << "TF卡目录不存在:" << tfcardPath;
        return; // 不执行导航操作
    }

    qDebug() << "TF卡按钮被点击，导航到:" << tfcardPath;

    // 设置当前文件夹为TF卡
    m_currentFolder = "TFCard/";

    // 重置过滤器
    resetFilters();

    // 导航到TF卡目录
    navigateToFolder(tfcardPath);

    // 设置 QListView 和 QTreeView 的状态
    TreeorListShow();
}

void FileManage::ListViewShow() {
    if (!m_fileSystemModel) {
        return;
    }
    ui->listView->setVisible(false);  // 显示缩略图视图
    ui->treeView->setVisible(true); // 隐藏列表视图

    // 获取当前路径
    QString currentPath = m_fileSystemModel->rootPath();

    // 将源索引映射到代理索引
    QModelIndex sourceIndex = m_fileSystemModel->index(currentPath);
    QModelIndex proxyIndex = m_filterProxyModel->mapFromSource(sourceIndex);
    qDebug()<<"sourceIndex:"<<sourceIndex;
    qDebug()<<"proxyIndex:"<<proxyIndex;
    // 设置 QListView 的根索引
    ui->listView->setRootIndex(proxyIndex);
}

void FileManage::TreeViewShow() {
    if (!m_fileSystemModel) {
        return;
    }
    ui->listView->setVisible(true); // 隐藏缩略图视图
    ui->treeView->setVisible(false);  // 显示列表视图

    // 获取当前路径
    QString currentPath = m_fileSystemModel->rootPath();

    // 将源索引映射到代理索引
    QModelIndex sourceIndex = m_fileSystemModel->index(currentPath);
    QModelIndex proxyIndex = m_filterProxyModel->mapFromSource(sourceIndex);

    // 设置 QTreeView 的根索引
    ui->treeView->setRootIndex(proxyIndex);
}

void FileManage::onChannel2ButtonClicked() {
    QString mcf = getBasePath();
    if (m_currentFolder == "Recording/") {
        // 检查 CH2 文件夹是否存在
        QString ch2Path = getAbsolutePath(mcf + "/" + m_currentFolder + "CH2");
        QDir ch2Dir(ch2Path);

        if (!ch2Dir.exists()) {
            // 如果文件夹不存在，直接返回
            return; // 不执行导航操作
        }

        // 重置过滤器
        resetFilters();
        navigateToFolder(mcf + "/" + m_currentFolder + "CH2");
    }
    else if (m_currentFolder == "Picture/") {
        // 检查 CH2 文件夹是否存在
        QString ch2Path = getAbsolutePath(mcf + "/" + m_currentFolder + "CH2");
        QDir ch2Dir(ch2Path);

        if (!ch2Dir.exists()) {
            // 如果文件夹不存在，直接返回
            return; // 不执行导航操作
        }

        // 重置过滤器
        resetFilters();
        navigateToFolder(mcf + "/" + m_currentFolder + "CH2");
    }
}

void FileManage::onChannel3ButtonClicked() {
    QString mcf = getBasePath();
    if (m_currentFolder == "Recording/") {
        // 检查 CH3 文件夹是否存在
        QString ch3Path = getAbsolutePath(mcf + "/" + m_currentFolder + "CH3");
        QDir ch3Dir(ch3Path);

        if (!ch3Dir.exists()) {
            // 如果文件夹不存在，直接返回
            return; // 不执行导航操作
        }

        // 重置过滤器
        resetFilters();
        navigateToFolder(mcf + "/" + m_currentFolder + "CH3");
    }
    else if (m_currentFolder == "Picture/") {
        // 检查 CH3 文件夹是否存在
        QString ch3Path = getAbsolutePath(mcf + "/" + m_currentFolder + "CH3");
        QDir ch3Dir(ch3Path);

        if (!ch3Dir.exists()) {
            // 如果文件夹不存在，直接返回
            return; // 不执行导航操作
        }

        // 重置过滤器
        resetFilters();
        navigateToFolder(mcf + "/" + m_currentFolder + "CH3");
    }
}

void FileManage::onChannel4ButtonClicked() {
    QString mcf = getBasePath();
    if (m_currentFolder == "Recording/") {
        // 检查 CH4 文件夹是否存在
        QString ch4Path = getAbsolutePath(mcf + "/" + m_currentFolder + "CH4");
        QDir ch4Dir(ch4Path);

        if (!ch4Dir.exists()) {
            // 如果文件夹不存在，直接返回
            return; // 不执行导航操作
        }

        // 重置过滤器
        resetFilters();
        navigateToFolder(mcf + "/" + m_currentFolder + "CH4");
    }
    else if (m_currentFolder == "Picture/") {
        // 检查 CH4 文件夹是否存在
        QString ch4Path = getAbsolutePath(mcf + "/" + m_currentFolder + "CH4");
        QDir ch4Dir(ch4Path);

        if (!ch4Dir.exists()) {
            // 如果文件夹不存在，直接返回
            return; // 不执行导航操作
        }

        // 重置过滤器
        resetFilters();
        navigateToFolder(mcf + "/" + m_currentFolder + "CH4");
    }
}



void FileManage::navigateToFolder(const QString& folderPath)
{
    // 安全检查：确保模型已初始化
    if (!m_fileSystemModel || !m_filterProxyModel) {
        qDebug() << "错误：文件系统模型未初始化";
        return;
    }

    QString absolutePath = getAbsolutePath(folderPath);
    qDebug() << "导航到文件夹:" << absolutePath;

    // 检查路径是否存在
    QDir dir(absolutePath);
    if (!dir.exists()) {
        qDebug() << "错误：文件夹不存在:" << absolutePath;
        return;
    }

    // 更新当前文件夹路径
    m_currentFolderPath = absolutePath;

    // 设置模型根路径
    m_fileSystemModel->setRootPath(absolutePath);
    QModelIndex sourceIndex = m_fileSystemModel->index(absolutePath);

    // 调试信息：检查根路径和索引
    qDebug() << "Root path set to:" << absolutePath;
    qDebug() << "Source index is valid:" << sourceIndex.isValid();

    if (!sourceIndex.isValid()) {
        qDebug() << "错误：无法获取有效的源索引";
        return;
    }

    // 将源索引映射到代理索引
    QModelIndex proxyIndex = m_filterProxyModel->mapFromSource(sourceIndex);

    if (!proxyIndex.isValid()) {
        qDebug() << "错误：无法获取有效的代理索引";
        return;
    }

    // 更新视图的根索引
    ui->treeView->setRootIndex(proxyIndex);
    ui->listView->setRootIndex(proxyIndex);

    // 重置过滤器（在设置根索引之后）
    resetFilters();
}

QString FileManage::getAbsolutePath(const QString& relativePath)
{
    QDir currentDir(QDir::currentPath()); // 获取当前工作目录
    return currentDir.absoluteFilePath(relativePath); // 转换为绝对路径
}

// 获取当前配置的基础路径
QString FileManage::getBasePath() const
{
    MainWindow* pParent = qobject_cast<MainWindow*>(parent());
    if (!pParent) {
        qDebug() << "警告：无法获取MainWindow父对象，使用默认路径";
        return "/mnt/nvme"; // 默认路径
    }

    QString basePath = pParent->basePath;
    qDebug() << "从MainWindow获取的basePath:" << basePath;

    if (basePath.isEmpty()) {
        // 向后兼容：使用Picturepath
        basePath = pParent->Picturepath;
        qDebug() << "basePath为空，使用Picturepath:" << basePath;

        if (basePath.endsWith("/")) {
            basePath = basePath.left(basePath.length() - 1);
        }
        if (basePath.isEmpty()) {
            basePath = "/mnt/nvme"; // 最终默认值
            qDebug() << "Picturepath也为空，使用最终默认值:" << basePath;
        }
    }

    // 验证路径是否存在
    QDir dir(basePath);
    if (!dir.exists()) {
        qDebug() << "警告：配置的基础路径不存在:" << basePath << "，使用默认路径";
        return "/mnt/nvme";
    }

    qDebug() << "最终使用的基础路径:" << basePath;
    return basePath;
}

// 确保目录存在，如果不存在则创建
bool FileManage::ensureDirectoryExists(const QString& dirPath)
{
    QDir dir(dirPath);
    if (dir.exists()) {
        qDebug() << "目录已存在:" << dirPath;
        return true;
    }

    if (dir.mkpath(".")) {
        qDebug() << "成功创建目录:" << dirPath;
        return true;
    } else {
        qDebug() << "创建目录失败:" << dirPath;
        return false;
    }
}

void FileManage::SetSelectDate(const QDate& fdate) {
    if (!m_fileSystemModel) {
        return;
    }

    // 获取选择的日期
    QString selectedDate = fdate.toString("yyyy-MM-dd");

    // 启用自定义过滤逻辑
    m_filterProxyModel->setUseCustomFilter(true);

    // 设置日期过滤
    m_filterProxyModel->setFilterDate(fdate); // 直接传递 QDate
    m_filterProxyModel->enableDateFilter(true); // 启用日期过滤
    m_filterProxyModel->enableKeywordFilter(false); // 禁用文件名过滤

    // 设置视图的根索引为当前文件夹路径
    QString currentPath = m_currentFolderPath;
    QModelIndex rootIndex = m_fileSystemModel->index(currentPath);
    ui->treeView->setRootIndex(m_filterProxyModel->mapFromSource(rootIndex));
    ui->listView->setRootIndex(m_filterProxyModel->mapFromSource(rootIndex));

    // 如果没有找到匹配项，显示提示信息
    // if (m_filterProxyModel->rowCount(ui->treeView->rootIndex()) == 0) {
    //     //QMessageBox::information(this, "提示", "没有找到匹配的文件或文件夹。");
    // }
}
void FileManage::onFileDoubleClicked(const QModelIndex& index)
{
    MainWindow* pParent = qobject_cast<MainWindow*>(parent());

    // 确保传递的 index 属于 m_filterProxyModel
    QModelIndex sourceIndex = m_filterProxyModel->mapToSource(index);
    QString filePath = m_fileSystemModel->filePath(sourceIndex);
    qDebug() << "文件路径: " << filePath;

    QFileInfo fileInfo(filePath);
    if (!fileInfo.isReadable()) {
        return;
    }

    // 如果是文件夹，手动导航到该文件夹
    if (fileInfo.isDir()) {
        navigateToFolder(filePath); // 手动导航到文件夹
        return;
    }

    // 如果是文件，则根据文件类型处理
    QUrl fileUrl = QUrl::fromLocalFile(filePath);

    // 检查文件扩展名
    QString fileSuffix = fileInfo.suffix().toLower();
    if (fileSuffix == "mp4") {
        // 使用 QProcess 非阻塞调用外部播放器
        QProcess *playerProcess = new QProcess(this);

        // 设置进程结束后自动删除
        connect(playerProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
                playerProcess, &QProcess::deleteLater);

        // 处理启动失败的情况
        connect(playerProcess, &QProcess::errorOccurred, [this, filePath, playerProcess,pParent](QProcess::ProcessError error) {
            Q_UNUSED(error)
            CustomMessageBox::warning(this, pParent->systemmanager->getTranslatedText("error",pParent->currentLanguageIndex,"filemanage"), pParent->systemmanager->getTranslatedText("player_error",pParent->currentLanguageIndex,"filemanage"));
            playerProcess->deleteLater();
        });
        // 启动播放器进程
        playerProcess->start("qplayer", QStringList() << filePath);
    } else if (fileSuffix == "jpg" || fileSuffix == "png" || fileSuffix == "bmp") {
        // 加载图片
        QPixmap pixmap(filePath);
        if (pixmap.isNull()) {
            CustomMessageBox::warning(this, pParent->systemmanager->getTranslatedText("error",pParent->currentLanguageIndex,"filemanage"), pParent->systemmanager->getTranslatedText("image_load_error",pParent->currentLanguageIndex,"filemanage"));
            return;
        }

        // 获取屏幕尺寸，计算适合的显示大小（保持宽高比）
        QScreen *screen = QGuiApplication::primaryScreen();
        QRect screenGeometry = screen->availableGeometry();
        int maxWidth = screenGeometry.width() * 0.9;  // 最大宽度为屏幕宽度的90%
        int maxHeight = screenGeometry.height() * 0.9; // 最大高度为屏幕高度的90%

        // 计算缩放后的尺寸（保持宽高比）
        QPixmap scaledPixmap = pixmap.scaled(
            maxWidth, maxHeight,
            Qt::KeepAspectRatio,
            Qt::SmoothTransformation
            );

        // 获取图片文件名（不含路径）
        QFileInfo fileInfo(filePath);
        QString fileName = fileInfo.fileName();

        // 创建 QLabel 显示图片
        QLabel *imageLabel = new QLabel();
        imageLabel->setPixmap(scaledPixmap);
        imageLabel->setAlignment(Qt::AlignCenter);
        imageLabel->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

        // 创建自定义对话框
        CustomDialog *imageDialog = new CustomDialog(this);
        imageDialog->setDialogTitle(fileName); // 设置标题为图片文件名

        // 设置图片标签背景为黑色
        imageLabel->setStyleSheet("QLabel { background-color: black; }");

        // 设置对话框内容
        imageDialog->setDialogContent(imageLabel);

        // 调整窗口大小以适应图片（但不超过屏幕大小）
        imageDialog->setDialogSize(scaledPixmap.width() + 20, scaledPixmap.height() + 60);
        imageDialog->exec(); // 显示对话框
    } else {
        // 非 MP4 和 JPG 文件，用默认程序打开
        if (!QDesktopServices::openUrl(fileUrl)) {
            CustomMessageBox::warning(this, pParent->systemmanager->getTranslatedText("error",pParent->currentLanguageIndex,"filemanage"), pParent->systemmanager->getTranslatedText("cannot_Open_File",pParent->currentLanguageIndex,"filemanage"));
        }
    }
}


void FileManage::initializeFileSystemModel()
{
    if (!m_fileSystemModel) {
        m_fileSystemModel = new CustomFileSystemModel(this); // 初始化 m_fileSystemModel

        // 设置过滤器，只显示文件和文件夹
        m_fileSystemModel->setFilter(QDir::Dirs | QDir::Files | QDir::NoDotAndDotDot);

        // 设置 m_filterProxyModel 的源模型为 m_fileSystemModel
        m_filterProxyModel->setSourceModel(m_fileSystemModel);

        // 设置 QTreeView 和 QListView 的模型为 m_filterProxyModel
        ui->treeView->setModel(m_filterProxyModel);
        ui->listView->setModel(m_filterProxyModel);

        // 去掉 QTreeView 的文件夹箭头
        ui->treeView->setRootIsDecorated(false);

        // 确保所有列都可见
        ui->treeView->setColumnHidden(1, false); // 修改日期
        ui->treeView->setColumnHidden(2, false); // 类型
        ui->treeView->setColumnHidden(3, false); // 大小

        // 设置列的宽度
        ui->treeView->setColumnWidth(0, 300); // 名称列宽度
        ui->treeView->setColumnWidth(1, 200); // 修改日期列宽度
        ui->treeView->setColumnWidth(2, 150); // 类型列宽度
        ui->treeView->setColumnWidth(3, 100); // 大小列宽度

        // 配置 QListView
        ui->listView->setViewMode(QListView::IconMode); // 设置为图标模式
        ui->listView->setIconSize(QSize(64, 64));      // 设置图标大小
        ui->listView->setGridSize(QSize(100, 100));    // 设置网格大小
        ui->listView->setSpacing(10);                  // 设置间距

    }
}
QString FileManage::getCurrentFolderPath() const
{
    return m_currentFolderPath;
}

void FileManage::resetFilters() {
    // 安全检查：确保模型已初始化
    if (!m_filterProxyModel || !m_fileSystemModel) {
        qDebug() << "错误：过滤器重置失败，模型未初始化";
        return;
    }

    // 禁用自定义过滤逻辑
    m_filterProxyModel->setUseCustomFilter(false);

    // 禁用日期过滤
    m_filterProxyModel->enableDateFilter(false);

    // 禁用关键字过滤
    m_filterProxyModel->enableKeywordFilter(false);

    // 重置过滤日期和关键字
    m_filterProxyModel->setFilterDate(QDate()); // 设置为无效日期
    m_filterProxyModel->setFilterKeyword("");   // 设置为空字符串

    // 重新应用过滤器
    m_filterProxyModel->reapplyFilters();

    // 更新视图的根索引为当前文件夹路径
    QString currentPath = m_currentFolderPath;
    qDebug() << "重置过滤器，当前路径:" << currentPath;

    if (currentPath.isEmpty()) {
        qDebug() << "警告：当前文件夹路径为空";
        return;
    }

    QModelIndex rootIndex = m_fileSystemModel->index(currentPath);
    if (!rootIndex.isValid()) {
        qDebug() << "错误：无法获取当前路径的有效索引:" << currentPath;
        return;
    }

    QModelIndex proxyIndex = m_filterProxyModel->mapFromSource(rootIndex);
    if (!proxyIndex.isValid()) {
        qDebug() << "错误：无法映射到有效的代理索引";
        return;
    }

    ui->treeView->setRootIndex(proxyIndex);
    ui->listView->setRootIndex(proxyIndex);
}
