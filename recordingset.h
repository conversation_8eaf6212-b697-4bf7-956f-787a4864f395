#ifndef RECORDINGSET_H
#define RECORDINGSET_H

#include <QDialog>
#include <QMouseEvent>
#include "ui_recordingset.h"
#include <QButtonGroup>

#include "virtualkeyboard.h"

// 添加MainWindow的前向声明
class MainWindow;

namespace Ui {
class RecordingSet;
}

class RecordingSet : public QDialog
{
    Q_OBJECT

public:
    explicit RecordingSet(QWidget *parent = nullptr);
    ~RecordingSet();

private:
    // 加载当前通道的设置
    void loadChannelSettings();

private:
    Ui::RecordingSet *ui;
    bool mouse_press;
    QPoint mousePoint;

protected:
    // 鼠标按下事件 - 开始拖动
    void mousePressEvent(QMouseEvent* event) override
    {
        // 当左键点击在指定widget区域内时
        if (event->button() == Qt::LeftButton && ui->widget->geometry().contains(event->pos()))
        {
            mouse_press = true;                  // 设置拖动标志
            mousePoint = event->pos();            // 记录鼠标相对窗口的位置
            event->accept();                      // 接受事件
        }
    }

    // 鼠标移动事件 - 处理拖动
    void mouseMoveEvent(QMouseEvent* event) override
    {
        // 如果正在拖动且左键按住
        if (mouse_press && (event->buttons() & Qt::LeftButton))
        {
            QPoint globalPos = mapToGlobal(event->pos());  // 获取鼠标全局位置
            QPoint newPos = globalPos - mousePoint;        // 计算窗口新位置
            move(newPos);                                 // 移动窗口
            event->accept();                              // 接受事件
        }
    }

    // 鼠标释放事件 - 结束拖动
    void mouseReleaseEvent(QMouseEvent* event) override
    {
        if (event->button() == Qt::LeftButton)
        {
            mouse_press = false;  // 清除拖动标志
            event->accept();      // 接受事件
        }
    }

private:
    VirtualKeyboard* m_virtualKeyboard;  // 虚拟键盘实例
    QLineEdit* m_textEditProxy;         // 用于QTextEdit的代理
    QTextEdit* m_currentTextEdit;       // 当前活动的TextEdit

    // 初始化函数
    void setupTextEditKeyboard(QTextEdit* textEdit);
    void setupSpinBoxKeyboard(QSpinBox* spinBox);
    bool handleSpinBoxKeyboard(QSpinBox* spinBox, QLineEdit* lineEdit);

    // 事件过滤
    bool eventFilter(QObject* obj, QEvent* event) override;

private slots:
    void PathSelect();
    void closewindow(){
        close();
    }
    void recordingSettings();
    void spaceCheckTimer();
    //是否分段录像
    void Sectional_recording();
    //按时间分段
    void time_segment();
    //按字节分段
    void byte_segment();
    //录像存储限制
    void video_storage();

};


#endif // RECORDINGSET_H
