#ifndef CUSTOMPROGRESSDIALOG_H
#define CUSTOMPROGRESSDIALOG_H

#include "CustomDialog.h"
#include <QProgressBar>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>

class CustomProgressDialog : public CustomDialog
{
    Q_OBJECT

public:
    explicit CustomProgressDialog(QWidget *parent = nullptr);
    ~CustomProgressDialog();

    // 设置进度条范围
    void setRange(int minimum, int maximum);
    
    // 设置当前进度值
    void setValue(int value);
    
    // 获取当前进度值
    int value() const;
    
    // 设置状态文本
    void setLabelText(const QString &text);
    
    // 设置速度信息
    void setSpeedText(const QString &text);
    
    // 设置取消按钮文本
    void setCancelButtonText(const QString &text);
    
    // 显示/隐藏取消按钮
    void setCancelButtonVisible(bool visible);
    
    // 检查是否被取消
    bool wasCanceled() const;

signals:
    void canceled();

private slots:
    void onCancelClicked();

private:
    void setupProgressUI();
    
    QVBoxLayout *m_contentLayout;
    QLabel *m_statusLabel;
    QProgressBar *m_progressBar;
    QLabel *m_speedLabel;
    QPushButton *m_cancelButton;
    
    bool m_wasCanceled;
};

#endif // CUSTOMPROGRESSDIALOG_H
