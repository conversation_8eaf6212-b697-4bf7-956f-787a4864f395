#ifndef ABOUTUS_H
#define ABOUTUS_H

#include <QDialog>
#include "ui_aboutus.h"
#include <QMouseEvent>

namespace Ui {
class Aboutus;
}

class Aboutus : public QDialog
{
    Q_OBJECT

public:
    explicit Aboutus(QWidget *parent = nullptr);
    ~Aboutus();

private:
    Ui::Aboutus *ui;
    bool mouse_press;
    QPoint mousePoint;

protected:
    // 鼠标按下事件 - 开始拖动
    void mousePressEvent(QMouseEvent* event) override
    {
        // 当左键点击在指定widget区域内时
        if (event->button() == Qt::LeftButton && ui->widget->geometry().contains(event->pos()))
        {
            mouse_press = true;                  // 设置拖动标志
            mousePoint = event->pos();            // 记录鼠标相对窗口的位置
            event->accept();                      // 接受事件
        }
    }

    // 鼠标移动事件 - 处理拖动
    void mouseMoveEvent(QMouseEvent* event) override
    {
        // 如果正在拖动且左键按住
        if (mouse_press && (event->buttons() & Qt::LeftButton))
        {
            QPoint globalPos = mapToGlobal(event->pos());  // 获取鼠标全局位置
            QPoint newPos = globalPos - mousePoint;        // 计算窗口新位置
            move(newPos);                                 // 移动窗口
            event->accept();                              // 接受事件
        }
    }

    // 鼠标释放事件 - 结束拖动
    void mouseReleaseEvent(QMouseEvent* event) override
    {
        if (event->button() == Qt::LeftButton)
        {
            mouse_press = false;  // 清除拖动标志
            event->accept();      // 接受事件
        }
    }


private slots:
    void closewindow(){
        close();
    }
};

#endif // ABOUTUS_H
