#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QDebug>
#include <QTimer>
#include <QVBoxLayout>
#include <QFile>
#include <QTextStream>
#include <QRegExp>
#include<QScreen>
#include <QProcess>
#include <linux/videodev2.h>
#include <QElapsedTimer>
#include <QDir>
#include <QMessageBox>
#include <QAction>
#include <QMenu>
#include <libudev.h>
#include <SysComboBoxStyle.h>
#include <unistd.h>  // 提供 close() 函数


int keybod=0;

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent), ui(new Ui::MainWindow)
{
    ui->setupUi(this);
    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);
    //ui->video->setStyleSheet("background-color: rgb(0, 0, 0);");
    m_dragWidget = ui->widget;//长按鼠标可拖动范围

    showMaximized();

    deviceinit();

    ui->stackedWidget_2->setCurrentIndex(0);


    // 初始化 udev 监控
    m_udevMonitor = new UdevMonitor(this);
    if (!m_udevMonitor->isValid()) {
        qWarning("Udev monitor initialization failed, falling back to polling");
        // 可以回退到定时检查（方法一）
    }

    // 连接信号
    connect(m_udevMonitor, &UdevMonitor::deviceChanged, this, &MainWindow::onMouseDeviceChanged);

    startSystemDoubleClickMonitor();
    timerinit();
    const char* cmd1 = "media-ctl -d /dev/media0 --set-v4l2 '\"rkisp-isp-subdev\":0[fmt:UYVY2X8/1920x1080]'";
    const char* cmd2 = "media-ctl -d /dev/media0 --set-v4l2 '\"rkisp-isp-subdev\":0[crop:(0,0)/1920x1080]'";
    // const char* cmd3 ="mount /dev/nvme0n1 /sdcard";
    system(cmd1);
    system(cmd2);
    // system(cmd3);

    //初始化UI
    initUI();
    setupbutton();

    // 初始化控制按钮显示/隐藏功能
    initControlButtonsVisibility();
    QTimer::singleShot(100, this, [this]()
                       {
                            reopendevice();
                            clearHotplugDataFromIni(0,iniPath);
                            clearHotplugDataFromIni(1,iniPath);
                            clearHotplugDataFromIni(2,iniPath);
                            clearHotplugDataFromIni(3,iniPath);
                            //updateCameraBackgrounds();
                            curchannel =100;
                            Signals1();
                       });
    // 创建必要的文件夹结构
    createDirectoryStructure();

    // 初始化录音状态（默认关闭）
    curaudiostate = QVector<bool>(4, false);  // 4 个摄像头，初始 false
    // 初始化移动侦测状态（默认关闭）
    curmotionstate = QVector<bool>(4, false);  // 4 个摄像头，初始 false

    timewatermark = QVector<bool>(4, false);

}

MainWindow::~MainWindow()
{
    // 清理资源
    if (cameraStream1) {
        cameraStream1->cleanupGStreamer();
    }
    if (cameraStream2) {
        cameraStream2->cleanupGStreamer();
    }
    if (cameraStream3) {
        cameraStream3->cleanupGStreamer();
    }
    if (cameraStream4) {
        cameraStream4->cleanupGStreamer();
    }

    // 清理定时器资源
    if (m_mouseCheckTimer) {
        m_mouseCheckTimer->stop();
        delete m_mouseCheckTimer;
        m_mouseCheckTimer = nullptr;
    }

    if (m_buttonHideTimer) {
        m_buttonHideTimer->stop();
        delete m_buttonHideTimer;
        m_buttonHideTimer = nullptr;
    }

    // 安全释放键盘资源
    if (m_virtualKeyboard) {
        // 先停止键盘可能正在进行的操作
        m_virtualKeyboard->hide();
        m_virtualKeyboard->disconnect(); // 断开所有信号连接
        delete m_virtualKeyboard;
    }

    // 关闭所有错误对话框
    for (auto it = errorDialogs.begin(); it != errorDialogs.end(); ++it) {
        if (it.value()) {
            it.value()->close();
        }
    }
    errorDialogs.clear();

    stopSystemDoubleClickMonitor();

    delete ui;
}

void MainWindow::initUI(){
    QButtonGroup *group1 = new QButtonGroup(this);
    QButtonGroup *group2 = new QButtonGroup(this);

    group1->addButton(ui->videoSet, 0);
    group1->addButton(ui->pictureSet, 1);
    group1->addButton(ui->cameraControl, 2);
    group1->addButton(ui->textSet, 3);
    ui->videoSet->setChecked(true);

    group2->addButton(ui->p1, 0);
    group2->addButton(ui->p2, 1);
    group2->addButton(ui->p3, 2);
    group2->addButton(ui->p4, 3);
    ui->p1->setChecked(true);

    QButtonGroup *group3 = new QButtonGroup(this);
    group3->addButton(ui->p_1, 0);
    group3->addButton(ui->p_2, 1);
    group3->addButton(ui->p_3, 2);
    group3->addButton(ui->p_4, 3);
    group3->addButton(ui->p1_2, 4);
    group3->addButton(ui->p3_4, 5);
    group3->addButton(ui->p1234, 6);
    ui->p_1->setChecked(true);

    syncButtonGroups(group2, group3);
    syncButtonGroups(group3, group2);

    ui->comboBox_cameradev->setView(new QListView);
    ui->comboBox_cameradev->setStyleSheet(SysComboBoxStyle::getStyleSheet());
    ui->comboBox_videoformat->setView(new QListView);
    ui->comboBox_videoformat->setStyleSheet(SysComboBoxStyle::getStyleSheet());
    ui->comboBox_fbl->setView(new QListView);
    ui->comboBox_fbl->setStyleSheet(SysComboBoxStyle::getStyleSheet());
    ui->comboBox_audiodev->setView(new QListView);
    ui->comboBox_audiodev->setStyleSheet(SysComboBoxStyle::getStyleSheet());

    // 初始化虚拟键盘
    m_virtualKeyboard = new VirtualKeyboard(this);

    // 为所有SpinBox设置键盘
    setupSpinBoxKeyboard(ui->spinBox_brightness);
    setupSpinBoxKeyboard(ui->spinBox_contrast);
    setupSpinBoxKeyboard(ui->spinBox_saturation);
    setupSpinBoxKeyboard(ui->spinBox_sharpness);
    setupSpinBoxKeyboard(ui->spinBox_backlightcompensation);
    setupSpinBoxKeyboard(ui->spinBox_exposure);
    setupSpinBoxKeyboard(ui->spinBox_focus);
    setupSpinBoxKeyboard(ui->spinBox_gain);
    setupSpinBoxKeyboard(ui->spinBox_gamma);
    setupSpinBoxKeyboard(ui->spinBox_hue);
    setupSpinBoxKeyboard(ui->spinBox_pan);
    setupSpinBoxKeyboard(ui->spinBox_tilt);
    setupSpinBoxKeyboard(ui->spinBox_whitebalance);
    setupSpinBoxKeyboard(ui->spinBox_zoom);
}

// 通用设置函数
void MainWindow::setupSpinBoxKeyboard(QSpinBox* spinBox)
{
    if (!spinBox) return;

    // 确保可以获取焦点
    spinBox->setFocusPolicy(Qt::StrongFocus);

    // 获取内部的LineEdit
    QLineEdit* lineEdit = spinBox->findChild<QLineEdit*>();
    if (lineEdit) {
        lineEdit->installEventFilter(this);

        // 可选：设置样式以可视化LineEdit区域（调试用）
        lineEdit->setStyleSheet("QLineEdit { border: 1px solid red; }");
    }

    // SpinBox本身也安装事件过滤器
    spinBox->installEventFilter(this);
}

// 修改后的eventFilter
bool MainWindow::eventFilter(QObject* obj, QEvent* event)
{
    if (event->type() == QEvent::MouseButtonRelease) {
        QMouseEvent* mouseEvent = static_cast<QMouseEvent*>(event);

        // 处理LineEdit点击
        if (QLineEdit* lineEdit = qobject_cast<QLineEdit*>(obj)) {
            if (QSpinBox* spinBox = qobject_cast<QSpinBox*>(lineEdit->parent())) {
                // 检查SpinBox是否启用
                if (!spinBox->isEnabled()) {
                    return false; // 禁用时不处理
                }
                lineEdit->setFocus();
                lineEdit->selectAll();
                showKeyboardForSpinBox(spinBox);
                return true;
            }
        }
        // 处理SpinBox点击（需排除按钮区域）
        else if (QSpinBox* spinBox = qobject_cast<QSpinBox*>(obj)) {
            // 检查SpinBox是否启用
            if (!spinBox->isEnabled()) {
                return false; // 禁用时不处理
            }

            // 获取SpinBox的按钮区域
            QStyleOptionSpinBox opt;
            opt.initFrom(spinBox);
            QRect upRect = spinBox->style()->subControlRect(
                QStyle::CC_SpinBox, &opt, QStyle::SC_SpinBoxUp, spinBox);
            QRect downRect = spinBox->style()->subControlRect(
                QStyle::CC_SpinBox, &opt, QStyle::SC_SpinBoxDown, spinBox);

            // 仅当点击不在按钮区域时触发
            if (!upRect.contains(mouseEvent->pos()) &&
                !downRect.contains(mouseEvent->pos())) {
                spinBox->setFocus();
                if (QLineEdit* lineEdit = spinBox->findChild<QLineEdit*>()) {
                    lineEdit->selectAll();
                }
                showKeyboardForSpinBox(spinBox);
                return true;
            }
        }
    }
    return QMainWindow::eventFilter(obj, event);
}

// 显示键盘的辅助函数
void MainWindow::showKeyboardForSpinBox(QSpinBox* spinBox)
{
    if (!spinBox || !m_virtualKeyboard) return;

    QLineEdit* lineEdit = spinBox->findChild<QLineEdit*>();
    if (lineEdit) {
        m_virtualKeyboard->attachTo(lineEdit);
        m_virtualKeyboard->showAtOptimalPosition(spinBox);
        stopSystemDoubleClickMonitor();
    }
}


// 创建必要的文件夹结构
void MainWindow::createDirectoryStructure()
{
    // 使用配置的基础路径而不是硬编码的"/sdcard"
    QString basePathToUse = basePath;
    if (basePathToUse.isEmpty()) {
        basePathToUse = "/mnt/nvme";  // 默认值
    }

    // 确保基础路径存在
    QDir baseDir(basePathToUse);
    if (!baseDir.exists()) {
        if (!baseDir.mkpath(".")) {
            printf("创建基础目录失败: %s\n", basePathToUse.toUtf8().constData());
            return;
        }
        printf("成功创建基础目录: %s\n", basePathToUse.toUtf8().constData());
    } else {
        printf("基础目录已存在: %s\n", basePathToUse.toUtf8().constData());
    }

    // 创建 Picture 和 Recording 文件夹
    QStringList mainFolders = {"Picture", "Recording"};
    QStringList channelFolders = {"CH1", "CH2", "CH3", "CH4"};

    for (const QString &mainFolder : mainFolders) {
        // 检查并创建主文件夹（Picture 或 Recording）
        if (!baseDir.exists(mainFolder)) {
            if (baseDir.mkdir(mainFolder)) {
                printf("成功创建 %s/%s 文件夹\n", basePathToUse.toLocal8Bit().constData(), mainFolder.toLocal8Bit().constData());
            } else {
                printf("创建 %s/%s 文件夹失败\n", basePathToUse.toLocal8Bit().constData(), mainFolder.toLocal8Bit().constData());
                continue; // 如果无法创建主文件夹，跳过该文件夹的子文件夹创建
            }
        } else {
            printf("%s/%s 文件夹已存在\n", basePathToUse.toLocal8Bit().constData(), mainFolder.toLocal8Bit().constData());
        }

        // 在主文件夹下创建通道文件夹
        QDir mainFolderDir(QString("%1/%2").arg(basePathToUse).arg(mainFolder));
        for (const QString &channelFolder : channelFolders) {
            if (!mainFolderDir.exists(channelFolder)) {
                if (mainFolderDir.mkdir(channelFolder)) {
                    printf("成功创建 %s/%s/%s 文件夹\n", basePathToUse.toLocal8Bit().constData(), mainFolder.toLocal8Bit().constData(), channelFolder.toLocal8Bit().constData());
                } else {
                    printf("创建 %s/%s/%s 文件夹失败\n", basePathToUse.toLocal8Bit().constData(), mainFolder.toLocal8Bit().constData(), channelFolder.toLocal8Bit().constData());
                }
            } else {
                printf("%s/%s/%s 文件夹已存在\n", basePathToUse.toLocal8Bit().constData(), mainFolder.toLocal8Bit().constData(), channelFolder.toLocal8Bit().constData());
            }
        }
    }
    printf("创建文件夹结构完成\n");
}



// 初始化控制按钮显示/隐藏功能
void MainWindow::initControlButtonsVisibility()
{
    // 创建并设置定时器，用于检查鼠标位置
    m_mouseCheckTimer = new QTimer(this);
    connect(m_mouseCheckTimer, &QTimer::timeout, this, &MainWindow::checkMousePositionAndUpdateButtons);
    m_mouseCheckTimer->start(200); // 每200毫秒检查一次

    // 创建并设置定时器，用于延时隐藏控制按钮
    m_buttonHideTimer = new QTimer(this);
    m_buttonHideTimer->setSingleShot(true); // 单次触发
    connect(m_buttonHideTimer, &QTimer::timeout, this, &MainWindow::hideControlButtons);

    // 初始状态下根据摄像头状态设置控制按钮可见性
    updateControlButtonsVisibility();
}

// 检查当前通道的摄像头是否打开
bool MainWindow::isCameraOpen() const
{
    switch(curchannel) {
    case 0: // 第1页 - 摄像头1
    case 1: // 第2页 - 摄像头2
    case 2: // 第3页 - 摄像头3
    case 3: // 第4页 - 摄像头4
        return channelSettings[curchannel].isOpen;

    case 4: // 第5页 - 摄像头1+2组合
        return channelSettings[0].isOpen || channelSettings[1].isOpen;

    case 5: // 第6页 - 摄像头3+4组合
        return channelSettings[2].isOpen || channelSettings[3].isOpen;

    case 6: // 第7页 - 四宫格(1+2+3+4)
        return channelSettings[0].isOpen || channelSettings[1].isOpen ||
               channelSettings[2].isOpen || channelSettings[3].isOpen;

    default:
        return false;
    }
}


// 检查鼠标在哪个视频子区域，并返回区域索引和是否对应的摄像头开启
std::pair<int, bool> MainWindow::getCurrentMouseAreaState() const
{
    int areaIndex = -1;
    bool isCameraOpen = false;

    if (!ui->video->isVisible()) {
        return {-1, false};
    }

    QPoint mousePos = QCursor::pos();
    QPoint localPos = ui->video->mapFromGlobal(mousePos);

    if (!ui->video->rect().contains(localPos)) {
        return {-1, false};
    }

    int pageIndex = ui->stackedWidget_2->currentIndex();

    // 单摄像头页面
    if (pageIndex < 4) {
        return {0, channelSettings[pageIndex].isOpen};
    }
    // 双摄像头组合页面
    else if (pageIndex == 4) { // 1+2组合
        if (localPos.x() < ui->video->width() / 2) {
            return {1, channelSettings[0].isOpen}; // 左区域，摄像头1
        } else {
            return {2, channelSettings[1].isOpen}; // 右区域，摄像头2
        }
    }
    else if (pageIndex == 5) { // 3+4组合
        if (localPos.x() < ui->video->width() / 2) {
            return {1, channelSettings[2].isOpen}; // 左区域，摄像头3
        } else {
            return {2, channelSettings[3].isOpen}; // 右区域，摄像头4
        }
    }
    // 四宫格页面
    else if (pageIndex == 6) {
        bool left = localPos.x() < ui->video->width() / 2;
        bool top = localPos.y() < ui->video->height() / 2;

        if (top && left) return {1, channelSettings[0].isOpen};    // 左上，摄像头1
        if (top && !left) return {2, channelSettings[1].isOpen};   // 右上，摄像头2
        if (!top && left) return {3, channelSettings[2].isOpen};   // 左下，摄像头3
        return {4, channelSettings[3].isOpen};                     // 右下，摄像头4
    }

    return {-1, false};
}

// 检查鼠标位置并更新按钮显示状态的槽函数
void MainWindow::checkMousePositionAndUpdateButtons()
{
    static int lastAreaIndex = -1;
    auto [currentArea, isOpen] = getCurrentMouseAreaState();

    // 只有当区域状态变化时才更新UI
    if (currentArea != lastAreaIndex) {
        lastAreaIndex = currentArea;
        updateControlButtonsVisibility();

        // 处理定时器逻辑
        if (currentArea >= 0) {
            if (m_buttonHideTimer->isActive()) {
                m_buttonHideTimer->stop();
            }
        } else {
            m_buttonHideTimer->start(1000);
        }
    }
}

// 隐藏控制按钮的槽函数
void MainWindow::hideControlButtons()
{
    //m_mouseInVideoArea = false;
    updateControlButtonsVisibility();
}



void MainWindow::syncButtonGroups(QButtonGroup *source, QButtonGroup *target) {
    connect(source, QOverload<QAbstractButton *>::of(&QButtonGroup::buttonClicked),
            [=](QAbstractButton *button) {
                int id = source->id(button);
                if (target->button(id)) {  // 确保目标按钮存在
                    target->button(id)->setChecked(true);
                }
            });
}


void MainWindow::deviceinit(){
    // 初始化当前配置变量
    currentDevicePath = "";
    currentFormat = QVariantMap();
    currentResolution = QVariantMap();

    // 创建CameraParams对象
    cameraParams = new CameraParams(this);

    // 创建CameraStream对象
    cameraStream1 = new CameraStream(this);
    cameraStream2 = new CameraStream(this);
    cameraStream3 = new CameraStream(this);
    cameraStream4 = new CameraStream(this);
    systemmanager = new SystemManager(this);
    // 设置通道号
    cameraStream1->channelstream = 0;
    cameraStream2->channelstream = 1;
    cameraStream3->channelstream = 2;
    cameraStream4->channelstream = 3;

    // 连接GStreamer错误信号
    connect(cameraStream1, &CameraStream::gstreamerErrorOccurred, this, &MainWindow::handleGstreamerError);
    connect(cameraStream2, &CameraStream::gstreamerErrorOccurred, this, &MainWindow::handleGstreamerError);
    connect(cameraStream3, &CameraStream::gstreamerErrorOccurred, this, &MainWindow::handleGstreamerError);
    connect(cameraStream4, &CameraStream::gstreamerErrorOccurred, this, &MainWindow::handleGstreamerError);

    // 查找摄像头设备
    QList<QPair<QString, QString>> devices = cameraParams->findVideoDevices();
    for (const auto &device : devices) {
        ui->comboBox_cameradev->addItem(device.first, device.second);
    }

    // 添加rkisp_v5设备
    ui->comboBox_cameradev->addItem("rkisp_v5", "/dev/video0");

    // 如果有设备，初始化格式列表
    if (ui->comboBox_cameradev->count() > 0)
    {
        // 默认不选择任何设备，设置索引为 -1
        ui->comboBox_cameradev->addItem("关闭设备", "");
        ui->comboBox_cameradev->setCurrentIndex(-1);
    }
}

void MainWindow::timerinit(){
    // 创建定时器，用于检测新设备
    QTimer *deviceCheckTimer = new QTimer(this);
    connect(deviceCheckTimer, &QTimer::timeout, this, &MainWindow::checkForNewDevices);
    deviceCheckTimer->start(500); // 每500毫秒检查一次

    // 创建计时器
    m_elapsedTimer.start();

    QTimer *timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, &MainWindow::updateCpuUsage);
    connect(timer, &QTimer::timeout, this, &MainWindow::updatetimelabel);
    timer->start(1000); // 每秒更新一次
}

void MainWindow::updatetimelabel(){
    // 计算经过的时间（毫秒）
    qint64 elapsed = m_elapsedTimer.elapsed();

    // 转换为时分秒格式
    int seconds = (elapsed / 1000) % 60;
    int minutes = (elapsed / 60000) % 60;
    int hours = (elapsed / 3600000);

    // 更新标签文本
    QString runtimeText = systemmanager->getTranslatedText(
        "program_runtime_label",  // 翻译键
        currentLanguageIndex,     // 当前语言索引
        "mainwindow"           // 模块名
    );
    ui->label_time->setText(
        QString("%1 %2:%3:%4")
            .arg(runtimeText)
            .arg(hours, 2, 10, QLatin1Char('0'))
            .arg(minutes, 2, 10, QLatin1Char('0'))
            .arg(seconds, 2, 10, QLatin1Char('0'))
    );
}

void MainWindow::updateCpuUsage()
{
    QFile file("/proc/stat");
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        ui->label_cpu->setText("无法读取 CPU 使用率");
        return;
    }

    QTextStream in(&file);
    QString line = in.readLine(); // 读取第一行，通常是 "cpu" 行
    file.close();

    QStringList cpuTimes = line.split(' ', QString::SkipEmptyParts);

    if (cpuTimes.size() < 9) {
        ui->label_cpu->setText("CPU 数据格式错误");
        return;
    }

    // 解析当前的 CPU 时间
    int user = cpuTimes[1].toInt();
    int nice = cpuTimes[2].toInt();
    int system = cpuTimes[3].toInt();
    int idle = cpuTimes[4].toInt();
    int iowait = cpuTimes[5].toInt();
    int irq = cpuTimes[6].toInt();
    int softirq = cpuTimes[7].toInt();
    int steal = cpuTimes[8].toInt();

    // 计算总 CPU 时间和总空闲时间
    int totalCpuTime = user + nice + system + idle + iowait + irq + softirq + steal;
    int totalIdleTime = idle + iowait;

    int cpuUsage = 0;
    if (!prevCpuTimes.isEmpty()) {
        // 解析上一次的 CPU 时间
        int prevUser = prevCpuTimes[1].toInt();
        int prevNice = prevCpuTimes[2].toInt();
        int prevSystem = prevCpuTimes[3].toInt();
        int prevIdle = prevCpuTimes[4].toInt();
        int prevIowait = prevCpuTimes[5].toInt();
        int prevIrq = prevCpuTimes[6].toInt();
        int prevSoftirq = prevCpuTimes[7].toInt();
        int prevSteal = prevCpuTimes[8].toInt();

        // 计算上一次的总 CPU 时间和总空闲时间
        int prevTotalCpuTime = prevUser + prevNice + prevSystem + prevIdle + prevIowait + prevIrq + prevSoftirq + prevSteal;
        int prevTotalIdleTime = prevIdle + prevIowait;

        // 计算时间差
        int diffTotalCpuTime = totalCpuTime - prevTotalCpuTime;
        int diffTotalIdleTime = totalIdleTime - prevTotalIdleTime;

        // 防止分母为零
        if (diffTotalCpuTime > 0) {
            cpuUsage = 100 * (diffTotalCpuTime - diffTotalIdleTime) / diffTotalCpuTime;
            if (cpuUsage < 0) {
                cpuUsage = 0; // 防止负值
            }
        }
    }
    // 保存当前的 CPU 时间，用于下一次计算
    prevCpuTimes = cpuTimes;

    // 读取 GPU 使用率
    QFile gpuFile("/sys/class/devfreq/fde60000.gpu/load");
    if (!gpuFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        ui->label_gpu->setText("无法读取 GPU 使用率");
        return;
    }

    QTextStream gpuIn(&gpuFile);
    QString gpuLine = gpuIn.readLine();
    gpuFile.close();

    QStringList gpuParts = gpuLine.split('@');
    if (gpuParts.size() < 2) {
        ui->label_gpu->setText("GPU 数据格式错误");
        return;
    }

    int gpuUsage = gpuParts[0].toInt();
    QString gpuFrequency = gpuParts[1];

    // 使用/proc/meminfo文件计算内存使用率
    QFile file1("/proc/meminfo");
    if (!file1.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "无法打开/proc/meminfo文件:" << file1.errorString();
        ui->label_memory->setText("无法读取内存使用率");
        return;
    }

    QTextStream memIn(&file1);
    QString memContent = memIn.readAll();
    file1.close();

    // 解析内存信息
    long long memTotal = 0;
    long long memAvailable = 0;

    QStringList lines = memContent.split('\n');
    for (const QString &line : lines) {
        if (line.startsWith("MemTotal:")) {
            QStringList parts = line.split(QRegExp("\\s+"), QString::SkipEmptyParts);
            if (parts.size() >= 2) {
                memTotal = parts[1].toLongLong(); // 单位是 kB
            }
        } else if (line.startsWith("MemAvailable:")) {
            QStringList parts = line.split(QRegExp("\\s+"), QString::SkipEmptyParts);
            if (parts.size() >= 2) {
                memAvailable = parts[1].toLongLong(); // 单位是 kB
            }
        }

        // 如果两个值都找到了，可以提前退出循环
        if (memTotal > 0 && memAvailable > 0) {
            break;
        }
    }

    // 计算内存使用率：使用率 = (MemTotal - MemAvailable) / MemTotal * 100
    int memUsage = 0;
    if (memTotal > 0) {
        memUsage = static_cast<int>((memTotal - memAvailable) * 100 / memTotal);
        if (memUsage < 0) {
            memUsage = 0; // 防止负值
        } else if (memUsage > 100) {
            memUsage = 100; // 防止超过100%
        }
    }

    // 更新标签显示
    ui->label_cpu->setText(QString("CPU: %1%").arg(cpuUsage));
    ui->label_gpu->setText(QString("GPU: %1%").arg(gpuUsage));

    QString memoryText = systemmanager->getTranslatedText(
        "memory_usage_label",  // 翻译键
        currentLanguageIndex,  // 当前语言索引
        "mainwindow"         // 模块名（根据实际情况调整）
    );
    ui->label_memory->setText(QString("%1 %2%").arg(memoryText).arg(memUsage));
}

void MainWindow::showEvent(QShowEvent *event)
{
    QMainWindow::showEvent(event);

    //根据当前选择初始化摄像头
    // QTimer::singleShot(100, this, [this]() {
    //     int deviceIndex = ui->comboBox_cameradev->currentIndex();
    //     if (deviceIndex >= 0) {
    //         QString devicePath = ui->comboBox_cameradev->itemData(deviceIndex).toString();
    //         if (!devicePath.isEmpty()) {
    //             // 初始化摄像头
    //             openvideodevice(curchannel);
    //         }
    //     }
    // });
}

//视频设置
void MainWindow::VideoSetPage(){
    ui->stackedWidget->setCurrentIndex(0);
    // 如果当前有摄像头打开，检查参数支持情况
    // if (!currentDevicePath.isEmpty()) {
    //     initControl();
    // }

}
//图像设置
void MainWindow::PictureSetPage(){
    ui->stackedWidget->setCurrentIndex(1);
    // 如果当前有摄像头打开，检查参数支持情况
    // if (!currentDevicePath.isEmpty()) {
    //     initControl();
    // }
    // else{
    //     resetcontrol();
    // }
}
//相机控制
void MainWindow::CameraControlPage(){
    ui->stackedWidget->setCurrentIndex(2);
    // 如果当前有摄像头打开，检查参数支持情况
    // if (!currentDevicePath.isEmpty()) {
    //     initControl();
    // }
}
//文字设置
void MainWindow::TextSetPage(){
    ui->stackedWidget->setCurrentIndex(3);
    // 如果当前有摄像头打开，检查参数支持情况
    // if (!currentDevicePath.isEmpty()) {
    //     initControl();
    // }
}

void MainWindow::onCloseButtonClicked()
{
    for(int i = 0; i < 4; i ++)
    {
        if(isRecording[i] == true)
        {
            // 显示录像警告
            CustomMessageBox::warning(this,
                systemmanager->getTranslatedText("warning_title",currentLanguageIndex,"mainwindow"),
                systemmanager->getTranslatedText("recording_warning",currentLanguageIndex,"mainwindow"));
            //QMessageBox::warning(this, "警告", "有设备正在录像，禁止关闭！", QMessageBox::Ok);
            return;
        }
    }
    close();
}

void MainWindow::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton &&
        m_dragWidget->rect().contains(m_dragWidget->mapFromGlobal(event->globalPos())))
    {
        m_isDragging = true;
        m_dragPosition = event->globalPos() - frameGeometry().topLeft();
        //setCursor(Qt::SizeAllCursor); // 设置十字箭头光标
        event->accept();
    }
}

void MainWindow::mouseMoveEvent(QMouseEvent *event)
{
    if (m_isDragging && (event->buttons() & Qt::LeftButton)) // 检查左键是否仍按住
    {
        move(event->globalPos() - m_dragPosition);
        event->accept();
    }
}

void MainWindow::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton && m_isDragging)
    {
        m_isDragging = false;
        unsetCursor(); // 恢复默认光标
        event->accept();
    }
}

// 检查新设备

MainWindow::CurrentSelection MainWindow::saveCurrentSelection() {
    CurrentSelection selection;
    selection.deviceIndex = ui->comboBox_cameradev->currentIndex();
    if (selection.deviceIndex >= 0) {
        selection.devicePath = ui->comboBox_cameradev->itemData(selection.deviceIndex).toString();
        selection.deviceName = ui->comboBox_cameradev->itemText(selection.deviceIndex);
    }

    selection.formatIndex = ui->comboBox_videoformat->currentIndex();
    if (selection.formatIndex >= 0) {
        selection.format = ui->comboBox_videoformat->currentText();
    }

    return selection;
}

// 获取当前设备列表
QSet<QString> MainWindow::getCurrentDevicePaths() {
    QSet<QString> currentDevices;
    for (int i = 0; i < ui->comboBox_cameradev->count(); ++i) {
        QString devicePath = ui->comboBox_cameradev->itemData(i).toString();
        QString deviceName = ui->comboBox_cameradev->itemText(i);
        if (!devicePath.isEmpty() && deviceName != "rkisp_v5" && deviceName != "关闭设备") {
            currentDevices.insert(devicePath);
        }
    }
    return currentDevices;
}

MainWindow::DeviceChanges MainWindow::checkDeviceChanges(const QSet<QString>& currentDevices,
                                            const QSet<QString>& newDevicePaths,
                                            const CurrentSelection& selection) {
    DeviceChanges changes{false, false, false};

    // 检查当前选中设备是否被移除
    if (!selection.devicePath.isEmpty() && !newDevicePaths.contains(selection.devicePath) &&
        selection.deviceName != "rkisp_v5" && selection.deviceName != "关闭设备") {
        changes.currentRemoved = true;
    }

    // 检查是否有任何设备被移除
    for (const QString &devicePath : currentDevices) {
        if (!newDevicePaths.contains(devicePath)) {
            changes.anyRemoved = true;
            break;
        }
    }

    // 检查是否有新增设备
    for (const QString &devicePath : newDevicePaths) {
        if (!currentDevices.contains(devicePath)) {
            changes.newAdded = true;
            break;
        }
    }

    return changes;
}

// 处理当前设备被移除的情况
void MainWindow::handleCurrentDeviceRemoved(int channel) {
    try {

        // 保存热插拔数据
        //saveHotplugDataToIni(channel, iniPath);

        // 停止录像
        if (isRecording[channel]) {
            CameraStream* cameraStreams[4] = {cameraStream1, cameraStream2, cameraStream3, cameraStream4};
            if (channel >= 0 && channel < 4 && cameraStreams[channel]) {
                cameraStreams[channel]->stopRecording();
                isRecording[channel] = false;

                // 重置录像按钮
                resetRecordingButtons(channel);
            }
        }

        // // 保存热插拔数据
        saveHotplugDataToIni(channel, iniPath);

        // 清理资源
        clearWhichVideo(channel);

        // 重置通道设置
        resetChannelSettings(channel);

        //qDebug()<<"*********************************************************************************badaio"<<isRecording[channel];
        // 更新UI
        //updateCameraBackgrounds();
        //saveChannelSettingsToIni(channelSettings, iniPath);
        qDebug() << "curchannel" << channel << "beibale";
    } catch (...) {
        qDebug() << "处理当前设备移除时发生异常";
    }

}

// 重置通道设置
void MainWindow::resetChannelSettings(int channel) {
    channelSettings[channel].isOpen = false;
    channelSettings[channel].devicePath = "";
    channelSettings[channel].format.clear();
    channelSettings[channel].resolution.clear();
    channelSettings[channel].videoIndex = -1;
    channelSettings[channel].formatIndex = -1;
    channelSettings[channel].resolutionIndex = -1;
    channelSettings[channel].devicename = "";
    channelSettings[channel].FBLname = "";

    currentDevicePath = "";
    currentFormat = QVariantMap();
    currentResolution = QVariantMap();
}

// 重置录像按钮状态
void MainWindow::resetRecordingButtons(int channel) {
    static QIcon recordIcon(":/image/icons/录像.png");
    QVector<RecordingButtonData*>* targetButtons = nullptr;

    switch(channel + 1) {
        case 1: targetButtons = &camera1Buttons; break;
        case 2: targetButtons = &camera2Buttons; break;
        case 3: targetButtons = &camera3Buttons; break;
        case 4: targetButtons = &camera4Buttons; break;
    }

    if (targetButtons && !targetButtons->isEmpty()) {
        for (auto* buttonData : *targetButtons) {
            buttonData->button->setIcon(recordIcon);
            buttonData->isRecording = false;
            buttonData->timeLabel->setText("00:00:00");
        }
    }
}

// 检查并处理所有通道的设备移除
void MainWindow::checkAllChannelsForRemovedDevices(const QSet<QString>& newDevicePaths) {
    for (int channel = 0; channel < 4; ++channel) {
        // 只处理有设备连接的通道
        if (!channelSettings[channel].devicePath.isEmpty() && channelSettings[channel].isOpen) {
            bool deviceStillExists = false;

            // 检查该通道的设备是否仍然存在
            for (const QString &devicePath : newDevicePaths) {
                if (channelSettings[channel].devicePath == devicePath) {
                    deviceStillExists = true;
                    break;
                }
            }
        // 只有当设备不存在时才清理该通道
            if (!deviceStillExists) {
                qDebug() << "channel" << channel << "bachuqinli...";
                handleChannelCleanup(channel, "设备被拔出");
                // 如果是当前显示的通道，更新UI
                if (channel == curchannel) {
                    ui->comboBox_cameradev->setCurrentIndex(-1);
                    ui->comboBox_videoformat->clear();
                    ui->comboBox_fbl->clear();
                    ui->comboBox_audiodev->clear();  // 清空音频设备列表
                    updateAudioCheckboxState();  // 更新音频复选框状态
                    resetcontrol();
                    //updateCameraBackgrounds();
                }
            }
        }
    }
}

// 处理通道清理
void MainWindow::handleChannelCleanup(int channel, const QString& reason) {
    // 确保只清理指定通道
    if (channel < 0 || channel >= 4) return;

    try {
        // 保存热插拔数据
        saveHotplugDataToIni(channel, iniPath);

        // 停止该通道的录像
        if (isRecording[channel]) {
            qDebug() << "通道" << channel << "正在录像中，" << reason << "，先停止录像";
            CameraStream* cameraStreams[4] = {cameraStream1, cameraStream2, cameraStream3, cameraStream4};
            if (cameraStreams[channel]) {
                cameraStreams[channel]->stopRecording();
                isRecording[channel] = false;
                resetRecordingButtons(channel);
            }
        }

        //保存热插拔数据
        // saveHotplugDataToIni(channel, iniPath);
        // 清理该通道资源
        clearWhichVideo(channel);

        // 重置该通道设置
        channelSettings[channel].isOpen = false;
        channelSettings[channel].devicePath = "";
        channelSettings[channel].format.clear();
        channelSettings[channel].resolution.clear();
        channelSettings[channel].videoIndex = -1;
        channelSettings[channel].formatIndex = -1;
        channelSettings[channel].resolutionIndex = -1;
        channelSettings[channel].devicename = "";
        channelSettings[channel].FBLname = "";


        qDebug() << "channel" << channel << "finish" << reason;
    } catch (...) {
        qDebug() << "copechannel" << channel << "清理时发生异常";
    }
    saveChannelSettingsToIni(channelSettings,iniPath);
}

// 处理热插拔设备恢复
void MainWindow::handleHotplugRecovery(const QList<QPair<QString, QString>>& newDevices) {
    for (int channel = 0; channel < 4; ++channel) {
            qDebug()<<channel<<channelSettings[channel].isOpen;
        if (!channelSettings[channel].isOpen && loadHotplugDataFromIni(channel, iniPath)) {
            QString foundDevice = findMatchingVideoDevice(
                channelSettings[channel].unpluggedUsbInterface,
                channelSettings[channel].unpluggedProductVid
            );
            qDebug()<<channel<<foundDevice;
            if (!foundDevice.isEmpty()) {
                QString devicePath = "/dev/" + foundDevice;
                QString deviceName = "";
                qDebug()<<channel<<foundDevice;
                // 检查设备是否在新设备列表中
                bool deviceInNewList = false;
                for (const auto &device : newDevices) {
                    if (device.second == devicePath) {
                        deviceInNewList = true;
                        deviceName = device.first;
                        break;
                    }
                }

                if (deviceInNewList) {
                    recoverHotplugDevice(channel, devicePath, deviceName);
                }
            }
        }
    }
}

// 恢复热插拔设备
void MainWindow::recoverHotplugDevice(int channel, const QString& devicePath, const QString& deviceName) {
    // 关闭错误对话框
    if (errorDialogs.contains(channel) && errorDialogs[channel]) {
        errorDialogs[channel]->accept();
        errorDialogs.remove(channel);
    }

    // 恢复设备设置
    channelSettings[channel].isOpen = true;
    channelSettings[channel].devicePath = devicePath;
    channelSettings[channel].format = channelSettings[channel].unpluggedFormat;
    channelSettings[channel].resolution = channelSettings[channel].unpluggedResolution;
    channelSettings[channel].formatIndex = channelSettings[channel].unpluggedFormatIndex;
    channelSettings[channel].resolutionIndex = channelSettings[channel].unpluggedResolutionIndex;
    channelSettings[channel].devicename = channelSettings[channel].unpluggedDeviceName;
    channelSettings[channel].FBLname = channelSettings[channel].unpluggedFBLname;
    channelSettings[channel].UsbInterface = channelSettings[channel].unpluggedUsbInterface;
    channelSettings[channel].ProductVid = channelSettings[channel].unpluggedProductVid;

    // 恢复音频设备信息
    channelSettings[channel].audioDeviceName = channelSettings[channel].unpluggedAudioDeviceName;
    channelSettings[channel].audioDevicePath = channelSettings[channel].unpluggedAudioDevicePath;
    channelSettings[channel].audioDeviceIndex = channelSettings[channel].unpluggedAudioDeviceIndex;
    channelSettings[channel].audioEnabled = channelSettings[channel].unpluggedAudioEnabled;

    qDebug() << "检测到热插拔设备重新连接 - 通道:" << channel << "设备:" << deviceName;

    if (channel == curchannel) {
        QTimer::singleShot(1000, this, [this, channel, devicePath, deviceName]() {

            recoverCurrentChannel(channel, devicePath, deviceName);
            if(isRecording[channel]){
                    toggleRecordingForCamera(channel+1);
            }
            initControl();
        });

    } else {
        QTimer::singleShot(1000, this, [this, channel, devicePath]() {
            recoverBackgroundChannel(channel, devicePath);
            if(isRecording[channel]){
                    toggleRecordingForCamera(channel+1);
            }
        });
    }

    // 清理热插拔数据
    clearHotplugDataFromIni(channel, iniPath);
}

// 恢复当前通道
void MainWindow::recoverCurrentChannel(int channel, const QString& devicePath, const QString& deviceName) {
    // 更新当前设备信息
    currentDevicePath = devicePath;
    currentFormat = channelSettings[channel].format;
    currentResolution = channelSettings[channel].resolution;

    // 更新UI选择项
    for (int i = 0; i < ui->comboBox_cameradev->count(); ++i) {
        if (ui->comboBox_cameradev->itemData(i).toString() == devicePath) {
            ui->comboBox_cameradev->setCurrentIndex(i);
            break;
        }
    }

    // 更新视频格式列表
    ui->comboBox_videoformat->clear();
    QList<QPair<QString, QVariantMap>> formats = cameraParams->findVideoFormats(currentDevicePath);
    for (const auto &format : formats) {
        ui->comboBox_videoformat->addItem(format.first, format.second);
    }

    // 恢复格式选择
    if (channelSettings[channel].formatIndex >= 0) {
        ui->comboBox_videoformat->setCurrentIndex(channelSettings[channel].formatIndex);

        // 更新分辨率列表
        ui->comboBox_fbl->clear();
        uint32_t pixelformat = currentFormat["pixelformat"].toUInt();
        QList<QPair<QString, QVariantMap>> resolutions = cameraParams->findVideoResolutions(currentDevicePath, pixelformat);
        for (const auto &resolution : resolutions) {
            ui->comboBox_fbl->addItem(resolution.first, resolution.second);
        }

        // 恢复分辨率选择
        if (channelSettings[channel].resolutionIndex >= 0) {
            ui->comboBox_fbl->setCurrentIndex(channelSettings[channel].resolutionIndex);
        }
    }

    // 更新音频设备列表
    updateAudioDevicesForCamera(channelSettings[channel].UsbInterface);

    // 恢复音频设备选择
    if (!channelSettings[channel].audioDevicePath.isEmpty()) {
        for (int i = 0; i < ui->comboBox_audiodev->count(); ++i) {
            if (ui->comboBox_audiodev->itemData(i).toString() == channelSettings[channel].audioDevicePath) {
                ui->comboBox_audiodev->setCurrentIndex(i);
                break;
            }
        }
    }

    // 恢复音频启用状态
    curaudiostate[channel] = channelSettings[channel].audioEnabled;
    ui->checkBox_audio->setChecked(channelSettings[channel].audioEnabled);
    updateAudioCheckboxState();

    // 重新初始化摄像头
    openvideodevice(channel);
    //updateCameraBackgrounds();

    // 管理预览状态
    managePreviewStates(channel);

    saveChannelSettingsToIni(channelSettings, iniPath);
    qDebug() << "热插拔设备已重启并显示 - 通道:" << channel << "设备:" << deviceName;
}

// 恢复后台通道
void MainWindow::recoverBackgroundChannel(int channel, const QString& devicePath) {
    // 保存当前通道的设备信息
    QString originalCurrentDevicePath = currentDevicePath;
    QVariantMap originalCurrentFormat = currentFormat;
    QVariantMap originalCurrentResolution = currentResolution;

    // 临时设置为要恢复的通道设备信息
    currentDevicePath = devicePath;
    currentFormat = channelSettings[channel].format;
    currentResolution = channelSettings[channel].resolution;

    // 查找并设置设备索引
    for (int i = 0; i < ui->comboBox_cameradev->count(); ++i) {
        QString data = ui->comboBox_cameradev->itemData(i).toString();
        if (data.contains(devicePath)) {
            channelSettings[channel].videoIndex = i;
            break;
        }
    }

    // 重启该通道的摄像头
    openvideodevice(channel);
    //updateCameraBackgrounds();
    curdevicename[channel] = channelSettings[channel].devicename;
    curfblname[channel] = channelSettings[channel].FBLname;
    updateDeviceLabels(channel);
    updateCompositePageLabels(0,1,channelSettings[0].isOpen,channelSettings[1].isOpen);
    updateCompositePageLabels(2,3,channelSettings[2].isOpen,channelSettings[3].isOpen);
    updateGridPageLabels(0,1,2,3,channelSettings[0].isOpen,channelSettings[1].isOpen,channelSettings[2].isOpen,channelSettings[3].isOpen);

    // 暂停预览，使其不显示
    CameraStream* cameraStreams[4] = {cameraStream1, cameraStream2, cameraStream3, cameraStream4};
    if (channel >= 0 && channel < 4 && cameraStreams[channel]) {
        //qDebug()<<"chchchchchchchcch"<<channel;
        cameraStreams[channel]->pausePreview();
    }
    if(curchannel > 3){
        //qDebug()<<"e5lse555555";
        managePreviewStates(curchannel);
    }

    // 恢复当前通道的设备信息
    currentDevicePath = originalCurrentDevicePath;
    currentFormat = originalCurrentFormat;
    currentResolution = originalCurrentResolution;

    saveChannelSettingsToIni(channelSettings, iniPath);
    qDebug() << "后台通道热插拔设备已重启但不显示 - 通道:" << channel;
}

// 管理预览状态
void MainWindow::managePreviewStates(int channel) {
    CameraStream* cameraStreams[4] = {cameraStream1, cameraStream2, cameraStream3, cameraStream4};

    // 先暂停所有通道的预览
    for (int i = 0; i < 4; i++) {
        if (cameraStreams[i]) {
            cameraStreams[i]->pausePreview();
        }
    }
    if(isFullscreen){
        // 根据当前显示模式恢复需要显示的通道
        if (channel < 4) {
            // 单通道显示模式
            if (channel == curchannel && cameraStreams[channel]) {
                cameraStreams[channel]->setfullPreviewSize(channel);
                cameraStreams[channel]->resumePreview();
            }
        } else if (curchannel == 4) {
            // 双通道显示模式 (通道0和1)
            if (channelSettings[0].isOpen && cameraStreams[0]) {
                qDebug()<<"diwu0";
                cameraStreams[0]->setfullPreviewSize(4);
                cameraStreams[0]->resumePreview();
            }
            if (channelSettings[1].isOpen && cameraStreams[1]) {
                cameraStreams[1]->setfullPreviewSize(5);
                cameraStreams[1]->resumePreview();
            }
        } else if (curchannel == 5) {
            // 双通道显示模式 (通道2和3)
            if (channelSettings[2].isOpen && cameraStreams[2]) {
                cameraStreams[2]->setfullPreviewSize(6);
                cameraStreams[2]->resumePreview();
            }
            if (channelSettings[3].isOpen && cameraStreams[3]) {
                cameraStreams[3]->setfullPreviewSize(7);
                cameraStreams[3]->resumePreview();
            }
        } else if (curchannel == 6) {
            // 四通道显示模式
            for (int i = 0; i < 4; i++) {
                if (channelSettings[i].isOpen && cameraStreams[i]) {
                    cameraStreams[i]->setfullPreviewSize(8 + i);
                    cameraStreams[i]->resumePreview();
                }
            }
        }
        createFullscreenBackgrounds();
    }
    else{
        // 根据当前显示模式恢复需要显示的通道
        if (channel < 4) {
            // 单通道显示模式
            if (channel == curchannel && cameraStreams[channel]) {
                cameraStreams[channel]->setPreviewSize(channel);
                cameraStreams[channel]->resumePreview();
            }
        } else if (curchannel == 4) {
            // 双通道显示模式 (通道0和1)
            if (channelSettings[0].isOpen && cameraStreams[0]) {
                qDebug()<<"diwu0";
                cameraStreams[0]->setPreviewSize(4);
                cameraStreams[0]->resumePreview();
            }
            if (channelSettings[1].isOpen && cameraStreams[1]) {
                cameraStreams[1]->setPreviewSize(5);
                cameraStreams[1]->resumePreview();
            }
        } else if (curchannel == 5) {
            // 双通道显示模式 (通道2和3)
            if (channelSettings[2].isOpen && cameraStreams[2]) {
                cameraStreams[2]->setPreviewSize(6);
                cameraStreams[2]->resumePreview();
            }
            if (channelSettings[3].isOpen && cameraStreams[3]) {
                cameraStreams[3]->setPreviewSize(7);
                cameraStreams[3]->resumePreview();
            }
        } else if (curchannel == 6) {
            // 四通道显示模式
            for (int i = 0; i < 4; i++) {
                if (channelSettings[i].isOpen && cameraStreams[i]) {
                    cameraStreams[i]->setPreviewSize(8 + i);
                    cameraStreams[i]->resumePreview();
                }
            }
        }
        //createFullscreenBackgrounds();
    }
}

// 更新设备列表UI
void MainWindow::updateDeviceListUI(const CurrentSelection& selection, const QList<QPair<QString, QString>>& newDevices) {
    // 保存rkisp_v5的选中状态
    bool rkispSelected = (selection.deviceName == "rkisp_v5");
    int rkispIndex = -1;

    // 清空现有的设备列表
    ui->comboBox_cameradev->clear();

    // 添加新检测到的设备
    for (const auto &device : newDevices) {
        ui->comboBox_cameradev->addItem(device.first, device.second);
    }

    // 添加rkisp_v5设备
    rkispIndex = ui->comboBox_cameradev->count();
    ui->comboBox_cameradev->addItem("rkisp_v5", "/dev/video0");

    // 添加"关闭设备"选项
    if (ui->comboBox_cameradev->count() > 0) {
        QString closeDeviceText = systemmanager->getTranslatedText(
            "close_device",
            currentLanguageIndex,
            "mainwindow"  // 模块名，根据实际情况调整
        );

        ui->comboBox_cameradev->addItem(closeDeviceText, "");
    }

    //恢复rkisp_v5选择
    if (rkispSelected && rkispIndex >= 0) {
        ui->comboBox_cameradev->setCurrentIndex(rkispIndex);
        return;
    }

    // 尝试恢复之前选中的设备
    if (!selection.devicePath.isEmpty()) {
        for (int i = 0; i < ui->comboBox_cameradev->count(); ++i) {
            if (ui->comboBox_cameradev->itemData(i).toString() == selection.devicePath) {
                ui->comboBox_cameradev->setCurrentIndex(i);
                break;
            }
        }
    } else {
        ui->comboBox_cameradev->setCurrentIndex(-1);
        ui->comboBox_videoformat->clear();
        ui->comboBox_fbl->clear();
        ui->comboBox_audiodev->clear();  // 清空音频设备列表
        updateAudioCheckboxState();  // 更新音频复选框状态
    }

    // 更新格式列表
    if (ui->comboBox_cameradev->currentIndex() >= 0) {
        QString devicePath = ui->comboBox_cameradev->itemData(ui->comboBox_cameradev->currentIndex()).toString();

        if (devicePath == selection.devicePath && ui->comboBox_videoformat->count() > 0) {
            qDebug() << "格式列表未改变，尝试恢复格式选择";
            if (!selection.format.isEmpty()) {
                for (int i = 0; i < ui->comboBox_videoformat->count(); ++i) {
                    if (ui->comboBox_videoformat->itemText(i) == selection.format) {
                        ui->comboBox_videoformat->setCurrentIndex(i);
                        break;
                    }
                }
            }
        } else {
            // 重新获取格式列表
            QList<QPair<QString, QVariantMap>> formats;
            try {
                formats = cameraParams->findVideoFormats(devicePath);
            } catch (...) {
                qDebug() << "获取格式列表失败";
            }

            ui->comboBox_videoformat->clear();
            for (const auto &format : formats) {
                ui->comboBox_videoformat->addItem(format.first, format.second);
            }
            qDebug() << "更新格式列表，共" << formats.size() << "种格式";
        }
    }
}

// 主函数 - 检查新设备
void MainWindow::checkForNewDevices() {
    // 保存当前选中的设备信息
    CurrentSelection selection = saveCurrentSelection();

    // 如果是rkisp_v5设备，直接返回
    if (selection.deviceName == "rkisp_v5") {
        return;
    }

    // 获取当前所有设备的路径
    QSet<QString> currentDevices = getCurrentDevicePaths();

    // 获取新检测到的设备
    QList<QPair<QString, QString>> newDevices = cameraParams->findVideoDevices();
    QSet<QString> newDevicePaths;
    for (const auto &device : newDevices) {
        newDevicePaths.insert(device.second);
    }

    // 检查设备变化
    DeviceChanges changes = checkDeviceChanges(currentDevices, newDevicePaths, selection);

    // 当有任何设备变化时更新UI
    if (changes.currentRemoved || changes.anyRemoved || changes.newAdded) {
        // 处理当前设备被移除的情况
        // if (changes.currentRemoved) {
        //     qDebug()<<"dangqian";
        //     handleCurrentDeviceRemoved(curchannel);
        // }
            //qDebug()<<"feidangqian";
            // 检查并处理所有通道的设备移除
        checkAllChannelsForRemovedDevices(newDevicePaths);
        // 处理热插拔设备恢复
        if (changes.newAdded) {
            handleHotplugRecovery(newDevices);
        }

        // 更新设备列表UI
        updateDeviceListUI(selection, newDevices);
        if(!channelSettings[curchannel].isOpen){
            ui->comboBox_cameradev->setCurrentIndex(-1);
            ui->comboBox_videoformat->setCurrentIndex(-1);
            ui->comboBox_fbl->setCurrentIndex(-1);
        }
    }
}

// 根据摄像头的USB接口更新音频设备列表
void MainWindow::updateAudioDevicesForCamera(const QString &usbInterface)
{
    // 清空当前音频设备列表
    ui->comboBox_audiodev->clear();

    if (usbInterface.isEmpty()) {
            QString microphoneText = systemmanager->getTranslatedText(
                "microphone",
                currentLanguageIndex,
                "mainwindow"
            );
            ui->comboBox_audiodev->addItem(microphoneText, "hw:1");

            // 添加"关闭"选项（最后一个位置）
            QString audioOffText = systemmanager->getTranslatedText(
                "off",
                currentLanguageIndex,
                "mainwindow"
            );
            ui->comboBox_audiodev->addItem(audioOffText, "-1");

            // 设置为无选择状态
            ui->comboBox_audiodev->setCurrentIndex(-1);
            updateAudioCheckboxState();
            return;
    }

    // 查找该摄像头对应的音频设备
    QList<QPair<QString, QString>> audioDevices = cameraParams->findAudioDevicesForCamera(usbInterface);

    if (!audioDevices.isEmpty()) {
        // 添加找到的音频设备
        for (const auto &device : audioDevices) {
            ui->comboBox_audiodev->addItem(device.first, device.second);
        }
    }
    QString microphoneText = systemmanager->getTranslatedText(
        "microphone",
        currentLanguageIndex,
        "mainwindow"
    );
    ui->comboBox_audiodev->addItem(microphoneText, "hw:1");

    // 添加"关闭"选项（最后一个位置）
    QString audioOffText = systemmanager->getTranslatedText(
        "off",
        currentLanguageIndex,
        "mainwindow"
    );
    ui->comboBox_audiodev->addItem(audioOffText, "-1");

    // 设置为无选择状态
    // ui->comboBox_audiodev->setCurrentIndex(-1);
    // 恢复音频设备选择
    ui->comboBox_audiodev->setCurrentIndex(channelSettings[curchannel].audioDeviceIndex);
    // 更新音频复选框的启用状态
    updateAudioCheckboxState();
}

// 更新音频复选框的启用状态
void MainWindow::updateAudioCheckboxState()
{
    // 检查音频设备下拉列表是否有可选项
    bool hasAudioDevices = (ui->comboBox_audiodev->currentIndex()== -1 ? false : true);

    // 根据是否有音频设备来启用或禁用音频复选框
    ui->checkBox_audio->setEnabled(hasAudioDevices);

    // 如果没有音频设备，取消勾选音频复选框并更新状态
    if (!hasAudioDevices) {
        ui->checkBox_audio->setChecked(false);
        if(curchannel<4){
            curaudiostate[curchannel] = false;
        }
    }

}

void MainWindow::clearWhichVideo(int channel){
    if(channel==0){
        // 先停止视频流
        cameraStream1->cleanupGStreamer();
        // 等待一小段时间确保资源释放
        QApplication::processEvents();
        //channelSettings[channel].isOpen=false;
    }
    if(channel==1){
            cameraStream2->cleanupGStreamer();
            QApplication::processEvents();
            //channelSettings[channel].isOpen=false;
    }
    if(channel==2){
            cameraStream3->cleanupGStreamer();
            QApplication::processEvents();
            //channelSettings[channel].isOpen=false;
    }
    if(channel==3){
            cameraStream4->cleanupGStreamer();
            QApplication::processEvents();
            //channelSettings[channel].isOpen=false;
    }
}

void MainWindow::openvideodevice(int channel){
    QString devicename = ui->comboBox_cameradev->currentText();
    QString fblname = ui->comboBox_fbl->currentText();

    // 如果是rkisp_v5设备，确保UI界面正确显示所选格式和分辨率
    if (devicename == "rkisp_v5") {
        // 确保UI上显示正确的格式和分辨率
        if (ui->comboBox_videoformat->count() == 0) {
            // 如果格式为空，添加UYVY格式
            QVariantMap uyvyFormat;
            uyvyFormat["pixelformat"] = V4L2_PIX_FMT_UYVY;
            uyvyFormat["formatName"] = "UYVY";
            ui->comboBox_videoformat->addItem("UYVY", uyvyFormat);
        }

        if (ui->comboBox_fbl->count() == 0) {
            // 如果分辨率为空，添加1080p30分辨率
            QVariantMap res1080p30;
            res1080p30["width"] = 1920;
            res1080p30["height"] = 1080;
            res1080p30["framerate_num"] = 30;
            res1080p30["framerate_den"] = 1;
            ui->comboBox_fbl->addItem("1920x1080 @60fps", res1080p30);
        }

        // 更新选择项
        ui->comboBox_videoformat->setCurrentIndex(0);
        ui->comboBox_fbl->setCurrentIndex(0);

        // 更新格式名称
        fblname = ui->comboBox_fbl->currentText();
    }

    if(channel==0){
        cameraStream1->initializeGStreamer(currentDevicePath, currentFormat, currentResolution);
        // ui->video1->is_camera=true;
        curdevicename[0]=devicename;
        curfblname[0]=fblname;
    }
    if(channel==1){
        cameraStream2->initializeGStreamer(currentDevicePath, currentFormat, currentResolution);
        //ui->video2->is_camera=true;
        curdevicename[1]=devicename;
        curfblname[1]=fblname;
    }
    if(channel==2){
        cameraStream3->initializeGStreamer(currentDevicePath, currentFormat, currentResolution);
        //ui->video3->is_camera=true;
        curdevicename[2]=devicename;
        curfblname[2]=fblname;
    }
    if(channel==3){
        cameraStream4->initializeGStreamer(currentDevicePath, currentFormat, currentResolution);
        //ui->video4->is_camera=true;
        curdevicename[3]=devicename;
        curfblname[3]=fblname;
    }
    updateDeviceLabels(channel);
}

void MainWindow::select_video(int index)
{
    // 检查是否有效索引
    if (index < 0) {
        return;
    }

    // 获取新选择的设备路径
    QString newDevicePath = ui->comboBox_cameradev->itemData(index).toString();
    QString deviceName = ui->comboBox_cameradev->itemText(index);
    qDebug()<<"new:"<<newDevicePath<<"0:"<<channelSettings[0].devicePath;
    if (newDevicePath.isEmpty()) {

        clearWhichVideo(curchannel);

        // 将分辨率和格式的选项设置为 -1
        ui->comboBox_cameradev->setCurrentIndex(-1);
        ui->comboBox_videoformat->setCurrentIndex(-1);
        ui->comboBox_fbl->setCurrentIndex(-1);
        ui->comboBox_audiodev->setCurrentIndex(-1);  // 清空音频设备列表
        updateAudioCheckboxState();  // 更新音频复选框状态

        timewatermark[curchannel]=false;

        // 清空当前设备路径和配置
        currentDevicePath = "";
        currentFormat = QVariantMap();
        currentResolution = QVariantMap();

        // 保存当前通道设置
        channelSettings[curchannel].isOpen = false;
        channelSettings[curchannel].devicePath = "";
        channelSettings[curchannel].format.clear();
        channelSettings[curchannel].resolution.clear();
        channelSettings[curchannel].videoIndex = -1;
        channelSettings[curchannel].formatIndex = -1;
        channelSettings[curchannel].resolutionIndex = -1;

        saveChannelSettingsToIni(channelSettings, iniPath);
        resetcontrol();
        return;
    }

    // 检查其他通道是否已经打开了这个摄像头
        for (int i = 0; i < 4; i++) {
            qDebug()<<i<<channelSettings[i].isOpen<<"*******************";
            qDebug()<<i<<channelSettings[i].devicePath;

            if (i != curchannel && channelSettings[i].isOpen && channelSettings[i].devicePath == newDevicePath) {
                // 显示摄像头重复警告
                CustomMessageBox::warning(this,
                    systemmanager->getTranslatedText("warning_title",currentLanguageIndex,"mainwindow"),
                    systemmanager->getTranslatedText("camera_duplicate_warning",currentLanguageIndex,"mainwindow").arg(deviceName).arg(i+1));

                // 重置选择
                if(!channelSettings[curchannel].isOpen){
                    ui->comboBox_cameradev->setCurrentIndex(-1);
                    // ui->comboBox_videoformat->clear();
                    // ui->comboBox_fbl->clear();
                }else{
                    ui->comboBox_cameradev->setCurrentIndex(channelSettings[curchannel].videoIndex);
                }
                return;
            }
        }

    // 检查是否与当前设备相同
    if (newDevicePath == currentDevicePath) {
        return;
    }

    // 如果设备发生变化，关闭当前正在使用的摄像头
    //cameraStream->cleanupGStreamer();

    clearWhichVideo(curchannel);
    channelSettings[curchannel].isOpen=false;

    // 更新背景状态
    updateCameraBackgrounds();
    // 保存新设备路径
    currentDevicePath = newDevicePath;

    // 确保选中当前设备
    ui->comboBox_cameradev->setCurrentIndex(index);

    // 当选择新的摄像头设备时，更新视频格式列表
    ui->comboBox_videoformat->clear();

    // 如果选择的是rkisp_v5设备，添加特定的UYVY格式
    if (deviceName == "rkisp_v5") {
        // 创建UYVY格式数据
        QVariantMap uyvyFormat;
        uyvyFormat["pixelformat"] = V4L2_PIX_FMT_UYVY;
        uyvyFormat["formatName"] = "UYVY";

        // 添加到格式下拉菜单
        ui->comboBox_videoformat->addItem("UYVY", uyvyFormat);

        // 直接选择UYVY格式
        ui->comboBox_videoformat->setCurrentIndex(0);
        currentFormat = uyvyFormat;

        // 更新分辨率列表
        ui->comboBox_fbl->clear();

        // 创建1080p30分辨率数据 (使用30fps，与命令行参数匹配)
        QVariantMap res1080p30;
        res1080p30["width"] = 1920;
        res1080p30["height"] = 1080;
        res1080p30["framerate_num"] = 30;  // 30fps, 对应命令: gst-launch-1.0 v4l2src device=/dev/video0 ! 'video/x-raw,width=1920,height=1080,framerate=30/1' ! appsink
        res1080p30["framerate_den"] = 1;

        // 添加到分辨率下拉菜单
        ui->comboBox_fbl->addItem("1920x1080 @60fps", res1080p30);

        // 直接选择1080p30分辨率
        ui->comboBox_fbl->setCurrentIndex(0);
        currentResolution = res1080p30;
    } else {
        // 对于其他设备，正常查询支持的格式
        QList<QPair<QString, QVariantMap>> formats = cameraParams->findVideoFormats(currentDevicePath);
        for (const auto &format : formats) {
            ui->comboBox_videoformat->addItem(format.first, format.second);
            //qDebug()<<format;
        }

        // 尝试找到MJPEG格式和1080p分辨率
        selectPreferredFormat();
    }

    // 启动视频预览
    QTimer::singleShot(100, this, [this, index]()
    {
        // 确保UI上显示正确的选择
        ui->comboBox_cameradev->setCurrentIndex(index);
        // if (ui->comboBox_videoformat->count() > 0)
        //     ui->comboBox_videoformat->setCurrentIndex(0);
        // if (ui->comboBox_fbl->count() > 0)
        //     ui->comboBox_fbl->setCurrentIndex(0);

        // 初始化摄像头
        openvideodevice(curchannel);

        // 初始化控制参数
        QTimer::singleShot(200, this, &MainWindow::initControl);
    });
    QTimer::singleShot(800, this, [this]()
                       {
        // // 更新背景状态
        // updateCameraBackgrounds();
        if (curchannel >= 0 && curchannel < 4) {
            // 保存当前通道是否打开
            channelSettings[curchannel].isOpen = !currentDevicePath.isEmpty();
            // 保存当前设备路径和参数
            if (channelSettings[curchannel].isOpen) {
                channelSettings[curchannel].devicePath = currentDevicePath;
                //qDebug()<<"path"<<channelSettings[curchannel].devicePath;
                channelSettings[curchannel].format = currentFormat;
                channelSettings[curchannel].resolution = currentResolution;
                // 保存当前所选的索引
                channelSettings[curchannel].videoIndex = ui->comboBox_cameradev->currentIndex();
                channelSettings[curchannel].formatIndex = ui->comboBox_videoformat->currentIndex();
                channelSettings[curchannel].resolutionIndex = ui->comboBox_fbl->currentIndex();
                //保存设备名和分辨率
                channelSettings[curchannel].devicename=ui->comboBox_cameradev->currentText();
                channelSettings[curchannel].FBLname=ui->comboBox_fbl->currentText();

                // 保存音频设备信息
                if (ui->comboBox_audiodev->currentIndex() >= 0) {
                    channelSettings[curchannel].audioDeviceName = ui->comboBox_audiodev->currentText();
                    channelSettings[curchannel].audioDevicePath = ui->comboBox_audiodev->currentData().toString();
                    channelSettings[curchannel].audioDeviceIndex = ui->comboBox_audiodev->currentIndex();
                } else {
                    channelSettings[curchannel].audioDeviceName = "";
                    channelSettings[curchannel].audioDevicePath = "";
                    channelSettings[curchannel].audioDeviceIndex = -1;
                }
                channelSettings[curchannel].audioEnabled = curaudiostate[curchannel];

                // 获取USB接口信息
                QString videoName = extractOddVideoName(currentDevicePath);
                if (!videoName.isEmpty()) {
                    QString usbInterface = findUsbInterfaceForVideo(videoName);
                    if (!usbInterface.isEmpty()) {
                        channelSettings[curchannel].UsbInterface = usbInterface;
                        channelSettings[curchannel].ProductVid = getProductVidFromInterface(usbInterface);

                        // 更新音频设备列表
                        updateAudioDevicesForCamera(usbInterface);
                        qDebug() << "摄像头" << curchannel << "USB接口:" << usbInterface << "已更新音频设备列表";
                    }
                }

                saveChannelSettingsToIni(channelSettings, iniPath);
            }
            // else
            // {
            //     updateControlButtonsVisibility();
            // }
        }
    });

}




void MainWindow::select_format(int index)
{

    // 首先检查所选择的格式索引是否有效
    if (index < 0) {
        qWarning("无效的格式索引");
        return;
    }

    // 获取新选择的格式
    QVariantMap newFormatData = ui->comboBox_videoformat->itemData(index).toMap();
    QString formatName = ui->comboBox_videoformat->currentText();
    qDebug() << "newFormatSelected:" << formatName;

    // 检查是否与当前格式相同
    bool formatChanged = true;
    if (!currentFormat.isEmpty() && !newFormatData.isEmpty()) {
        // 比较pixelformat
        if (currentFormat["pixelformat"].toUInt() == newFormatData["pixelformat"].toUInt()) {
            formatChanged = false;
        }
    }

    // 如果格式没有改变，则不需要重新打开摄像头
    if (!formatChanged) {
        return;
    }

    clearWhichVideo(curchannel);
    channelSettings[curchannel].isOpen=false;
    //updateCameraBackgrounds();

    // 保存新格式 (添加格式名称)
    currentFormat = newFormatData;
    currentFormat["formatName"] = formatName;

    //qDebug()<<"path:"<<currentDevicePath;
    // 当选择新的视频格式时，更新分辨率列表
    ui->comboBox_fbl->clear();
    uint32_t pixelformat = currentFormat["pixelformat"].toUInt();
    QList<QPair<QString, QVariantMap>> resolutions = cameraParams->findVideoResolutions(currentDevicePath, pixelformat);
    for (const auto &resolution : resolutions) {
        ui->comboBox_fbl->addItem(resolution.first, resolution.second);
    }
    if (!resolutions.isEmpty()) {
        // 选择第一个分辨率
        ui->comboBox_fbl->setCurrentIndex(0);
        currentResolution = resolutions.first().second;
    }

    // 重新启动视频预览
    QTimer::singleShot(100, this, [this, formatName]() {
        // 在定时器回调中再次检查当前选择的格式
        QString currentSelectedFormat = ui->comboBox_videoformat->currentText();
        if (currentSelectedFormat != formatName) {
            //qDebug() << "warning:TheFormatHasChanged!OriginalFormat:" << formatName << "，currentFormat:" << currentSelectedFormat;
        }

        // 初始化摄像头
        openvideodevice(curchannel);

        // QString curformatname = ui->comboBox_videoformat->currentText();
        // ui->video1->codecValueLabel->setText(curformatname);

        // channelSettings[curchannel].format = currentFormat;
        // channelSettings[curchannel].formatIndex = ui->comboBox_videoformat->currentIndex();
        // channelSettings[curchannel].resolution = currentResolution;
        // channelSettings[curchannel].formatIndex = ui->comboBox_fbl->currentIndex();

    });
    QTimer::singleShot(800, this, [this]()
                       {
                           // 更新背景状态
                           //updateCameraBackgrounds();

                       });
    if (curchannel >= 0 && curchannel < 4) {
        // 保存当前通道是否打开
        channelSettings[curchannel].isOpen = !currentDevicePath.isEmpty();
        // 保存当前设备路径和参数
        if (channelSettings[curchannel].isOpen) {
            channelSettings[curchannel].devicePath = currentDevicePath;
            qDebug()<<"path"<<channelSettings[curchannel].devicePath;
            channelSettings[curchannel].format = currentFormat;
            channelSettings[curchannel].resolution = currentResolution;
            // 保存当前所选的索引
            channelSettings[curchannel].videoIndex = ui->comboBox_cameradev->currentIndex();
            channelSettings[curchannel].formatIndex = ui->comboBox_videoformat->currentIndex();
            channelSettings[curchannel].resolutionIndex = ui->comboBox_fbl->currentIndex();
            //保存设备名和分辨率
            channelSettings[curchannel].devicename=ui->comboBox_cameradev->currentText();
            channelSettings[curchannel].FBLname=ui->comboBox_fbl->currentText();

            saveChannelSettingsToIni(channelSettings, iniPath);
        }
    }


    //QString iniPath = "/data/camera_settings.ini";

}


void MainWindow::select_resolution(int index)
{
    // 首先检查所选择的分辨率索引是否有效
    if (index < 0) {
        qWarning("无效的分辨率索引");
        return;
    }

    // 获取新选择的分辨率
    QVariantMap newResolutionData = ui->comboBox_fbl->itemData(index).toMap();
    qDebug()<<newResolutionData;

    // 检查是否与当前分辨率相同
    bool resolutionChanged = true;
    if (!currentResolution.isEmpty() && !newResolutionData.isEmpty()) {
        // 比较宽度、高度和帧率
        bool widthHeightSame = (currentResolution["width"].toInt() == newResolutionData["width"].toInt()) &&
                               (currentResolution["height"].toInt() == newResolutionData["height"].toInt());

        bool framerateSame = true;
        if (currentResolution.contains("framerate_num") && newResolutionData.contains("framerate_num") &&
            currentResolution.contains("framerate_den") && newResolutionData.contains("framerate_den")) {

            framerateSame = (currentResolution["framerate_num"].toInt() == newResolutionData["framerate_num"].toInt()) &&
                            (currentResolution["framerate_den"].toInt() == newResolutionData["framerate_den"].toInt());
        }

        if (widthHeightSame && framerateSame) {
            resolutionChanged = false;
        }
    }

    // 如果分辨率和帧率没有改变，则不需要重新打开摄像头
    if (!resolutionChanged) {
        return;
    }

    clearWhichVideo(curchannel);

    channelSettings[curchannel].isOpen=false;
    //updateCameraBackgrounds();

    // 保存新分辨率
    currentResolution = newResolutionData;

    // 重新启动视频预览
    QTimer::singleShot(100, this, [this]() {
        // 初始化摄像头
        openvideodevice(curchannel);

        // QString curfblname=ui->comboBox_fbl->currentText();
        // ui->video1->resolutionValueLabel->setText(curfblname);


        // 保存当前通道分辨率设置
        channelSettings[curchannel].resolution = currentResolution;
        channelSettings[curchannel].resolutionIndex = ui->comboBox_fbl->currentIndex();

        //保存设备名和分辨率
        channelSettings[curchannel].devicename=ui->comboBox_cameradev->currentText();
        channelSettings[curchannel].FBLname=ui->comboBox_fbl->currentText();

        // 保存音频设备信息
        if (ui->comboBox_audiodev->currentIndex() >= 0) {
            channelSettings[curchannel].audioDeviceName = ui->comboBox_audiodev->currentText();
            channelSettings[curchannel].audioDevicePath = ui->comboBox_audiodev->currentData().toString();
            channelSettings[curchannel].audioDeviceIndex = ui->comboBox_audiodev->currentIndex();
        } else {
            channelSettings[curchannel].audioDeviceName = "";
            channelSettings[curchannel].audioDevicePath = "";
            channelSettings[curchannel].audioDeviceIndex = -1;
        }
        channelSettings[curchannel].audioEnabled = curaudiostate[curchannel];

        //QString iniPath = "/data/camera_settings.ini";
        saveChannelSettingsToIni(channelSettings, iniPath);
    });

    QTimer::singleShot(800, this, [this]()
                       {
                           // 更新背景状态
                           //updateCameraBackgrounds();

                       });
    if (curchannel >= 0 && curchannel < 4) {
        // 保存当前通道是否打开
        channelSettings[curchannel].isOpen = !currentDevicePath.isEmpty();
        // 保存当前设备路径和参数
        if (channelSettings[curchannel].isOpen) {
            channelSettings[curchannel].devicePath = currentDevicePath;
            qDebug()<<"path"<<channelSettings[curchannel].devicePath;
            channelSettings[curchannel].format = currentFormat;
            channelSettings[curchannel].resolution = currentResolution;
            // 保存当前所选的索引
            channelSettings[curchannel].videoIndex = ui->comboBox_cameradev->currentIndex();
            channelSettings[curchannel].formatIndex = ui->comboBox_videoformat->currentIndex();
            channelSettings[curchannel].resolutionIndex = ui->comboBox_fbl->currentIndex();

            //保存设备名和分辨率
            channelSettings[curchannel].devicename=ui->comboBox_cameradev->currentText();
            channelSettings[curchannel].FBLname=ui->comboBox_fbl->currentText();


        }
    }

}

void MainWindow::select_audio(int index)
{
    QString devicePath = ui->comboBox_audiodev->currentData().toString();
    QString deviceName = ui->comboBox_audiodev->currentText();
    // 处理"关闭"选项
    if (devicePath == "-1") {
        channelSettings[curchannel].audioDeviceName = "";
        channelSettings[curchannel].audioDevicePath = "";
        channelSettings[curchannel].audioDeviceIndex = -1;
        ui->comboBox_audiodev->setCurrentIndex(channelSettings[curchannel].audioDeviceIndex);
        saveChannelSettingsToIni(channelSettings, iniPath);
        updateAudioCheckboxState();
        return;
    }
    // 处理"麦克风"选项 - 检查冲突
    if (devicePath == "hw:1") {
        // 检查其他通道是否已经使用了麦克风
        for (int i = 0; i < 4; i++) {
            if (i != curchannel && channelSettings[i].audioDevicePath == "hw:1") {
                // 发现冲突，直接返回不操作，不显示弹窗
                // 恢复之前的选择
                if (!channelSettings[curchannel].audioDevicePath.isEmpty()) {
                    for (int j = 0; j < ui->comboBox_audiodev->count(); ++j) {
                        if (ui->comboBox_audiodev->itemData(j).toString() == channelSettings[curchannel].audioDevicePath) {
                            ui->comboBox_audiodev->setCurrentIndex(j);
                            return;
                        }
                    }
                } else {
                    ui->comboBox_audiodev->setCurrentIndex(-1);
                }
                return;
            }
        }
    }
    // 保存音频设备选择到当前通道设置
    if (curchannel >= 0 && curchannel < 4) {
        if (index >= 0) {
            channelSettings[curchannel].audioDeviceName = ui->comboBox_audiodev->currentText();
            channelSettings[curchannel].audioDevicePath = ui->comboBox_audiodev->currentData().toString();
            channelSettings[curchannel].audioDeviceIndex = index;
        } else {
            channelSettings[curchannel].audioDeviceName = "";
            channelSettings[curchannel].audioDevicePath = "";
            channelSettings[curchannel].audioDeviceIndex = -1;
        }

        // 保存到INI文件
        saveChannelSettingsToIni(channelSettings, iniPath);
    }
    updateAudioCheckboxState();

}


void MainWindow::selectPreferredFormat()
{
    // 获取当前格式列表和分辨率列表
    QList<QPair<QString, QVariantMap>> formats;
    for (int i = 0; i < ui->comboBox_videoformat->count(); i++) {
        formats.append(qMakePair(ui->comboBox_videoformat->itemText(i),
                                 ui->comboBox_videoformat->itemData(i).toMap()));
    }

    // 如果没有格式，则返回
    if (formats.isEmpty()) {
        return;
    }

    // 查找并选择MJPEG格式
    int formatIndex = -1;
    // 调用CameraParams来选择首选格式
    if (ui->comboBox_videoformat->count() > 0) {
        // 选择首选格式索引
        for (int i = 0; i < ui->comboBox_videoformat->count(); i++) {
            QString formatName = ui->comboBox_videoformat->itemText(i);
            if (formatName.toUpper() == "MJPG" || formatName.toUpper() == "JPEG") {
                formatIndex = i;
                break;
            }
        }

        // 如果找到MJPEG格式，选择它
        if (formatIndex >= 0) {
            ui->comboBox_videoformat->setCurrentIndex(formatIndex);
            currentFormat = ui->comboBox_videoformat->itemData(formatIndex).toMap();
        } else if (ui->comboBox_videoformat->count() > 0) {
            // 否则选择第一个格式
            ui->comboBox_videoformat->setCurrentIndex(0);
            currentFormat = ui->comboBox_videoformat->itemData(0).toMap();
        }
    }

    // 更新分辨率列表
    ui->comboBox_fbl->clear();
    if (!currentFormat.isEmpty()) {
        uint32_t pixelformat = currentFormat["pixelformat"].toUInt();
        QList<QPair<QString, QVariantMap>> resolutions = cameraParams->findVideoResolutions(currentDevicePath, pixelformat);
        for (const auto &resolution : resolutions) {
            ui->comboBox_fbl->addItem(resolution.first, resolution.second);
        }
    }

    // 选择首选分辨率
    int resolutionIndex = -1;
    // 找出1080p分辨率和30fps最接近的选项
    float bestFpsMatch = 100.0f;
    for (int i = 0; i < ui->comboBox_fbl->count(); i++) {
        QVariantMap resData = ui->comboBox_fbl->itemData(i).toMap();
        if (resData["width"].toInt() == 1920 && resData["height"].toInt() == 1080) {
            if (resData.contains("framerate_num") && resData.contains("framerate_den")) {
                float fps = (float)resData["framerate_num"].toInt() / resData["framerate_den"].toInt();
                float fpsDiff = qAbs(fps - 30.0f);
                if (fpsDiff < bestFpsMatch) {
                    bestFpsMatch = fpsDiff;
                    resolutionIndex = i;
                }
            } else if (resolutionIndex == -1) {
                resolutionIndex = i;
            }
        }
    }

    // 如果没有找到1080p的分辨率，选择任何可用的
    if (resolutionIndex == -1 && ui->comboBox_fbl->count() > 0) {
        resolutionIndex = 0;
    }

    // 如果找到合适的分辨率，则选择它
    if (resolutionIndex >= 0) {
        ui->comboBox_fbl->setCurrentIndex(resolutionIndex);
        currentResolution = ui->comboBox_fbl->itemData(resolutionIndex).toMap();
        //qDebug() << "fbl: " << ui->comboBox_fbl->itemText(resolutionIndex);
    }
}

void MainWindow::select_brightness()
{
    updateCameraParameter(sender(), ui->horizontalSlider_brightness, ui->spinBox_brightness, V4L2_CID_BRIGHTNESS);
}
void MainWindow::select_contrast()
{
    updateCameraParameter(sender(), ui->horizontalSlider_contrast, ui->spinBox_contrast, V4L2_CID_CONTRAST);
}
void MainWindow::select_saturation()
{
    updateCameraParameter(sender(), ui->horizontalSlider_saturation, ui->spinBox_saturation, V4L2_CID_SATURATION);
}
void MainWindow::select_hue()
{
    updateCameraParameter(sender(), ui->horizontalSlider_hue, ui->spinBox_hue, V4L2_CID_HUE);
}
void MainWindow::select_sharpness()
{
    updateCameraParameter(sender(), ui->horizontalSlider_sharpness, ui->spinBox_sharpness, V4L2_CID_SHARPNESS);
}
void MainWindow::select_gamma()
{
    updateCameraParameter(sender(), ui->horizontalSlider_gamma, ui->spinBox_gamma, V4L2_CID_GAMMA);
}
void MainWindow::select_backlightcompensation()
{
    updateCameraParameter(sender(), ui->horizontalSlider_backlightcompensation, ui->spinBox_backlightcompensation, V4L2_CID_BACKLIGHT_COMPENSATION);
}
void MainWindow::select_gain()
{
    updateCameraParameter(sender(), ui->horizontalSlider_gain, ui->spinBox_gain, V4L2_CID_GAIN);
}
void MainWindow::select_whitebalance()
{
    updateCameraParameter(sender(), ui->horizontalSlider_whitebalance, ui->spinBox_whitebalance, V4L2_CID_WHITE_BALANCE_TEMPERATURE);
}
void MainWindow::select_whitebalance_auto()
{
    // 获取复选框的当前状态
    Qt::CheckState state = ui->checkBox_whitebalance->checkState();
    bool isAuto = (state == Qt::Checked);

    if (isAuto) {
        // 复选框被选中，启用自动白平衡
        qDebug() << "setcheckBox_whitebalance";
        ui->horizontalSlider_whitebalance->setEnabled(false);
        ui->spinBox_whitebalance->setEnabled(false);

        // 设置自动白平衡
        if (cameraParams->setAutoWhiteBalance(currentDevicePath, true)) {

        } else {
            qWarning() << "auto white balance error";
            // 如果设置失败，恢复复选框状态
            ui->checkBox_whitebalance->setChecked(false);
            ui->horizontalSlider_whitebalance->setEnabled(true);
            ui->spinBox_whitebalance->setEnabled(true);
        }
    } else {
        // 复选框未被选中，禁用自动白平衡
        qDebug() << "unsetcheckBox_whitebalance";
        ui->horizontalSlider_whitebalance->setEnabled(true);
        ui->spinBox_whitebalance->setEnabled(true);

        // 设置手动白平衡
        if (cameraParams->setAutoWhiteBalance(currentDevicePath, false)) {

            // 获取当前白平衡值并设置到滑动条和文本框
            int min = 0, max = 100, step = 1, defaultValue = 50;
            if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_WHITE_BALANCE_TEMPERATURE, min, max, step, defaultValue)) {
                qDebug() << "setwhitebalance:"<<defaultValue;
                select_whitebalance();
                ui->horizontalSlider_whitebalance->setValue(defaultValue);
                ui->spinBox_whitebalance->setValue(defaultValue);
            }
        } else {
            qWarning() << "manual white balance error";
            // 如果设置失败，恢复复选框状态
            ui->checkBox_whitebalance->setChecked(true);
            ui->horizontalSlider_whitebalance->setEnabled(false);
            ui->spinBox_whitebalance->setEnabled(false);
        }
    }
}

void MainWindow::select_exposure()
{
    updateCameraParameter(sender(), ui->horizontalSlider_exposure, ui->spinBox_exposure, V4L2_CID_EXPOSURE_ABSOLUTE);
}
void MainWindow::select_exposure_auto()
{
    // 获取复选框的当前状态
    Qt::CheckState state = ui->checkBox_exposure->checkState();
    bool isAuto = (state == Qt::Checked);

    if (isAuto) {
        // 复选框被选中，启用自动曝光
        qDebug() << "setcheckBox_exposure";
        ui->horizontalSlider_exposure->setEnabled(false);
        ui->spinBox_exposure->setEnabled(false);

        // 设置自动曝光
        if (cameraParams->setAutoExposure(currentDevicePath, true)) {
        } else {
            qWarning() << "auto exposure error";
            // 如果设置失败，恢复复选框状态
            ui->checkBox_exposure->setChecked(false);
            ui->horizontalSlider_exposure->setEnabled(true);
            ui->spinBox_exposure->setEnabled(true);
        }
    } else {
        // 复选框未被选中，禁用自动曝光
        qDebug() << "unsetcheckBox_exposure";
        ui->horizontalSlider_exposure->setEnabled(true);
        ui->spinBox_exposure->setEnabled(true);

        // 设置手动曝光
        if (cameraParams->setAutoExposure(currentDevicePath, false)) {

            // 获取当前曝光值并设置到滑动条和文本框
            int min = 0, max = 100, step = 1, defaultValue = 50;
            if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_EXPOSURE_ABSOLUTE, min, max, step, defaultValue)) {
                qDebug() << "setexposure:  "<<defaultValue;
                select_exposure();
                ui->horizontalSlider_exposure->setValue(defaultValue);
                ui->spinBox_exposure->setValue(defaultValue);
            }
        } else {
            qWarning() << "manual exposure error";
            // 如果设置失败，恢复复选框状态
            ui->checkBox_exposure->setChecked(true);
            ui->horizontalSlider_exposure->setEnabled(false);
            ui->spinBox_exposure->setEnabled(false);
        }
    }
}

void MainWindow::select_focus()
{
    updateCameraParameter(sender(), ui->horizontalSlider_focus, ui->spinBox_focus, V4L2_CID_FOCUS_ABSOLUTE);
}

void MainWindow::select_focus_auto()
{
    // 获取复选框的当前状态
    Qt::CheckState state = ui->checkBox_focus->checkState();
    bool isAuto = (state == Qt::Checked);

    if (isAuto) {
        // 复选框被选中，启用自动焦点
        ui->horizontalSlider_focus->setEnabled(false);
        ui->spinBox_focus->setEnabled(false);

        // 设置自动焦点
        if (cameraParams->setAutoFocus(currentDevicePath, true)) {
        } else {
            qWarning() << "auto focus error";
            // 如果设置失败，恢复复选框状态
            ui->checkBox_focus->setChecked(false);
            ui->horizontalSlider_focus->setEnabled(true);
            ui->spinBox_focus->setEnabled(true);
        }
    } else {
        // 复选框未被选中，禁用自动焦点
        ui->horizontalSlider_focus->setEnabled(true);
        ui->spinBox_focus->setEnabled(true);

        // 设置手动焦点
        if (cameraParams->setAutoFocus(currentDevicePath, false)) {

            // 获取当前焦点值并设置到滑动条和文本框
            int min = 0, max = 100, step = 1, defaultValue = 50;
            if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_FOCUS_ABSOLUTE, min, max, step, defaultValue)) {
                ui->horizontalSlider_focus->setValue(defaultValue);
                ui->spinBox_focus->setValue(defaultValue);
            }
        } else {
            qWarning() << "manual focus error";
            // 如果设置失败，恢复复选框状态
            ui->checkBox_focus->setChecked(true);
            ui->horizontalSlider_focus->setEnabled(false);
            ui->spinBox_focus->setEnabled(false);
        }
    }
}
void MainWindow::select_zoom()
{
    updateCameraParameter(sender(), ui->horizontalSlider_zoom, ui->spinBox_zoom, V4L2_CID_ZOOM_ABSOLUTE);
}
void MainWindow::select_pan()
{
    updateCameraParameter(sender(), ui->horizontalSlider_pan, ui->spinBox_pan, V4L2_CID_PAN_ABSOLUTE);
}
void MainWindow::select_tilt()
{
    updateCameraParameter(sender(), ui->horizontalSlider_tilt, ui->spinBox_tilt, V4L2_CID_TILT_ABSOLUTE);
}
void MainWindow::set_default1()
{
    // 获取当前设备路径
    if (currentDevicePath.isEmpty()) {
        return;
    }

    int min = 0, max = 100, step = 1, defaultValue = 50;

    // 恢复亮度默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_BRIGHTNESS, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_BRIGHTNESS, min, max, step, defaultValue)) {
            ui->horizontalSlider_brightness->setValue(defaultValue);
            ui->spinBox_brightness->setValue(defaultValue);
            cameraParams->setControl(currentDevicePath, V4L2_CID_BRIGHTNESS, defaultValue);
        }
        ui->horizontalSlider_brightness->setEnabled(true);
        ui->spinBox_brightness->setEnabled(true);
    } else {
        // 禁用亮度控件
        ui->horizontalSlider_brightness->setEnabled(false);
        ui->spinBox_brightness->setEnabled(false);
    }

    // 恢复对比度默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_CONTRAST, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_CONTRAST, min, max, step, defaultValue)) {
            ui->horizontalSlider_contrast->setValue(defaultValue);
            ui->spinBox_contrast->setValue(defaultValue);
            cameraParams->setControl(currentDevicePath, V4L2_CID_CONTRAST, defaultValue);
        }
        ui->horizontalSlider_contrast->setEnabled(true);
        ui->spinBox_contrast->setEnabled(true);
    } else {
        // 禁用对比度控件
        ui->horizontalSlider_contrast->setEnabled(false);
        ui->spinBox_contrast->setEnabled(false);
    }

    // 恢复饱和度默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_SATURATION, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_SATURATION, min, max, step, defaultValue)) {
            ui->horizontalSlider_saturation->setValue(defaultValue);
            ui->spinBox_saturation->setValue(defaultValue);
            cameraParams->setControl(currentDevicePath, V4L2_CID_SATURATION, defaultValue);
        }
        ui->horizontalSlider_saturation->setEnabled(true);
        ui->spinBox_saturation->setEnabled(true);
    } else {
        // 禁用饱和度控件
        ui->horizontalSlider_saturation->setEnabled(false);
        ui->spinBox_saturation->setEnabled(false);
    }

    // 恢复色调默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_HUE, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_HUE, min, max, step, defaultValue)) {
            ui->horizontalSlider_hue->setValue(defaultValue);
            ui->spinBox_hue->setValue(defaultValue);
            cameraParams->setControl(currentDevicePath, V4L2_CID_HUE, defaultValue);
        }
        ui->horizontalSlider_hue->setEnabled(true);
        ui->spinBox_hue->setEnabled(true);
    } else {
        // 禁用色调控件
        ui->horizontalSlider_hue->setEnabled(false);
        ui->spinBox_hue->setEnabled(false);
    }

    // 恢复清晰度默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_SHARPNESS, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_SHARPNESS, min, max, step, defaultValue)) {
            ui->horizontalSlider_sharpness->setValue(defaultValue);
            ui->spinBox_sharpness->setValue(defaultValue);
            cameraParams->setControl(currentDevicePath, V4L2_CID_SHARPNESS, defaultValue);
        }
        ui->horizontalSlider_sharpness->setEnabled(true);
        ui->spinBox_sharpness->setEnabled(true);
    } else {
        // 禁用清晰度控件
        ui->horizontalSlider_sharpness->setEnabled(false);
        ui->spinBox_sharpness->setEnabled(false);
    }

    // 恢复伽马默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_GAMMA, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_GAMMA, min, max, step, defaultValue)) {
            ui->horizontalSlider_gamma->setValue(defaultValue);
            ui->spinBox_gamma->setValue(defaultValue);
            cameraParams->setControl(currentDevicePath, V4L2_CID_GAMMA, defaultValue);
        }
        ui->horizontalSlider_gamma->setEnabled(true);
        ui->spinBox_gamma->setEnabled(true);
    } else {
        // 禁用伽马控件
        ui->horizontalSlider_gamma->setEnabled(false);
        ui->spinBox_gamma->setEnabled(false);
    }

    // 恢复逆光对比默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_BACKLIGHT_COMPENSATION, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_BACKLIGHT_COMPENSATION, min, max, step, defaultValue)) {
            ui->horizontalSlider_backlightcompensation->setValue(defaultValue);
            ui->spinBox_backlightcompensation->setValue(defaultValue);
            cameraParams->setControl(currentDevicePath, V4L2_CID_BACKLIGHT_COMPENSATION, defaultValue);
        }
        ui->horizontalSlider_backlightcompensation->setEnabled(true);
        ui->spinBox_backlightcompensation->setEnabled(true);
    } else {
        // 禁用逆光对比控件
        ui->horizontalSlider_backlightcompensation->setEnabled(false);
        ui->spinBox_backlightcompensation->setEnabled(false);
    }

    // 恢复增益默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_GAIN, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_GAIN, min, max, step, defaultValue)) {
            ui->horizontalSlider_gain->setValue(defaultValue);
            ui->spinBox_gain->setValue(defaultValue);
            cameraParams->setControl(currentDevicePath, V4L2_CID_GAIN, defaultValue);
        }
        ui->horizontalSlider_gain->setEnabled(true);
        ui->spinBox_gain->setEnabled(true);
    } else {
        // 禁用增益控件
        ui->horizontalSlider_gain->setEnabled(false);
        ui->spinBox_gain->setEnabled(false);
    }

    // 恢复白平衡默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_WHITE_BALANCE_TEMPERATURE, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_WHITE_BALANCE_TEMPERATURE, min, max, step, defaultValue)) {
            if(!ui->checkBox_whitebalance->isChecked()){
                ui->horizontalSlider_whitebalance->setValue(defaultValue);
                ui->spinBox_whitebalance->setValue(defaultValue);
                ui->horizontalSlider_whitebalance->setEnabled(true);
                ui->spinBox_whitebalance->setEnabled(true);
            }
            cameraParams->setControl(currentDevicePath, V4L2_CID_WHITE_BALANCE_TEMPERATURE, defaultValue);
        }
        ui->checkBox_whitebalance->setEnabled(true);

    } else {
        // 禁用白平衡控件
        ui->horizontalSlider_whitebalance->setEnabled(false);
        ui->spinBox_whitebalance->setEnabled(false);
        ui->checkBox_whitebalance->setEnabled(false);
    }
}

void MainWindow::set_default2()
{
    // 恢复第二组参数的默认设置
    // 获取当前设备路径
    if (currentDevicePath.isEmpty()) {
        return;
    }

    int min = 0, max = 100, step = 1, defaultValue = 50;

    // 恢复曝光默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_EXPOSURE_ABSOLUTE, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_EXPOSURE_ABSOLUTE, min, max, step, defaultValue)) {
            if(!ui->checkBox_exposure->isChecked()){
                ui->horizontalSlider_exposure->setValue(defaultValue);
                ui->spinBox_exposure->setValue(defaultValue);
                ui->horizontalSlider_exposure->setEnabled(true);
                ui->spinBox_exposure->setEnabled(true);
            }
            cameraParams->setControl(currentDevicePath, V4L2_CID_EXPOSURE_ABSOLUTE, defaultValue);
        }

        ui->checkBox_exposure->setEnabled(true);
    } else {
        // 禁用曝光控件
        ui->horizontalSlider_exposure->setEnabled(false);
        ui->spinBox_exposure->setEnabled(false);
        ui->checkBox_exposure->setEnabled(false);
    }

    // 恢复焦点默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_FOCUS_ABSOLUTE, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_FOCUS_ABSOLUTE, min, max, step, defaultValue)) {
            if(!ui->checkBox_focus->isChecked()){
                ui->horizontalSlider_focus->setValue(defaultValue);
                ui->spinBox_focus->setValue(defaultValue);
                ui->horizontalSlider_focus->setEnabled(true);
                ui->spinBox_focus->setEnabled(true);
            }
            cameraParams->setControl(currentDevicePath, V4L2_CID_FOCUS_ABSOLUTE, defaultValue);
        }

        ui->checkBox_focus->setEnabled(true);
    } else {
        // 禁用焦点控件
        ui->horizontalSlider_focus->setEnabled(false);
        ui->spinBox_focus->setEnabled(false);
        ui->checkBox_focus->setEnabled(false);
    }

    // 恢复缩放默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_ZOOM_ABSOLUTE, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_ZOOM_ABSOLUTE, min, max, step, defaultValue)) {
            ui->horizontalSlider_zoom->setValue(defaultValue);
            ui->spinBox_zoom->setValue(defaultValue);
            cameraParams->setControl(currentDevicePath, V4L2_CID_ZOOM_ABSOLUTE, defaultValue);
        }
        ui->horizontalSlider_zoom->setEnabled(true);
        ui->spinBox_zoom->setEnabled(true);
    } else {
        // 禁用缩放控件
        ui->horizontalSlider_zoom->setEnabled(false);
        ui->spinBox_zoom->setEnabled(false);
    }

    // 恢复全景默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_PAN_ABSOLUTE, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_PAN_ABSOLUTE, min, max, step, defaultValue)) {
            ui->horizontalSlider_pan->setValue(defaultValue);
            ui->spinBox_pan->setValue(defaultValue);
            cameraParams->setControl(currentDevicePath, V4L2_CID_PAN_ABSOLUTE, defaultValue);
        }
        ui->horizontalSlider_pan->setEnabled(true);
        ui->spinBox_pan->setEnabled(true);
    } else {
        // 禁用全景控件
        ui->horizontalSlider_pan->setEnabled(false);
        ui->spinBox_pan->setEnabled(false);
    }

    // 恢复倾斜默认设置
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_TILT_ABSOLUTE, min, max, step, defaultValue)) {
        if (cameraParams->getdefault(currentDevicePath, V4L2_CID_TILT_ABSOLUTE, min, max, step, defaultValue)) {
            ui->horizontalSlider_tilt->setValue(defaultValue);
            ui->spinBox_tilt->setValue(defaultValue);
            cameraParams->setControl(currentDevicePath, V4L2_CID_TILT_ABSOLUTE, defaultValue);
        }
        ui->horizontalSlider_tilt->setEnabled(true);
        ui->spinBox_tilt->setEnabled(true);
    } else {
        // 禁用倾斜控件
        ui->horizontalSlider_tilt->setEnabled(false);
        ui->spinBox_tilt->setEnabled(false);
    }
}


// 初始化控制
void MainWindow::initControl()
{
    // 检查是否有选中的设备
    if (currentDevicePath.isEmpty())
    {
        return;
    }
    int min = 0, max = 100, step = 1, defaultValue = 50;
    //亮度
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_BRIGHTNESS, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_brightness,ui->spinBox_brightness,min, max, step, defaultValue);
        ui->horizontalSlider_brightness->setEnabled(true);
        ui->spinBox_brightness->setEnabled(true);
    }
    else
    {
        // 禁用亮度控件
        ui->horizontalSlider_brightness->setEnabled(false);
        ui->spinBox_brightness->setEnabled(false);
    }

    //对比度
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_CONTRAST, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_contrast,ui->spinBox_contrast,min, max, step, defaultValue);
        ui->horizontalSlider_contrast->setEnabled(true);
        ui->spinBox_contrast->setEnabled(true);
    }
    else
    {
        // 禁用对比度控件
        ui->horizontalSlider_contrast->setEnabled(false);
        ui->spinBox_contrast->setEnabled(false);
    }

    //饱和度
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_SATURATION, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_saturation,ui->spinBox_saturation,min, max, step, defaultValue);
        ui->horizontalSlider_saturation->setEnabled(true);
        ui->spinBox_saturation->setEnabled(true);
    }
    else
    {
        // 禁用饱和度控件
        ui->horizontalSlider_saturation->setEnabled(false);
        ui->spinBox_saturation->setEnabled(false);
    }

    //色调
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_HUE, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_hue,ui->spinBox_hue,min, max, step, defaultValue);
        ui->horizontalSlider_hue->setEnabled(true);
        ui->spinBox_hue->setEnabled(true);
    }
    else
    {
        // 禁用色调控件
        ui->horizontalSlider_hue->setEnabled(false);
        ui->spinBox_hue->setEnabled(false);
    }

    //清晰度
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_SHARPNESS, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_sharpness,ui->spinBox_sharpness,min, max, step, defaultValue);
        ui->horizontalSlider_sharpness->setEnabled(true);
        ui->spinBox_sharpness->setEnabled(true);
    }
    else
    {
        // 禁用清晰度控件
        ui->horizontalSlider_sharpness->setEnabled(false);
        ui->spinBox_sharpness->setEnabled(false);
    }

    //伽马
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_GAMMA, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_gamma,ui->spinBox_gamma,min, max, step, defaultValue);
        ui->horizontalSlider_gamma->setEnabled(true);
        ui->spinBox_gamma->setEnabled(true);
    }
    else
    {
        // 禁用伽马控件
        ui->horizontalSlider_gamma->setEnabled(false);
        ui->spinBox_gamma->setEnabled(false);
    }

    //逆光对比
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_BACKLIGHT_COMPENSATION, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_backlightcompensation,ui->spinBox_backlightcompensation,min, max, step, defaultValue);
        ui->horizontalSlider_backlightcompensation->setEnabled(true);
        ui->spinBox_backlightcompensation->setEnabled(true);
    }
    else
    {
        // 禁用逆光对比控件
        ui->horizontalSlider_backlightcompensation->setEnabled(false);
        ui->spinBox_backlightcompensation->setEnabled(false);
    }

    //增益
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_GAIN, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_gain,ui->spinBox_gain,min, max, step, defaultValue);
        ui->horizontalSlider_gain->setEnabled(true);
        ui->spinBox_gain->setEnabled(true);
    }
    else
    {
        // 禁用增益控件
        ui->horizontalSlider_gain->setEnabled(false);
        ui->spinBox_gain->setEnabled(false);
    }

    //白平衡
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_WHITE_BALANCE_TEMPERATURE, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_whitebalance,ui->spinBox_whitebalance,min, max, step, defaultValue);
        ui->horizontalSlider_whitebalance->setEnabled(true);
        ui->spinBox_whitebalance->setEnabled(true);
        ui->checkBox_whitebalance->setEnabled(true);
    }
    else
    {

        // 禁用白平衡控件
        ui->horizontalSlider_whitebalance->setEnabled(false);
        ui->spinBox_whitebalance->setEnabled(false);
        ui->checkBox_whitebalance->setEnabled(false);
    }

    // 初始化自动白平衡状态
    bool isAutoWhiteBalance = false;
    if (cameraParams->getAutoWhiteBalance(currentDevicePath, isAutoWhiteBalance))
    {
        // 设置复选框状态
        ui->checkBox_whitebalance->setChecked(isAutoWhiteBalance);

        // 根据自动白平衡状态设置滑动条和文本框的启用/禁用状态
        ui->horizontalSlider_whitebalance->setEnabled(!isAutoWhiteBalance);
        ui->spinBox_whitebalance->setEnabled(!isAutoWhiteBalance);
    }
    else
    {
        // 禁用白平衡复选框
        ui->checkBox_whitebalance->setEnabled(false);
    }

    //缩放
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_ZOOM_ABSOLUTE, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_zoom,ui->spinBox_zoom,min, max, step, defaultValue);
        ui->horizontalSlider_zoom->setEnabled(true);
        ui->spinBox_zoom->setEnabled(true);
    }
    else
    {
        // 禁用缩放控件
        ui->horizontalSlider_zoom->setEnabled(false);
        ui->spinBox_zoom->setEnabled(false);
    }

    //全景
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_PAN_ABSOLUTE, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_pan,ui->spinBox_pan,min, max, step, defaultValue);
        ui->horizontalSlider_pan->setEnabled(true);
        ui->spinBox_pan->setEnabled(true);
    }
    else
    {
        // 禁用全景控件
        ui->horizontalSlider_pan->setEnabled(false);
        ui->spinBox_pan->setEnabled(false);
    }

    //倾斜
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_TILT_ABSOLUTE, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_tilt,ui->spinBox_tilt,min, max, step, defaultValue);
        ui->horizontalSlider_tilt->setEnabled(true);
        ui->spinBox_tilt->setEnabled(true);
    }
    else
    {
        // 禁用倾斜控件
        ui->horizontalSlider_tilt->setEnabled(false);
        ui->spinBox_tilt->setEnabled(false);
    }

    //曝光
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_EXPOSURE_ABSOLUTE, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_exposure,ui->spinBox_exposure,min, max, step, defaultValue);
        ui->horizontalSlider_exposure->setEnabled(true);
        ui->spinBox_exposure->setEnabled(true);
        ui->checkBox_exposure->setEnabled(true);
    }
    else
    {
        // 禁用曝光控件
        ui->horizontalSlider_exposure->setEnabled(false);
        ui->spinBox_exposure->setEnabled(false);
        ui->checkBox_exposure->setEnabled(false);
    }

    // 初始化自动曝光状态
    bool isAutoExposure = false;
    if (cameraParams->getAutoExposure(currentDevicePath, isAutoExposure))
    {
        // 设置复选框状态
        ui->checkBox_exposure->setChecked(isAutoExposure);

        // 根据自动曝光状态设置滑动条和文本框的启用/禁用状态
        ui->horizontalSlider_exposure->setEnabled(!isAutoExposure);
        ui->spinBox_exposure->setEnabled(!isAutoExposure);
    }
    else
    {
        // 禁用自动曝光复选框
        ui->checkBox_exposure->setEnabled(false);
    }

    // 焦点
    if (cameraParams->getControlRange(currentDevicePath, V4L2_CID_FOCUS_ABSOLUTE, min, max, step, defaultValue))
    {
        setuiCameraParam(ui->horizontalSlider_focus, ui->spinBox_focus, min, max, step, defaultValue);
        ui->horizontalSlider_focus->setEnabled(true);
        ui->spinBox_focus->setEnabled(true);
        ui->checkBox_focus->setEnabled(true);
    }
    else
    {
        // 禁用焦点控件
        ui->horizontalSlider_focus->setEnabled(false);
        ui->spinBox_focus->setEnabled(false);
        ui->checkBox_focus->setEnabled(false);
    }

    // 初始化自动焦点状态
    bool isAutoFocus = false;
    if (cameraParams->getAutoFocus(currentDevicePath, isAutoFocus))
    {
        // 设置复选框状态
        ui->checkBox_focus->setChecked(isAutoFocus);

        // 根据自动焦点状态设置滑动条和文本框的启用/禁用状态
        ui->horizontalSlider_focus->setEnabled(!isAutoFocus);
        ui->spinBox_focus->setEnabled(!isAutoFocus);
    }
    else
    {
        // 禁用自动焦点复选框
        ui->checkBox_focus->setEnabled(false);
    }
}

void MainWindow::resetcontrol(){
    int newMin=0;
    // 禁用亮度控件
    ui->horizontalSlider_brightness->setMinimum(newMin);
    ui->spinBox_brightness->setMinimum(newMin);
    ui->horizontalSlider_brightness->setValue(newMin);
    ui->spinBox_brightness->setValue(newMin);
    ui->horizontalSlider_brightness->setEnabled(false);
    ui->spinBox_brightness->setEnabled(false);

    // 禁用对比度控件
    ui->horizontalSlider_contrast->setMinimum(newMin);
    ui->spinBox_contrast->setMinimum(newMin);
    ui->horizontalSlider_contrast->setValue(newMin);
    ui->spinBox_contrast->setValue(newMin);
    ui->horizontalSlider_contrast->setEnabled(false);
    ui->spinBox_contrast->setEnabled(false);

    // 禁用饱和度控件
    ui->horizontalSlider_saturation->setMinimum(newMin);
    ui->spinBox_saturation->setMinimum(newMin);
    ui->horizontalSlider_saturation->setValue(newMin);
    ui->spinBox_saturation->setValue(newMin);
    ui->horizontalSlider_saturation->setEnabled(false);
    ui->spinBox_saturation->setEnabled(false);
    // 禁用色调控件
    ui->horizontalSlider_hue->setMinimum(newMin);
    ui->spinBox_hue->setMinimum(newMin);
    ui->horizontalSlider_hue->setValue(newMin);
    ui->spinBox_hue->setValue(newMin);
    ui->horizontalSlider_hue->setEnabled(false);
    ui->spinBox_hue->setEnabled(false);
    // 禁用清晰度控件
    ui->horizontalSlider_sharpness->setMinimum(newMin);
    ui->spinBox_sharpness->setMinimum(newMin);
    ui->horizontalSlider_sharpness->setValue(newMin);
    ui->spinBox_sharpness->setValue(newMin);
    ui->horizontalSlider_sharpness->setEnabled(false);
    ui->spinBox_sharpness->setEnabled(false);
    // 禁用伽马控件
    ui->horizontalSlider_gamma->setMinimum(newMin);
    ui->spinBox_gamma->setMinimum(newMin);
    ui->horizontalSlider_gamma->setValue(newMin);
    ui->spinBox_gamma->setValue(newMin);
    ui->horizontalSlider_gamma->setEnabled(false);
    ui->spinBox_gamma->setEnabled(false);
    // 禁用逆光对比控件
    ui->horizontalSlider_backlightcompensation->setMinimum(newMin);
    ui->spinBox_backlightcompensation->setMinimum(newMin);
    ui->horizontalSlider_backlightcompensation->setValue(newMin);
    ui->spinBox_backlightcompensation->setValue(newMin);
    ui->horizontalSlider_backlightcompensation->setEnabled(false);
    ui->spinBox_backlightcompensation->setEnabled(false);
    // 禁用增益控件
    ui->horizontalSlider_gain->setMinimum(newMin);
    ui->spinBox_gain->setMinimum(newMin);
    ui->horizontalSlider_gain->setValue(newMin);
    ui->spinBox_gain->setValue(newMin);
    ui->horizontalSlider_gain->setEnabled(false);
    ui->spinBox_gain->setEnabled(false);
    // 禁用白平衡控件
    ui->horizontalSlider_whitebalance->setMinimum(newMin);
    ui->spinBox_whitebalance->setMinimum(newMin);
    ui->horizontalSlider_whitebalance->setValue(ui->horizontalSlider_whitebalance->minimum());
    ui->spinBox_whitebalance->setValue(0);
    ui->horizontalSlider_whitebalance->setEnabled(false);
    ui->spinBox_whitebalance->setEnabled(false);
    ui->checkBox_whitebalance->setEnabled(false);
    // 禁用缩放控件
    ui->horizontalSlider_zoom->setMinimum(newMin);
    ui->spinBox_zoom->setMinimum(newMin);
    ui->horizontalSlider_zoom->setValue(newMin);
    ui->spinBox_zoom->setValue(newMin);
    ui->horizontalSlider_zoom->setEnabled(false);
    ui->spinBox_zoom->setEnabled(false);
    // 禁用全景控件
    ui->horizontalSlider_pan->setMinimum(newMin);
    ui->spinBox_pan->setMinimum(newMin);
    ui->horizontalSlider_pan->setValue(ui->horizontalSlider_pan->minimum());
    ui->spinBox_pan->setValue(newMin);
    ui->horizontalSlider_pan->setEnabled(false);
    ui->spinBox_pan->setEnabled(false);
    // 禁用倾斜控件
    ui->horizontalSlider_tilt->setMinimum(newMin);
    ui->spinBox_tilt->setMinimum(newMin);
    ui->horizontalSlider_tilt->setValue(ui->horizontalSlider_tilt->minimum());
    ui->spinBox_tilt->setValue(newMin);
    ui->horizontalSlider_tilt->setEnabled(false);
    ui->spinBox_tilt->setEnabled(false);
    // 禁用曝光控件
    ui->horizontalSlider_exposure->setMinimum(newMin);
    ui->spinBox_exposure->setMinimum(newMin);
    ui->horizontalSlider_exposure->setValue(ui->horizontalSlider_exposure->minimum());
    ui->spinBox_exposure->setValue(newMin);
    ui->horizontalSlider_exposure->setEnabled(false);
    ui->spinBox_exposure->setEnabled(false);
    ui->checkBox_exposure->setEnabled(false);
    // 禁用焦点控件
    ui->horizontalSlider_focus->setMinimum(newMin);
    ui->spinBox_focus->setMinimum(newMin);
    ui->horizontalSlider_focus->setValue(ui->horizontalSlider_focus->minimum());
    ui->spinBox_focus->setValue(newMin);
    ui->horizontalSlider_focus->setEnabled(false);
    ui->spinBox_focus->setEnabled(false);
    ui->checkBox_focus->setEnabled(false);
}

//更新摄像头参数
void MainWindow::updateCameraParameter(QObject* senderObj, QSlider* slider, QSpinBox* spinBox, int controlId)
{
    // 如果没有选中设备，直接返回
    if (currentDevicePath.isEmpty()) {
        return;
    }

    int value;
    // 如果是滑动条触发的
    if (senderObj == slider)
    {
        value = slider->value();

        // 更新文本框（避免触发 textEdited 信号）
        spinBox->blockSignals(true);
        spinBox->setValue(value);
        spinBox->blockSignals(false);
    }
    // 如果是文本框触发的
    else if (senderObj == spinBox)
    {
        bool ok;
        value = spinBox->text().toInt(&ok);

        // 检查值是否有效以及是否在滑动条的范围内
        if (ok && value >= slider->minimum() && value <= slider->maximum())
        {
            // 更新滑动条（避免触发 valueChanged 信号）
            slider->blockSignals(true);
            slider->setValue(value);
            slider->blockSignals(false);
        }
        else
        {
            // 值无效或超出范围，恢复为滑动条的当前值
            spinBox->blockSignals(true);
            spinBox->setValue(slider->value());
            spinBox->blockSignals(false);
            return; // 无效值，直接返回
        }
    }
    else
    {
        value = slider->value();
        // 设置摄像头参数
        cameraParams->setControl(currentDevicePath, controlId, value);
        return; // 不是滑动条或文本框触发，直接返回
    }

    // 设置摄像头参数
    cameraParams->setControl(currentDevicePath, controlId, value);
}
void MainWindow::setuiCameraParam(QSlider* slider, QSpinBox* spinBox,int &min, int &max, int &step, int &defaultValue)
{
    slider->setMinimum(min);
    slider->setMaximum(max);
    slider->setSingleStep(step);

    spinBox->setMinimum(min);
    spinBox->setMaximum(max);
    spinBox->setSingleStep(step);

    slider->setValue(defaultValue);
}


void MainWindow::createFullscreenBackgrounds()
{
    clearFullscreenBackgrounds();

    QScreen *screen = QGuiApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    const int screenWidth = screenGeometry.width();
    const int screenHeight = screenGeometry.height();
    const int lineWidth = 2;
    const QColor lineColor(Qt::white);

    auto createDividerLine = [&](bool vertical, int pos) -> QWidget* {
        QWidget* line = new QWidget(nullptr, Qt::Window | Qt::FramelessWindowHint);
        line->setStyleSheet(QString("background-color: %1;").arg(lineColor.name()));
        line->setAttribute(Qt::WA_DeleteOnClose);
        line->setProperty("isDividerLine", true);

        if (vertical) {
            line->setGeometry(pos - lineWidth/2, 0, lineWidth, screenHeight);
        } else {
            line->setGeometry(0, pos - lineWidth/2, screenWidth, lineWidth);
        }
        return line;
    };



    switch(curchannel) {
    case 0: case 1: case 2: case 3: { // 单画面
        if (!channelSettings[curchannel].isOpen) {
            QWidget* bg = new QWidget(nullptr, Qt::Window | Qt::FramelessWindowHint);
            bg->setStyleSheet("background-color: black;");
            bg->setGeometry(screenGeometry);
            bg->setWindowState(Qt::WindowFullScreen);
            bg->setAttribute(Qt::WA_DeleteOnClose);
            fullscreenBgWindows.append(bg);
        }
        break;
    }
    case 4: { // 双画面1+2
        // 左侧背景
        if (!channelSettings[0].isOpen) {
            QWidget* bg = new QWidget(nullptr, Qt::Window | Qt::FramelessWindowHint);
            bg->setStyleSheet("background-color: black;");
            bg->setGeometry(0, 1, screenWidth/2, screenHeight);
            bg->setAttribute(Qt::WA_DeleteOnClose);
            fullscreenBgWindows.append(bg);
            qDebug()<<screenWidth/2<<screenHeight;
        }
        // 右侧背景
        if (!channelSettings[1].isOpen) {
            QWidget* bg = new QWidget(nullptr, Qt::Window | Qt::FramelessWindowHint);
            bg->setStyleSheet("background-color: black;");
            bg->setGeometry(screenWidth/2, 0, screenWidth/2, screenHeight);
            bg->setAttribute(Qt::WA_DeleteOnClose);
            fullscreenBgWindows.append(bg);
        }
        // 竖线
        fullscreenBgWindows.append(createDividerLine(true, screenWidth/2));
        break;
    }
    case 5: { // 双画面3+4
        // 左侧背景
        if (!channelSettings[2].isOpen) {
            QWidget* bg = new QWidget(nullptr, Qt::Window | Qt::FramelessWindowHint);
            bg->setStyleSheet("background-color: black;");
            bg->setGeometry(0, 1, screenWidth/2, screenHeight);
            bg->setAttribute(Qt::WA_DeleteOnClose);
            fullscreenBgWindows.append(bg);
        }
        // 右侧背景
        if (!channelSettings[3].isOpen) {
            QWidget* bg = new QWidget(nullptr, Qt::Window | Qt::FramelessWindowHint);
            bg->setStyleSheet("background-color: black;");
            bg->setGeometry(screenWidth/2, 0, screenWidth/2, screenHeight);
            bg->setAttribute(Qt::WA_DeleteOnClose);
            fullscreenBgWindows.append(bg);
        }
        // 竖线
        fullscreenBgWindows.append(createDividerLine(true, screenWidth/2));
        break;
    }
    case 6: { // 四画面
        const int halfW = screenWidth/2;
        const int halfH = screenHeight/2;

        // 四个区域背景
        for (int i = 0; i < 4; ++i) {
            if (!channelSettings[i].isOpen) {
                QWidget* bg = new QWidget(nullptr, Qt::Window | Qt::FramelessWindowHint);
                bg->setStyleSheet("background-color: black;");
                bg->setAttribute(Qt::WA_DeleteOnClose);

                if (i == 0) bg->setGeometry(0, 1, halfW, halfH);
                else if (i == 1) bg->setGeometry(halfW, 0, halfW, halfH);
                else if (i == 2) bg->setGeometry(0, halfH, halfW, halfH);
                else if (i == 3) bg->setGeometry(halfW, halfH, halfW, halfH);

                fullscreenBgWindows.append(bg);
            }
        }
        // 竖线和横线
        fullscreenBgWindows.append(createDividerLine(true, halfW));
        fullscreenBgWindows.append(createDividerLine(false, halfH));
        break;
    }
    }

    // 显示所有窗口并置底
    for (auto w : fullscreenBgWindows) {
        w->show();
        w->lower();
    }
}

void MainWindow::updateFullscreenBackgrounds()
{
    if (!isFullscreen) return;

    QScreen *screen = QGuiApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    const int screenWidth = screenGeometry.width();
    const int screenHeight = screenGeometry.height();

    // 隐藏所有非分割线窗口
    for (auto w : fullscreenBgWindows) {
        if (w && !w->property("isDividerLine").toBool()) {
            w->hide();
        }
    }

    // 更新背景显示状态
    switch(curchannel) {
    case 0: case 1: case 2: case 3: {
        if (!fullscreenBgWindows.isEmpty() && !channelSettings[curchannel].isOpen) {
            fullscreenBgWindows[0]->show();
            fullscreenBgWindows[0]->lower();
        }
        break;
    }
    case 4: {
        if (fullscreenBgWindows.size() > 0 && !channelSettings[0].isOpen) {
            fullscreenBgWindows[0]->show();
            fullscreenBgWindows[0]->lower();
        }
        if (fullscreenBgWindows.size() > 1 && !channelSettings[1].isOpen) {
            fullscreenBgWindows[1]->show();
            fullscreenBgWindows[1]->lower();
        }
        break;
    }
    case 5: {
        if (fullscreenBgWindows.size() > 0 && !channelSettings[2].isOpen) {
            fullscreenBgWindows[0]->show();
            fullscreenBgWindows[0]->lower();
        }
        if (fullscreenBgWindows.size() > 1 && !channelSettings[3].isOpen) {
            fullscreenBgWindows[1]->show();
            fullscreenBgWindows[1]->lower();
        }
        break;
    }
    case 6: {
        for (int i = 0; i < 4 && i < fullscreenBgWindows.size(); ++i) {
            if (!channelSettings[i].isOpen) {
                fullscreenBgWindows[i]->show();
                fullscreenBgWindows[i]->lower();
            }
        }
        break;
    }
    }
}

void MainWindow::clearFullscreenBackgrounds()
{
    for (auto w : fullscreenBgWindows) {
        if (w) {
            w->close();
            w->deleteLater();
        }
    }
    fullscreenBgWindows.clear();
}

void MainWindow::toggleVideoFullscreen()
{
    if (!isFullscreen) {
        // 进入全屏

        // 设置视频流
        if (curchannel < 4){
            if(curchannel==0)
                cameraStream1->setfullPreviewSize(curchannel);
            if(curchannel==1)
                cameraStream2->setfullPreviewSize(curchannel);
            if(curchannel==2)
                cameraStream3->setfullPreviewSize(curchannel);
            if(curchannel==3)
                cameraStream4->setfullPreviewSize(curchannel);
        }
        else if (curchannel == 4) {
            cameraStream1->setfullPreviewSize(4);
            cameraStream2->setfullPreviewSize(5);
        }
        else if (curchannel == 5) {
            cameraStream3->setfullPreviewSize(6);
            cameraStream4->setfullPreviewSize(7);
        }
        else if (curchannel == 6) {
            cameraStream1->setfullPreviewSize(8);
            cameraStream2->setfullPreviewSize(9);
            cameraStream3->setfullPreviewSize(10);
            cameraStream4->setfullPreviewSize(11);
        }

        ui->centralwidget->hide();
        createFullscreenBackgrounds();
        //updateFullscreenBackgrounds();
        isFullscreen = true;
    } else {
        // 退出全屏

        // 恢复视频流
        if (curchannel < 4){
            if(curchannel==0)
                cameraStream1->setPreviewSize(curchannel);
            if(curchannel==1)
                cameraStream2->setPreviewSize(curchannel);
            if(curchannel==2)
                cameraStream3->setPreviewSize(curchannel);
            if(curchannel==3)
                cameraStream4->setPreviewSize(curchannel);
        }
        else if (curchannel == 4) {
            cameraStream1->setPreviewSize(4);
            cameraStream2->setPreviewSize(5);
        }
        else if (curchannel == 5) {
            cameraStream3->setPreviewSize(6);
            cameraStream4->setPreviewSize(7);
        }
        else if (curchannel == 6) {
            cameraStream1->setPreviewSize(8);
            cameraStream2->setPreviewSize(9);
            cameraStream3->setPreviewSize(10);
            cameraStream4->setPreviewSize(11);
        }

        ui->centralwidget->show();
        clearFullscreenBackgrounds();
        isFullscreen = false;
    }
}


QPair<QString, QString> MainWindow::findRealMouseDevice() {
    QFile file("/proc/bus/input/devices");
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "Failed to open /proc/bus/input/devices";
        return qMakePair(QString(), QString()); // 返回空pair
    }

    QTextStream in(&file);
    QString line;
    bool foundMouse = false;
    QString devicePath = "";
    QString deviceName = "";

    while (in.readLineInto(&line)) {
        // 检查是否是鼠标设备
        if (line.startsWith("N: Name=")) {
            deviceName = line.mid(8).trimmed(); // 提取设备名称
            if (deviceName.contains("Mouse", Qt::CaseInsensitive) ||
                deviceName.contains("MOUSE", Qt::CaseInsensitive)) {
                foundMouse = true;
            }
        }
        // 如果是鼠标设备，查找对应的Handlers行
        if (foundMouse && line.startsWith("H: Handlers=")) {
            int eventPos = line.indexOf("event");
            if (eventPos != -1) {
                // 提取eventX部分
                int spacePos = line.indexOf(' ', eventPos);
                QString eventStr = line.mid(eventPos, spacePos - eventPos);
                devicePath = "/dev/input/" + eventStr;
                break;
            }
        }

        // 空行表示设备块结束
        if (line.trimmed().isEmpty()) {
            foundMouse = false;
            deviceName.clear();
        }
    }

    file.close();
    return qMakePair(devicePath, deviceName);
}

void MainWindow::startSystemDoubleClickMonitor() {
    stopSystemDoubleClickMonitor();

    // 1. 查找真正的鼠标设备（现在返回的是 QPair<路径, 名称>）
    QPair<QString, QString> mouseInfo = findRealMouseDevice();
    if (!mouseInfo.first.isEmpty()) {  // 检查路径是否为空
        mouseFd = open(mouseInfo.first.toStdString().c_str(), O_RDONLY | O_NONBLOCK);
        if (mouseFd != -1) {
            qDebug() << "Using REAL mouse device:" << mouseInfo.second << "at" << mouseInfo.first;
            setupMouseNotifier();
            return;
        } else {
            qWarning() << "Failed to open mouse device:" << mouseInfo.first << "Error:" << strerror(errno);
        }
    }

    // 2. 如果找不到或无法打开鼠标设备，报错
    qWarning("Failed to find or open a real mouse device!");
}

void MainWindow::setupMouseNotifier() {
    mouseNotifier = new QSocketNotifier(mouseFd, QSocketNotifier::Read);
    connect(mouseNotifier, &QSocketNotifier::activated, this, &MainWindow::handleMouseEvent);
}

void MainWindow::handleMouseEvent()
{
    struct input_event ev;
    ssize_t n;

    while ((n = read(mouseFd, &ev, sizeof(ev))) > 0)   // 一直读到没数据为止
    {
        if (ev.type == EV_KEY && ev.code == BTN_LEFT && ev.value == 1)
        {
            // 1. 坐标检查（非全屏时才做）
            if (!isFullscreen)
            {
                QPoint localPos = ui->video->mapFromGlobal(QCursor::pos());
                if (!ui->video->rect().contains(localPos))
                {
                    clickCount = 0;
                    continue;
                }
            }

            // 2. 用事件自己的时间戳算双击
            static qint64 lastBtnTime = 0;
            // 事件时间 -> 毫秒
            qint64 now = ev.input_event_sec * 1000ULL +
                         ev.input_event_usec / 1000ULL;

            if (now - lastBtnTime < doubleClickInterval)
                ++clickCount;
            else
                clickCount = 1;

            lastBtnTime = now;

            if (clickCount == 2)
            {
                clickCount = 0;
                qDebug() << "Double click detected!";
                toggleVideoFullscreen();
            }
        }
    }

    if (n == -1 && errno != EAGAIN)
    {
        qWarning("Error reading mouse event");
        stopSystemDoubleClickMonitor();
    }
}


void MainWindow::stopSystemDoubleClickMonitor() {
    if (mouseNotifier) {
        mouseNotifier->setEnabled(false);  // 先禁用
        delete mouseNotifier;
        mouseNotifier = nullptr;
    }
    if (mouseFd != -1) {
        ::close(mouseFd);
        mouseFd = -1;
    }
}

void MainWindow::saveWidgetProperties(QWidget* widget, const QString& name) {
    if (widget) {
        WidgetProperties props;
        props.sizePolicy = widget->sizePolicy();
        props.minimumSize = widget->minimumSize();
        props.maximumSize = widget->maximumSize();
        widgetPropertiesMap[name] = props;
    }
}

void MainWindow::restoreWidgetProperties(QWidget* widget, const QString& name) {
    if (widget && widgetPropertiesMap.contains(name)) {
        const WidgetProperties& props = widgetPropertiesMap[name];
        widget->setSizePolicy(props.sizePolicy);
        widget->setMinimumSize(props.minimumSize);
        widget->setMaximumSize(props.maximumSize);
    }
}



void MainWindow::toggleVideoFullscreen2() {

}

void MainWindow::toggleVideoFullscreen4() {

}


// 处理激活事件
// void MainWindow::changeEvent(QEvent* event)
// {
//     if (event->type() == QEvent::ActivationChange) {
//         if (isFullscreen && !fullscreenWindow->isActiveWindow()) {
//             fullscreenWindow->activateWindow();
//         }
//     }
//     QMainWindow::changeEvent(event);
// }

void MainWindow::Signals1(){
    if(curchannel == 0)
    {
        return;
    }

    CameraStream* cameraStreams[4] = {cameraStream1, cameraStream2, cameraStream3, cameraStream4};
    for (int i = 0; i < 4; i++) {
        cameraStreams[i]->pausePreview();
    }


    // 切换到通道0
    curchannel = 0;
    ui->stackedWidget_2->setCurrentIndex(curchannel);

    ui->checkBox_audio->setChecked(curaudiostate[curchannel]);

    ui->timewmark->setChecked(timewatermark[curchannel]);

    // 更新UI选项
    if (channelSettings[curchannel].isOpen)
    {

        restoreSelection();
        // 更新音频设备列表
        updateAudioDevicesForCamera(channelSettings[curchannel].UsbInterface);

        // 恢复通道1的预览（如果之前被暂停）
        if (cameraStream1->isPreviewPaused())
        {
            cameraStream1->setPreviewSize(0);
            cameraStream1->resumePreview();
        }

        // 更新控制状态
        if(isRecording[curchannel] == false) {
            enableCameraControls();
            ui->recordingSet->setEnabled(true);
        } else {
            disableCameraControls();
            ui->recordingSet->setEnabled(false);
        }
    } else {
        // 如果该通道未打开摄像头，则设置为空
        currentDevicePath = "";
        currentFormat.clear();
        currentResolution.clear();
        ui->comboBox_cameradev->setCurrentIndex(-1);
        ui->comboBox_videoformat->clear();
        ui->comboBox_fbl->clear();
        ui->comboBox_audiodev->clear();  // 清空音频设备列表
        updateAudioCheckboxState();  // 更新音频复选框状态
        enableCameraControls();
        ui->recordingSet->setEnabled(true);

    }
    QTimer::singleShot(100, this, [this]()
                       {
                            if (!currentDevicePath.isEmpty()) {
                                initControl();
                            }else{
                                resetcontrol();
                            }

                       });
}

void MainWindow::Signals2(){
    if(curchannel == 1)
    {
        return;
    }
    // 更新背景状态
    //updateCameraBackgrounds();


    // 保存当前通道设置
    // if (curchannel >= 0 && curchannel < 4) {
    //     // 保存当前通道是否打开
    //     channelSettings[curchannel].isOpen = !currentDevicePath.isEmpty();
    //     // 保存当前设备路径和参数
    //     if (channelSettings[curchannel].isOpen) {
    //         channelSettings[curchannel].devicePath = currentDevicePath;
    //         channelSettings[curchannel].format = currentFormat;
    //         channelSettings[curchannel].resolution = currentResolution;
    //         // 保存当前所选的索引
    //         channelSettings[curchannel].videoIndex = ui->comboBox_cameradev->currentIndex();
    //         channelSettings[curchannel].formatIndex = ui->comboBox_videoformat->currentIndex();
    //         channelSettings[curchannel].resolutionIndex = ui->comboBox_fbl->currentIndex();
    //         // qDebug() << "videoIndex:" << channelSettings[0].videoIndex;
    //         // qDebug() << "formatIndex:" << channelSettings[0].formatIndex;
    //         // qDebug() << "resolutionIndex:" << channelSettings[0].resolutionIndex;
    //     }
    // }
    CameraStream* cameraStreams[4] = {cameraStream1, cameraStream2, cameraStream3, cameraStream4};
    for (int i = 0; i < 4; i++) {
        cameraStreams[i]->pausePreview();
    }

    // 切换到通道1
    curchannel = 1;
    ui->stackedWidget_2->setCurrentIndex(curchannel);

    ui->checkBox_audio->setChecked(curaudiostate[curchannel]);

    ui->timewmark->setChecked(timewatermark[curchannel]);

    // if(isRecording[curchannel] == false)
    // {
    //     enableCameraControls();

    //     ui->recordingSet->setEnabled(true);
    // }
    // else
    // {
    //     disableCameraControls();
    //     ui->recordingSet->setEnabled(false);
    // }

    // 更新UI选项
    if (channelSettings[curchannel].isOpen)
    {

        restoreSelection();
        // 更新音频设备列表
        updateAudioDevicesForCamera(channelSettings[curchannel].UsbInterface);


        // 恢复通道3的预览（如果之前被暂停）
        if (cameraStream2->isPreviewPaused()) {
            cameraStream2->setPreviewSize(1);
            cameraStream2->resumePreview();
        }

        // 更新控制状态
        if(isRecording[curchannel] == false) {
            enableCameraControls();
            ui->recordingSet->setEnabled(true);
        } else {
            disableCameraControls();
            ui->recordingSet->setEnabled(false);
        }

    } else {
        // 如果该通道未打开摄像头，则设置为空
        currentDevicePath = "";
        currentFormat.clear();
        currentResolution.clear();
        ui->comboBox_cameradev->setCurrentIndex(-1);
        ui->comboBox_videoformat->clear();
        ui->comboBox_fbl->clear();
        ui->comboBox_audiodev->clear();  // 清空音频设备列表
        updateAudioCheckboxState();  // 更新音频复选框状态
        enableCameraControls();
        ui->recordingSet->setEnabled(true);
    }
    QTimer::singleShot(100, this, [this]()
                       {
                           if (!currentDevicePath.isEmpty()) {
                               initControl();
                           }else{
                               resetcontrol();
                           }
                       });

}

void MainWindow::Signals3(){
    if(curchannel == 2)
    {
        return;
    }
    // 更新背景状态
    //updateCameraBackgrounds();

    CameraStream* cameraStreams[4] = {cameraStream1, cameraStream2, cameraStream3, cameraStream4};
    for (int i = 0; i < 4; i++) {
        cameraStreams[i]->pausePreview();
    }

    // 保存当前通道设置
    // if (curchannel >= 0 && curchannel < 4) {
    //     // 保存当前通道是否打开
    //     channelSettings[curchannel].isOpen = !currentDevicePath.isEmpty();
    //     // 保存当前设备路径和参数
    //     if (channelSettings[curchannel].isOpen) {
    //         channelSettings[curchannel].devicePath = currentDevicePath;
    //         channelSettings[curchannel].format = currentFormat;
    //         channelSettings[curchannel].resolution = currentResolution;
    //         // 保存当前所选的索引
    //         channelSettings[curchannel].videoIndex = ui->comboBox_cameradev->currentIndex();
    //         channelSettings[curchannel].formatIndex = ui->comboBox_videoformat->currentIndex();
    //         channelSettings[curchannel].resolutionIndex = ui->comboBox_fbl->currentIndex();
    //     }
    // }

    // 切换到通道2
    curchannel = 2;
    ui->stackedWidget_2->setCurrentIndex(curchannel);

    ui->checkBox_audio->setChecked(curaudiostate[curchannel]);

    ui->timewmark->setChecked(timewatermark[curchannel]);

    // 更新UI选项
    if (channelSettings[2].isOpen) {
        restoreSelection();
        // 更新音频设备列表
        updateAudioDevicesForCamera(channelSettings[curchannel].UsbInterface);

        // 恢复通道3的预览（如果之前被暂停）
        if (cameraStream3->isPreviewPaused()) {
            cameraStream3->setPreviewSize(2);
            cameraStream3->resumePreview();
        }

        // 更新控制状态
        if(isRecording[curchannel] == false)
        {
            enableCameraControls();
            ui->recordingSet->setEnabled(true);
        }
        else
        {
            disableCameraControls();
            ui->recordingSet->setEnabled(false);
        }
    } else {
        // 如果该通道未打开摄像头，则设置为空
        currentDevicePath = "";
        currentFormat.clear();
        currentResolution.clear();
        ui->comboBox_cameradev->setCurrentIndex(-1);
        ui->comboBox_videoformat->clear();
        ui->comboBox_fbl->clear();
        ui->comboBox_audiodev->clear();  // 清空音频设备列表
        updateAudioCheckboxState();  // 更新音频复选框状态
        enableCameraControls();
        ui->recordingSet->setEnabled(true);
    }
    QTimer::singleShot(100, this, [this]()
                       {
                           if (!currentDevicePath.isEmpty()) {
                               initControl();
                           }else{
                               resetcontrol();
                           }
                       });


}

void MainWindow::Signals4(){
    if(curchannel == 3)
    {
        return;
    }
    // 更新背景状态
    //updateCameraBackgrounds();

    CameraStream* cameraStreams[4] = {cameraStream1, cameraStream2, cameraStream3, cameraStream4};
    for (int i = 0; i < 4; i++) {
        cameraStreams[i]->pausePreview();
    }
    // 保存当前通道设置
    // if (curchannel >= 0 && curchannel < 4) {
    //     // 保存当前通道是否打开
    //     channelSettings[curchannel].isOpen = !currentDevicePath.isEmpty();
    //     // 保存当前设备路径和参数
    //     if (channelSettings[curchannel].isOpen) {
    //         channelSettings[curchannel].devicePath = currentDevicePath;
    //         channelSettings[curchannel].format = currentFormat;
    //         channelSettings[curchannel].resolution = currentResolution;
    //         // 保存当前所选的索引
    //         channelSettings[curchannel].videoIndex = ui->comboBox_cameradev->currentIndex();
    //         channelSettings[curchannel].formatIndex = ui->comboBox_videoformat->currentIndex();
    //         channelSettings[curchannel].resolutionIndex = ui->comboBox_fbl->currentIndex();
    //     }
    // }

    // 切换到通道3
    curchannel = 3;
    ui->stackedWidget_2->setCurrentIndex(curchannel);

    ui->checkBox_audio->setChecked(curaudiostate[curchannel]);

    ui->timewmark->setChecked(timewatermark[curchannel]);


    // 更新UI选项
    if (channelSettings[3].isOpen) {
        restoreSelection();
        // 更新音频设备列表
        updateAudioDevicesForCamera(channelSettings[curchannel].UsbInterface);

        // 恢复通道4的预览（如果之前被暂停）
        if (cameraStream4->isPreviewPaused()) {
            cameraStream4->setPreviewSize(3);
            cameraStream4->resumePreview();

        }

        // 更新控制状态
        if(isRecording[curchannel] == false)
        {
            enableCameraControls();
            ui->recordingSet->setEnabled(true);
        }
        else
        {
            disableCameraControls();
            ui->recordingSet->setEnabled(false);
        }
    } else {
        // 如果该通道未打开摄像头，则设置为空
        currentDevicePath = "";
        currentFormat.clear();
        currentResolution.clear();
        ui->comboBox_cameradev->setCurrentIndex(-1);
        ui->comboBox_videoformat->clear();
        ui->comboBox_fbl->clear();
        ui->comboBox_audiodev->clear();  // 清空音频设备列表
        updateAudioCheckboxState();  // 更新音频复选框状态
        enableCameraControls();
        ui->recordingSet->setEnabled(true);
        enableCameraControls();
        ui->recordingSet->setEnabled(true);
    }
    QTimer::singleShot(100, this, [this]()
                       {
                           if (!currentDevicePath.isEmpty()) {
                               initControl();
                           }else{
                               resetcontrol();
                           }
                       });
}

void MainWindow::Signals5(){
    if(curchannel == 4)
    {
        return;
    }
    // 更新背景状态
    //updateCameraBackgrounds();
    updateCompositePageLabels(0,1,channelSettings[0].isOpen,channelSettings[1].isOpen);
    disableCameraControls();
    CameraStream* cameraStreams[4] = {cameraStream1, cameraStream2, cameraStream3, cameraStream4};
    for (int i = 0; i < 4; i++) {
        cameraStreams[i]->pausePreview();
    }
    //保存当前通道设置
    // if (curchannel >= 0 && curchannel < 4) {
    //     // 保存当前通道是否打开
    //     channelSettings[curchannel].isOpen = !currentDevicePath.isEmpty();
    //     // 保存当前设备路径和参数
    //     if (channelSettings[curchannel].isOpen) {
    //         channelSettings[curchannel].devicePath = currentDevicePath;
    //         channelSettings[curchannel].format = currentFormat;
    //         channelSettings[curchannel].resolution = currentResolution;
    //         // 保存当前所选的索引
    //         channelSettings[curchannel].videoIndex = ui->comboBox_cameradev->currentIndex();
    //         channelSettings[curchannel].formatIndex = ui->comboBox_videoformat->currentIndex();
    //         channelSettings[curchannel].resolutionIndex = ui->comboBox_fbl->currentIndex();
    //     }
    // }

    curchannel=4;
    ui->stackedWidget_2->setCurrentIndex(curchannel);

    //清空combox
    ui->comboBox_cameradev->setCurrentIndex(-1);
    ui->comboBox_videoformat->setCurrentIndex(-1);
    ui->comboBox_fbl->setCurrentIndex(-1);
    ui->comboBox_audiodev->setCurrentIndex(-1);  // 清空音频设备列表
    ui->checkBox_audio->setChecked(false);
    //updateAudioCheckboxState();  // 更新音频复选框状态
    disableCameraControls();
    ui->recordingSet->setEnabled(false);
    //恢复通道1和2的预览（如果它们已打开）
    if(channelSettings[0].isOpen)
    {
        cameraStream1->setPreviewSize(4);
        cameraStream1->resumePreview();
    }
    if(channelSettings[1].isOpen)
    {
        cameraStream2->setPreviewSize(5);
        cameraStream2->resumePreview();
    }
}

void MainWindow::Signals6(){
    if(curchannel == 5)
    {
        return;
    }
    // 更新背景状态
    //updateCameraBackgrounds();
    updateCompositePageLabels(2, 3,channelSettings[2].isOpen,channelSettings[3].isOpen);
    disableCameraControls();
    CameraStream* cameraStreams[4] = {cameraStream1, cameraStream2, cameraStream3, cameraStream4};
    for (int i = 0; i < 4; i++) {
        cameraStreams[i]->pausePreview();
    }
    //保存当前通道设置
    // if (curchannel >= 0 && curchannel < 4) {
    //     // 保存当前通道是否打开
    //     channelSettings[curchannel].isOpen = !currentDevicePath.isEmpty();
    //     // 保存当前设备路径和参数
    //     if (channelSettings[curchannel].isOpen) {
    //         channelSettings[curchannel].devicePath = currentDevicePath;
    //         channelSettings[curchannel].format = currentFormat;
    //         channelSettings[curchannel].resolution = currentResolution;
    //         // 保存当前所选的索引
    //         channelSettings[curchannel].videoIndex = ui->comboBox_cameradev->currentIndex();
    //         channelSettings[curchannel].formatIndex = ui->comboBox_videoformat->currentIndex();
    //         channelSettings[curchannel].resolutionIndex = ui->comboBox_fbl->currentIndex();
    //     }
    // }
    curchannel=5;
    ui->stackedWidget_2->setCurrentIndex(curchannel);
    //清空combox
    ui->comboBox_cameradev->setCurrentIndex(-1);
    ui->comboBox_videoformat->setCurrentIndex(-1);
    ui->comboBox_fbl->setCurrentIndex(-1);
    ui->comboBox_audiodev->setCurrentIndex(-1);  // 清空音频设备列表
    ui->checkBox_audio->setChecked(false);
    //updateAudioCheckboxState();  // 更新音频复选框状态

    // 恢复通道3和4的预览（如果它们已打开）
    if(channelSettings[2].isOpen)
    {
        cameraStream3->setPreviewSize(6);
        cameraStream3->resumePreview();
    }
    if(channelSettings[3].isOpen)
    {
        cameraStream4->setPreviewSize(7);
        cameraStream4->resumePreview();
    }

    disableCameraControls();
    ui->recordingSet->setEnabled(false);
}

void MainWindow::Signals7(){
    if(curchannel == 6)
    {
        return;
    }
    // 更新背景状态
    //updateCameraBackgrounds();
    updateGridPageLabels(0,1,2,3,channelSettings[0].isOpen,channelSettings[1].isOpen,channelSettings[2].isOpen,channelSettings[3].isOpen);
    disableCameraControls();


    curchannel=6;
    ui->stackedWidget_2->setCurrentIndex(curchannel);
    //清空combox
    ui->comboBox_cameradev->setCurrentIndex(-1);
    ui->comboBox_videoformat->setCurrentIndex(-1);
    ui->comboBox_fbl->setCurrentIndex(-1);
    ui->comboBox_audiodev->setCurrentIndex(-1);  // 清空音频设备列表
    ui->checkBox_audio->setChecked(false);
    //updateAudioCheckboxState();  // 更新音频复选框状态

    // 恢复所有通道的预览（如果它们已打开）
    if(channelSettings[0].isOpen)
    {
        cameraStream1->setPreviewSize(8);
        cameraStream1->resumePreview();
    }
    if(channelSettings[1].isOpen)
    {
        cameraStream2->setPreviewSize(9);
        cameraStream2->resumePreview();
    }
    if(channelSettings[2].isOpen)
    {
        cameraStream3->setPreviewSize(10);
        cameraStream3->resumePreview();
    }
    if(channelSettings[3].isOpen)
    {
        cameraStream4->setPreviewSize(11);
        cameraStream4->resumePreview();
    }
    disableCameraControls();
    ui->recordingSet->setEnabled(false);

}

void MainWindow::videoset() {
    keybod=1;
    stopSystemDoubleClickMonitor();  // 临时禁用
    ui->centralwidget->setEnabled(false);
    RecordingSet recordset(this);
    recordset.exec();

    ui->centralwidget->setEnabled(true);
    startSystemDoubleClickMonitor(); // 重新启用
    keybod=0;
}

void MainWindow::filemanage() {
    keybod=1;
    stopSystemDoubleClickMonitor();  // 临时禁用
    ui->centralwidget->setEnabled(false);
    FileManage fileman(this);
    fileman.exec();
    ui->centralwidget->setEnabled(true);
    startSystemDoubleClickMonitor(); // 重新启用
    keybod=0;
}

void MainWindow::systemset() {
    keybod=1;
    stopSystemDoubleClickMonitor();  // 临时禁用
    ui->centralwidget->setEnabled(false);
    Systemset systems(this);
    connect(&systems, &Systemset::Languagechanged, this, &MainWindow::retranslateUi);  // 连接信号

    systems.exec();
    ui->centralwidget->setEnabled(true);
    startSystemDoubleClickMonitor(); // 重新启用
    keybod=0;
}

void MainWindow::retranslateUi(){
    ui->retranslateUi(this);
}

void MainWindow::onMinimizeClicked(){

    showMinimized();
}

void MainWindow::onMaximizeClicked(){

    if (isMaximized()) {
        showNormal();
    } else {
        showMaximized();
    }
    updateMaximizeButtonIcon();
}

void MainWindow::updateMaximizeButtonIcon() {
    if (isMaximized()) {
        ui->pushButton_max->setIcon(QIcon(":/image/最大化还原.png"));
    } else {
        ui->pushButton_max->setIcon(QIcon(":/image/最大化.png"));
    }
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);
    updateMaximizeButtonIcon();
}

// 禁用所有相机控制界面元素
void MainWindow::disableCameraControls()
{
    ui->stackedWidget->setEnabled(false);
}

// 启用所有相机控制界面元素
void MainWindow::enableCameraControls()
{
    ui->stackedWidget->setEnabled(true);
    // 检查摄像头参数支持情况并相应地启用/禁用控件
    // if (!currentDevicePath.isEmpty()) {
    //     initControl();
    // }
}

void MainWindow::enableCloseButton()
{
    ui->closeButton->setEnabled(true);
}
void MainWindow::disableCloseButton()
{
    ui->closeButton->setEnabled(false);
}

// 从/dev/videoX中提取videoX并检查是否为奇数
QString MainWindow::extractOddVideoName(const QString &devicePath) {
    QRegularExpression re("video(\\d+)");
    QRegularExpressionMatch match = re.match(devicePath);
    if (match.hasMatch()) {
        bool ok;
        int videoNum = match.captured(1).toInt(&ok);
        if (ok && videoNum % 2 != 0) {  // 只返回奇数编号的video设备
            return match.captured();    // 返回完整的videoX
        }
    }
    return "";
}

// 在系统中查找指定video设备对应的USB接口
QString MainWindow::findUsbInterfaceForVideo(const QString &videoName) {
    // 遍历所有USB接口
    QDir usbDir("/sys/bus/usb/devices");
    foreach (const QString &entry, usbDir.entryList(QDir::Dirs | QDir::NoDotAndDotDot)) {
        // 匹配X-X.X:X.X格式的接口
        if (QRegularExpression("^\\d+-\\d+(?:\\.\\d+)*:\\d+\\.\\d+$").match(entry).hasMatch()) {
            QString videoPath = QString("/sys/bus/usb/devices/%1/video4linux").arg(entry);
            QDir videoDir(videoPath);

            if (videoDir.exists()) {
                // 检查该接口下是否有我们要找的video设备
                foreach (const QString &videoEntry, videoDir.entryList(QDir::Dirs | QDir::NoDotAndDotDot)) {
                    if (videoEntry == videoName) {
                        return entry; // 返回USB接口名，如5-1.2:1.0
                    }
                }
            }
        }
    }
    return "";
}

// 从指定USB接口获取PRODUCT VID
QString MainWindow::getProductVidFromInterface(const QString &usbInterface) {
    QString ueventPath = QString("/sys/bus/usb/devices/%1/uevent").arg(usbInterface);
    QFile ueventFile(ueventPath);

    if (!ueventFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return "";
    }

    QTextStream in(&ueventFile);
    while (!in.atEnd()) {
        QString line = in.readLine();
        if (line.startsWith("PRODUCT=")) {
            QStringList parts = line.mid(8).split('/');
            if (parts.size() >= 3) {
                return parts[1]; // 返回中间部分
            }
        }
    }

    return "";
}

// 保存通道设置到INI文件
void MainWindow::saveChannelSettingsToIni(const ChannelSettings (&settings)[4], const QString &iniPath) {
    QSettings ini(iniPath, QSettings::IniFormat);

    // 保存全局基础路径配置
    ini.beginGroup("Global");
    ini.setValue("basePath", basePath);
    ini.endGroup();

    for (int i = 0; i < 4; ++i) {
        const ChannelSettings &channel = settings[i];
        QString group = QString("Channel_%1").arg(i);

        ini.beginGroup(group);

        // 1. 提取奇数video设备名
        QString videoName = extractOddVideoName(channel.devicePath);
        if (!videoName.isEmpty()) {
            // 2. 保存video设备基本信息
            ini.setValue("isopen",channel.isOpen);
            ini.setValue("videoDevice", videoName);
            ini.setValue("format", channel.format);
            ini.setValue("resolution", channel.resolution);

            ini.setValue("devicename",channel.devicename);
            ini.setValue("FBLname",channel.FBLname);

            ini.setValue("formatindex", channel.formatIndex);
            ini.setValue("resolutionindex", channel.resolutionIndex);

            // 保存音频设备信息
            ini.setValue("audioDeviceName", channel.audioDeviceName);
            ini.setValue("audioDevicePath", channel.audioDevicePath);
            ini.setValue("audioDeviceIndex", channel.audioDeviceIndex);
            ini.setValue("audioEnabled", channel.audioEnabled);
            //录像设置
            ini.setValue("basepath",RecordingSettings[i].basePath);  // 新增：保存基础路径
            ini.setValue("filepath",RecordingSettings[i].path);
            ini.setValue("qianzhuiname",RecordingSettings[i].name);
            ini.setValue("segvideo",RecordingSettings[i].segmentedVideo);
            ini.setValue("recordbytime",RecordingSettings[i].recordingByTime);
            ini.setValue("segrecordtime",RecordingSettings[i].segmentedTime);
            ini.setValue("filesize",RecordingSettings[i].byFileSize);
            ini.setValue("segfilesize",RecordingSettings[i].segmentedSize);
            ini.setValue("quality",RecordingSettings[i].recordingQuality);
            ini.setValue("storagelimit",RecordingSettings[i].videoStorage);
            ini.setValue("storageSize",RecordingSettings[i].storageSize);
            //是否录像
            ini.setValue("isRecording",isRecording[i]);
            //水印
            ini.setValue("watermark",timewatermark[i]);
            //qDebug()<<i<<timewatermark[i];
            //ini.setValue("resolution", channel.resolution);

            //qDebug()<<"savebool"<<i<<RecordingSettings[i].segmentedVideo;
            // qDebug()<<videoName;
            // qDebug()<<channel.formatIndex;
            // qDebug()<<channel.resolutionIndex;
            // qDebug()<<"devicename"<<channel.devicename;
            // qDebug()<<"fblname"<<channel.FBLname;

            // 3. 查找对应的USB接口
            QString usbInterface = findUsbInterfaceForVideo(videoName);
            if (!usbInterface.isEmpty()) {
                ini.setValue("usbInterface", usbInterface);
                //qDebug()<<usbInterface;
                // 4. 获取并保存PRODUCT VID
                QString productVid = getProductVidFromInterface(usbInterface);
                if (!productVid.isEmpty()) {
                    ini.setValue("productVid", productVid);
                    //qDebug()<<productVid;
                }
                channelSettings[i].UsbInterface = usbInterface;
                channelSettings[i].ProductVid = productVid;
            }
        } else {
            // 不是奇数video设备，清空该通道设置
            ini.remove("");
        }

        // 注意：热插拔数据不在这里保存，而是在专门的Hotplug组中

        ini.endGroup();
    }
}


// 从INI文件读取并恢复配置
void MainWindow::loadSettingsFromIni(ChannelSettings (&settings)[4], const QString &iniPath) {
    QSettings ini(iniPath, QSettings::IniFormat);

    // 加载全局基础路径配置
    ini.beginGroup("Global");
    QString loadedBasePath = ini.value("basePath").toString();
    if (!loadedBasePath.isEmpty()) {
        basePath = loadedBasePath;
    }
    ini.endGroup();

    for (int channel = 0; channel < 4; ++channel) {
        QString group = QString("Channel_%1").arg(channel);
        ini.beginGroup(group);

        // 读取INI中的配置
        QString usbInterface = ini.value("usbInterface").toString();
        QString productVid = ini.value("productVid").toString();
        QVariantMap format = ini.value("format").toMap();
        QVariantMap resolution = ini.value("resolution").toMap();
        QString savedVideo = ini.value("videoDevice").toString();
        int formatindex = ini.value("formatindex").toInt();
        int resolutionindex = ini.value("resolutionindex").toInt();

        QString devname=ini.value("devicename").toString();
        QString fblname=ini.value("FBLname").toString();

        // 加载音频设备信息
        QString audioDeviceName = ini.value("audioDeviceName").toString();
        QString audioDevicePath = ini.value("audioDevicePath").toString();
        int audioDeviceIndex = ini.value("audioDeviceIndex", -1).toInt();
        bool audioEnabled = ini.value("audioEnabled", false).toBool();

        // 录像设置 - 优先加载basePath
        QString loadedBasePath = ini.value("basepath").toString();
        QString loadedPath = ini.value("filepath").toString();


        if (!loadedBasePath.isEmpty()) {
            RecordingSettings[channel].basePath = loadedBasePath;
        } else if (!loadedPath.isEmpty()) {
            // 向后兼容：从完整路径中提取基础路径
            if (loadedPath.contains("/Recording/CH")) {
                RecordingSettings[channel].basePath = loadedPath.left(loadedPath.indexOf("/Recording/CH"));
            } else {
                RecordingSettings[channel].basePath = loadedPath;
            }
        } else {
            RecordingSettings[channel].basePath = basePath; // 使用全局默认值
        }

        RecordingSettings[channel].path = loadedPath; // 保留用于向后兼容
        RecordingSettings[channel].name=ini.value("qianzhuiname").toString();
        RecordingSettings[channel].segmentedVideo=ini.value("segvideo").toBool();
        RecordingSettings[channel].recordingByTime=ini.value("recordbytime").toBool();
        RecordingSettings[channel].segmentedTime=ini.value("segrecordtime").toInt();
        RecordingSettings[channel].byFileSize=ini.value("filesize").toBool();
        RecordingSettings[channel].segmentedSize=ini.value("segfilesize").toInt();
        RecordingSettings[channel].recordingQuality=ini.value("quality").toInt();
        RecordingSettings[channel].videoStorage=ini.value("storagelimit").toBool();
        RecordingSettings[channel].storageSize=ini.value("storageSize").toInt();

        isRecording[channel]=ini.value("isRecording").toBool();
        timewatermark[channel]=ini.value("watermark").toBool();
        //qDebug()<<"***********************************************isrecord:"<<channel<<isRecording[channel];

        // 在系统中查找匹配的设备
        QString foundVideo = findMatchingVideoDevice(usbInterface, productVid);

        if (!foundVideo.isEmpty()) {
            // 更新通道设置
            settings[channel].devicePath = "/dev/" + foundVideo;
            settings[channel].format = format;
            settings[channel].resolution = resolution;
            settings[channel].formatIndex = formatindex;
            settings[channel].resolutionIndex = resolutionindex;
            settings[channel].devicename = devname;
            settings[channel].FBLname = fblname;
            settings[channel].UsbInterface = usbInterface;
            settings[channel].ProductVid = productVid;

            // 设置音频设备信息
            settings[channel].audioDeviceName = audioDeviceName;
            settings[channel].audioDevicePath = audioDevicePath;
            settings[channel].audioDeviceIndex = audioDeviceIndex;
            settings[channel].audioEnabled = audioEnabled;

            //  qDebug()<<foundVideo;
            // qDebug()<<channel;
            // qDebug()<<formatindex;
            // qDebug()<<resolutionindex;
            // qDebug()<<"devicename"<<devname;
            // qDebug()<<"fblname"<<fblname;


            // 如果找到的video与保存的不同，更新INI文件
            if (foundVideo != savedVideo) {
                ini.setValue("videoDevice", foundVideo);
                //qDebug() << "Updated video device for" << group << "from" << savedVideo << "to" << foundVideo;
            }

            //qDebug() << group << "restored with device:" << foundVideo;
        } else {
            settings[channel].isOpen = false;
            //qDebug() << group << "no matching device found";
        }

        ini.endGroup();
    }
}


// 在系统中查找匹配USB接口和VID的video设备
QString MainWindow::findMatchingVideoDevice(const QString &targetInterface, const QString &targetVid) {
    QDir usbDir("/sys/bus/usb/devices");

    // 遍历所有USB接口
    foreach (const QString &entry, usbDir.entryList(QDir::Dirs | QDir::NoDotAndDotDot)) {
        // 检查接口名称匹配
        if (entry == targetInterface) {
            QString interfacePath = usbDir.filePath(entry);

            // 验证VID是否匹配
            if (getProductVidFromInterface(entry) == targetVid) {
                // 查找该接口下的奇数video设备
                QDir videoDir(interfacePath + "/video4linux");
                if (videoDir.exists()) {
                    foreach (const QString &videoEntry, videoDir.entryList(QDir::Dirs | QDir::NoDotAndDotDot)) {
                        QRegularExpressionMatch match = QRegularExpression("^video(\\d+)$").match(videoEntry);
                        if (match.hasMatch()) {
                            int num = match.captured(1).toInt();
                            if (num % 2 != 0) { // 只返回奇数设备
                                return videoEntry;
                            }
                        }
                    }
                }
            }
        }
    }
    return "";
}

void MainWindow::reopendevice(){
    //QString iniPath = "/data/camera_settings.ini";
    loadSettingsFromIni(channelSettings, iniPath);
    int channel;

    // 保存当前通道的设备路径，避免被其他通道覆盖
    QString originalCurrentDevicePath = currentDevicePath;
    QVariantMap originalCurrentFormat = currentFormat;
    QVariantMap originalCurrentResolution = currentResolution;

    for(channel=3;channel>=0;channel--){
        if(channelSettings[channel].devicePath.isEmpty()){
            continue;
        }
        else{
            // 获取QComboBox的项数
            int itemCount = ui->comboBox_cameradev->count();
            // 遍历所有项
            for (int i = 0; i < itemCount; ++i)
            {
                // 获取当前项的数据
                QString data = ui->comboBox_cameradev->itemData(i).toString();

                if (data.contains(channelSettings[channel].devicePath))
                {
                    if(channel==0){
                        // 如果找到，选中该项
                        ui->comboBox_cameradev->setCurrentIndex(i);
                        channelSettings[channel].videoIndex=i;
                        break; // 如果只需要选中第一个匹配项，找到后就可以退出循环
                    }
                    else{
                        // 如果找到，选中该项
                        channelSettings[channel].videoIndex=i;
                        break; // 如果只需要选中第一个匹配项，找到后就可以退出循环
                    }
                }
            }
            QString redevicePath = channelSettings[channel].devicePath;      // 设备路径
            QVariantMap reformat = channelSettings[channel].format;           // 格式
            QVariantMap resolutionre = channelSettings[channel].resolution;
            int fmindex=channelSettings[channel].formatIndex;
            int fblindex=channelSettings[channel].resolutionIndex;
            QString devicename=channelSettings[channel].devicename;
            QString fblname=channelSettings[channel].FBLname;

            if(channel==0){
                //qDebug()<<"**************************************************************************";
                cameraStream1->initializeGStreamer(redevicePath, reformat, resolutionre);
                channelSettings[channel].isOpen =true;



                // 只有当前通道是0时才更新全局的currentDevicePath
                // if(curchannel == 0) {
                //     currentDevicePath=redevicePath;
                //     currentFormat=reformat;
                //     currentResolution=resolutionre;
                // }

                //qDebug()<<"0Path:"<<redevicePath;

                // 只有当前通道是0时才初始化控制和UI
                if(curchannel == 0) {
                    initControl();

                    currentDevicePath=redevicePath;
                    currentFormat=reformat;
                    currentResolution=resolutionre;

                    // 对于其他设备，正常查询支持的格式
                    QList<QPair<QString, QVariantMap>> formats = cameraParams->findVideoFormats(redevicePath);
                    for (const auto &format : formats) {
                        ui->comboBox_videoformat->addItem(format.first, format.second);
                        //qDebug()<<format;
                    }
                    ui->comboBox_videoformat->setCurrentIndex(fmindex);
                    uint32_t pixelformat = reformat["pixelformat"].toUInt();
                    QList<QPair<QString, QVariantMap>> resolutions = cameraParams->findVideoResolutions(redevicePath, pixelformat);
                    for (const auto &resolution : resolutions) {
                        ui->comboBox_fbl->addItem(resolution.first, resolution.second);
                    }
                    ui->comboBox_fbl->setCurrentIndex(fblindex);

                    // 恢复音频设备设置
                    QString usbInterface = channelSettings[channel].UsbInterface;
                    if (!usbInterface.isEmpty()) {
                        updateAudioDevicesForCamera(usbInterface);

                        // 恢复音频设备选择
                        if (!channelSettings[channel].audioDevicePath.isEmpty()) {
                            for (int i = 0; i < ui->comboBox_audiodev->count(); ++i) {
                                if (ui->comboBox_audiodev->itemData(i).toString() == channelSettings[channel].audioDevicePath) {
                                    ui->comboBox_audiodev->setCurrentIndex(i);
                                    break;
                                }
                            }
                        }

                        // 恢复音频启用状态
                        curaudiostate[channel] = channelSettings[channel].audioEnabled;
                        ui->checkBox_audio->setChecked(channelSettings[channel].audioEnabled);
                        updateAudioCheckboxState();
                    }
                }

                curdevicename[channel]=devicename;
                curfblname[channel]=fblname;

                //qDebug()<<"21313213121312313121213131313123111111313213131"<<curfblname[channel];
                if(isRecording[channel]){
                    // 添加200毫秒延迟再开启录像，避免PTS问题
                    printf("通道 %d 设备恢复后延迟200ms重启录像\n", channel);
                    QTimer::singleShot(500, this, [this, channel]() {
                        toggleRecordingForCamera(channel+1);
                    });
                }
                ui->timewmark->setChecked(timewatermark[channel]);
                //qDebug()<<"21313213121312313121213131313123111111313213131"<<timewatermark[channel];
            }
            if(channel==1){
                cameraStream2->initializeGStreamer(redevicePath, reformat, resolutionre);
                channelSettings[channel].isOpen =true;



                // 只有当前通道是1时才更新全局的currentDevicePath
                if(curchannel == 1) {
                    currentDevicePath=redevicePath;
                    currentFormat=reformat;
                    currentResolution=resolutionre;
                    initControl();

                    // 恢复音频设备设置
                    QString usbInterface = channelSettings[channel].UsbInterface;
                    if (!usbInterface.isEmpty()) {
                        updateAudioDevicesForCamera(usbInterface);

                        // 恢复音频设备选择
                        if (!channelSettings[channel].audioDevicePath.isEmpty()) {
                            for (int i = 0; i < ui->comboBox_audiodev->count(); ++i) {
                                if (ui->comboBox_audiodev->itemData(i).toString() == channelSettings[channel].audioDevicePath) {
                                    ui->comboBox_audiodev->setCurrentIndex(i);
                                    break;
                                }
                            }
                        }

                        // 恢复音频启用状态
                        curaudiostate[channel] = channelSettings[channel].audioEnabled;
                        ui->checkBox_audio->setChecked(channelSettings[channel].audioEnabled);
                        updateAudioCheckboxState();
                    }
                }

                curdevicename[channel]=devicename;
                curfblname[channel]=fblname;
                if(isRecording[channel]){
                    // 添加200毫秒延迟再开启录像，避免PTS问题
                    printf("通道 %d 设备恢复后延迟200ms重启录像\n", channel);
                    QTimer::singleShot(500, this, [this, channel]() {
                        toggleRecordingForCamera(channel+1);
                    });
                }
                ui->timewmark->setChecked(timewatermark[channel]);
            }
            if(channel==2){
                cameraStream3->initializeGStreamer(redevicePath, reformat, resolutionre);
                channelSettings[channel].isOpen =true;



                // 只有当前通道是2时才更新全局的currentDevicePath
                if(curchannel == 2) {
                    currentDevicePath=redevicePath;
                    currentFormat=reformat;
                    currentResolution=resolutionre;
                    initControl();

                    // 恢复音频设备设置
                    QString usbInterface = channelSettings[channel].UsbInterface;
                    if (!usbInterface.isEmpty()) {
                        updateAudioDevicesForCamera(usbInterface);

                        // 恢复音频设备选择
                        if (!channelSettings[channel].audioDevicePath.isEmpty()) {
                            for (int i = 0; i < ui->comboBox_audiodev->count(); ++i) {
                                if (ui->comboBox_audiodev->itemData(i).toString() == channelSettings[channel].audioDevicePath) {
                                    ui->comboBox_audiodev->setCurrentIndex(i);
                                    break;
                                }
                            }
                        }

                        // 恢复音频启用状态
                        curaudiostate[channel] = channelSettings[channel].audioEnabled;
                        ui->checkBox_audio->setChecked(channelSettings[channel].audioEnabled);
                        updateAudioCheckboxState();
                    }
                }

                curdevicename[channel]=devicename;
                curfblname[channel]=fblname;
                if(isRecording[channel]){
                    // 添加200毫秒延迟再开启录像，避免PTS问题
                    printf("通道 %d 设备恢复后延迟200ms重启录像\n", channel);
                    QTimer::singleShot(500, this, [this, channel]() {
                        toggleRecordingForCamera(channel+1);
                    });
                }
                ui->timewmark->setChecked(timewatermark[channel]);
            }
            if(channel==3){
                cameraStream4->initializeGStreamer(redevicePath, reformat, resolutionre);
                channelSettings[channel].isOpen =true;



                // 只有当前通道是3时才更新全局的currentDevicePath
                if(curchannel == 3) {
                    currentDevicePath=redevicePath;
                    currentFormat=reformat;
                    currentResolution=resolutionre;
                    initControl();

                    // 恢复音频设备设置
                    QString usbInterface = channelSettings[channel].UsbInterface;
                    if (!usbInterface.isEmpty()) {
                        updateAudioDevicesForCamera(usbInterface);

                        // 恢复音频设备选择
                        if (!channelSettings[channel].audioDevicePath.isEmpty()) {
                            for (int i = 0; i < ui->comboBox_audiodev->count(); ++i) {
                                if (ui->comboBox_audiodev->itemData(i).toString() == channelSettings[channel].audioDevicePath) {
                                    ui->comboBox_audiodev->setCurrentIndex(i);
                                    break;
                                }
                            }
                        }

                        // 恢复音频启用状态
                        curaudiostate[channel] = channelSettings[channel].audioEnabled;
                        ui->checkBox_audio->setChecked(channelSettings[channel].audioEnabled);
                        updateAudioCheckboxState();
                    }
                }

                curdevicename[channel]=devicename;
                curfblname[channel]=fblname;
                if(isRecording[channel]){
                    // 添加200毫秒延迟再开启录像，避免PTS问题
                    printf("通道 %d 设备恢复后延迟200ms重启录像\n", channel);
                    QTimer::singleShot(500, this, [this, channel]() {
                        toggleRecordingForCamera(channel+1);
                    });
                }
                ui->timewmark->setChecked(timewatermark[channel]);
            }

        }
        updateDeviceLabels(channel);
    }

    // 如果当前通道没有摄像头打开，恢复原来的设备路径
    if(curchannel >= 0 && curchannel < 4 && !channelSettings[curchannel].isOpen) {
        currentDevicePath = originalCurrentDevicePath;
        currentFormat = originalCurrentFormat;
        currentResolution = originalCurrentResolution;
    }

}
// 新增通道录像控制函数实现
void MainWindow::startRecordingByChannel(int channel) {

    // 根据通道号启动对应的录像
    switch(channel) {
    case 0:
        // 设置CameraStream1的通道号为0 什么东西
        // channelSettings[channel].isOpen=false;
        // //updateCameraBackgrounds();
        // channelSettings[channel].isOpen=true;
        cameraStream1->channelstream = 0;
        cameraStream1->startRecording();
        //updateCameraBackgrounds();
        if(curchannel==4 || curchannel==5)
        {
            cameraStream1->setPreviewSize(4);
        }
        else if(curchannel==6)
        {
            cameraStream1->setPreviewSize(8);
        }
        isRecording[0] = true;
        break;
    case 1:
        // 设置CameraStream2的通道号为1
        cameraStream2->channelstream = 1;
        cameraStream2->startRecording();
        if(curchannel==4 || curchannel==5)
        {
            cameraStream2->setPreviewSize(5);
        }
        else if(curchannel==6)
        {
            cameraStream2->setPreviewSize(9);
        }
        isRecording[1] = true;
        break;
    case 2:
        // 设置CameraStream3的通道号为2
        cameraStream3->channelstream = 2;
        cameraStream3->startRecording();
        if(curchannel==4 || curchannel==5)
        {
            cameraStream3->setPreviewSize(4);
        }
        else if(curchannel==6)
        {
            cameraStream3->setPreviewSize(10);
        }
        isRecording[2] = true;
        break;
    case 3:
        // 设置CameraStream4的通道号为3
        cameraStream4->channelstream = 3;
        cameraStream4->startRecording();
        if(curchannel==4 || curchannel==5)
        {
            cameraStream4->setPreviewSize(5);
        }
        else if(curchannel==6)
        {
            cameraStream4->setPreviewSize(11);
        }
        isRecording[3] = true;
        break;
    }

    // 如果当前正在显示的是该通道，则禁用相机控制
    if (curchannel == channel || curchannel >= 4) {
        disableCameraControls();
    }
    saveChannelSettingsToIni(channelSettings,iniPath);

}

void MainWindow::stopRecordingByChannel(int channel) {
    // 根据通道号停止对应的录像
    switch(channel) {
    case 0:
        // 设置CameraStream1的通道号为0
        cameraStream1->channelstream = 0;
        cameraStream1->stopRecording();
        if(curchannel==4 || curchannel==5)
        {
            cameraStream1->setPreviewSize(4);
        }
        else if(curchannel==6)
        {
            cameraStream1->setPreviewSize(8);
        }
        isRecording[0] = false;
        break;
    case 1:
        // 设置CameraStream2的通道号为1
        cameraStream2->channelstream = 1;
        cameraStream2->stopRecording();
        if(curchannel==4 || curchannel==5)
        {
            cameraStream2->setPreviewSize(5);
        }
        else if(curchannel==6)
        {
            cameraStream2->setPreviewSize(9);
        }
        isRecording[1] = false;
        break;
    case 2:
        // 设置CameraStream3的通道号为2
        cameraStream3->channelstream = 2;
        cameraStream3->stopRecording();
        if(curchannel==4 || curchannel==5)
        {
            cameraStream3->setPreviewSize(4);
        }
        else if(curchannel==6)
        {
            cameraStream3->setPreviewSize(10);
        }
        isRecording[2] = false;
        break;
    case 3:
        // 设置CameraStream4的通道号为3
        cameraStream4->channelstream = 3;
        cameraStream4->stopRecording();
        if(curchannel==4 || curchannel==5)
        {
            cameraStream4->setPreviewSize(5);
        }
        else if(curchannel==6)
        {
            cameraStream4->setPreviewSize(11);
        }
        isRecording[3] = false;
        break;
    }

    // 强制进行内存清理，确保录像资源完全释放
    printf("录像停止后进行内存清理 - 通道 %d\n", channel);
    QCoreApplication::processEvents(QEventLoop::AllEvents, 100);


    // 如果当前正在显示的是该通道，则启用相机控制
    if (curchannel == channel) {
        enableCameraControls();
        ui->recordingSet->setEnabled(true);
    }
    saveChannelSettingsToIni(channelSettings,iniPath);
}


void MainWindow::setupbutton() {
    // 清空之前的记录
    qDeleteAll(camera1Buttons); camera1Buttons.clear();
    qDeleteAll(camera2Buttons); camera2Buttons.clear();
    qDeleteAll(camera3Buttons); camera3Buttons.clear();
    qDeleteAll(camera4Buttons); camera4Buttons.clear();

    // 设置4个摄像头的按钮配置
    pagesButtons[0] = { // 摄像头1
        {"", QIcon(":/image/icons/拍照.png"), "拍照.png"},
        {"", QIcon(":/image/icons/录像.png"), "录像.png"},
        //{"", QIcon(":/image/icons/旋转.png"), "旋转.png"}
    };
    pagesButtons[1] = { // 摄像头2
        {"", QIcon(":/image/icons/拍照.png"), "拍照.png"},
        {"", QIcon(":/image/icons/录像.png"), "录像.png"},
        //{"", QIcon(":/image/icons/旋转.png"), "旋转.png"}
    };
    pagesButtons[2] = { // 摄像头3
        {"", QIcon(":/image/icons/拍照.png"), "拍照.png"},
        {"", QIcon(":/image/icons/录像.png"), "录像.png"},
        //{"", QIcon(":/image/icons/旋转.png"), "旋转.png"}
    };
    pagesButtons[3] = { // 摄像头4
        {"", QIcon(":/image/icons/拍照.png"), "拍照.png"},
        {"", QIcon(":/image/icons/录像.png"), "录像.png"},
        //{"", QIcon(":/image/icons/旋转.png"), "旋转.png"}
    };

    // 初始化所有页面
    for (int i = 0; i < 7; ++i) {
        QWidget* page = ui->stackedWidget_2->widget(i);
        if (!page) {
            page = new QWidget();
            ui->stackedWidget_2->insertWidget(i, page);
        }

        // 清除原有内容
        QLayout* layout = page->layout();
        if (layout) {
            QLayoutItem* item;
            while ((item = layout->takeAt(0)) != nullptr) {
                delete item->widget();
                delete item;
            }
            delete layout;
        }

        // 根据页面类型初始化
        if (i < 4) {
            setupSingleCameraPage(page, i); // 1-4页
        } else if (i == 4) {
            setupCompositePage(page, 0, 1); // 第5页(1+2)
        } else if (i == 5) {
            setupCompositePage(page, 2, 3); // 第6页(3+4)
        } else if (i == 6) {
            setupGridPage(page, 0, 1, 2, 3); // 第7页(1+2+3+4)
        }
    }

    // 初始化所有摄像头区域的背景
    //initCameraBackgrounds();

}

void MainWindow::initCameraBackgrounds() {
    // 清空原有背景
    for (auto& pageBgs : pageBackgrounds) {
        qDeleteAll(pageBgs);
    }
    pageBackgrounds.clear();
    pageBackgrounds.resize(7); // 7个页面

    // 单摄像头页面（0-3）
    for (int i = 0; i < 4; ++i) {
        QWidget* page = ui->stackedWidget_2->widget(i);
        if (page) {
            QWidget* bg = new QWidget(page);
            bg->setStyleSheet("background-color: black;");
            bg->setAutoFillBackground(true);

            QVBoxLayout* layout = qobject_cast<QVBoxLayout*>(page->layout());
            if (layout) {
                layout->insertWidget(0, bg);
                layout->setStretch(0, 1);
            }

            pageBackgrounds[i].append(bg);
        }
    }

    // 双摄像头组合页面（4-5）
    for (int i = 4; i <= 5; ++i) {
        QWidget* page = ui->stackedWidget_2->widget(i);
        if (page) {
            QHBoxLayout* mainLayout = qobject_cast<QHBoxLayout*>(page->layout());
            if (mainLayout) {
                // 左侧摄像头背景
                QWidget* leftContainer = mainLayout->itemAt(0)->widget();
                if (leftContainer) {
                    QWidget* leftBg = new QWidget(leftContainer);
                    leftBg->setStyleSheet("background-color: black;");
                    leftBg->setAutoFillBackground(true);

                    QVBoxLayout* leftLayout = qobject_cast<QVBoxLayout*>(leftContainer->layout());
                    if (leftLayout) {
                        leftLayout->insertWidget(0, leftBg);
                        leftLayout->setStretch(0, 1);
                    }

                    pageBackgrounds[i].append(leftBg);
                }

                // 右侧摄像头背景
                QWidget* rightContainer = mainLayout->itemAt(2)->widget();
                if (rightContainer) {
                    QWidget* rightBg = new QWidget(rightContainer);
                    rightBg->setStyleSheet("background-color: black;");
                    rightBg->setAutoFillBackground(true);

                    QVBoxLayout* rightLayout = qobject_cast<QVBoxLayout*>(rightContainer->layout());
                    if (rightLayout) {
                        rightLayout->insertWidget(0, rightBg);
                        rightLayout->setStretch(0, 1);
                    }

                    pageBackgrounds[i].append(rightBg);
                }
            }
        }
    }

    // 四宫格页面（6）
    QWidget* gridPage = ui->stackedWidget_2->widget(6);
    if (gridPage) {
        QGridLayout* gridLayout = qobject_cast<QGridLayout*>(gridPage->layout());
        if (gridLayout) {
            // 左上摄像头背景
            QWidget* tlContainer = gridLayout->itemAtPosition(0, 0)->widget();
            if (tlContainer) {
                QWidget* tlBg = new QWidget(tlContainer);
                tlBg->setStyleSheet("background-color: black;");
                tlBg->setAutoFillBackground(true);

                QVBoxLayout* layout = qobject_cast<QVBoxLayout*>(tlContainer->layout());
                if (layout) {
                    layout->insertWidget(0, tlBg);
                    layout->setStretch(0, 1);
                }

                pageBackgrounds[6].append(tlBg);
            }

            // 右上摄像头背景
            QWidget* trContainer = gridLayout->itemAtPosition(0, 2)->widget();
            if (trContainer) {
                QWidget* trBg = new QWidget(trContainer);
                trBg->setStyleSheet("background-color: black;");
                trBg->setAutoFillBackground(true);

                QVBoxLayout* layout = qobject_cast<QVBoxLayout*>(trContainer->layout());
                if (layout) {
                    layout->insertWidget(0, trBg);
                    layout->setStretch(0, 1);
                }

                pageBackgrounds[6].append(trBg);
            }

            // 左下摄像头背景
            QWidget* blContainer = gridLayout->itemAtPosition(2, 0)->widget();
            if (blContainer) {
                QWidget* blBg = new QWidget(blContainer);
                blBg->setStyleSheet("background-color: black;");
                blBg->setAutoFillBackground(true);

                QVBoxLayout* layout = qobject_cast<QVBoxLayout*>(blContainer->layout());
                if (layout) {
                    layout->insertWidget(0, blBg);
                    layout->setStretch(0, 1);
                }

                pageBackgrounds[6].append(blBg);
            }

            // 右下摄像头背景
            QWidget* brContainer = gridLayout->itemAtPosition(2, 2)->widget();
            if (brContainer) {
                QWidget* brBg = new QWidget(brContainer);
                brBg->setStyleSheet("background-color: black;");
                brBg->setAutoFillBackground(true);

                QVBoxLayout* layout = qobject_cast<QVBoxLayout*>(brContainer->layout());
                if (layout) {
                    layout->insertWidget(0, brBg);
                    layout->setStretch(0, 1);
                }

                pageBackgrounds[6].append(brBg);
            }
        }
    }
}

void MainWindow::updateCameraBackgrounds() {
    // 单摄像头页面（0-3）
    // for (int i = 0; i < 4; ++i) {
    //     if (i < pageBackgrounds.size() && !pageBackgrounds[i].isEmpty()) {
    //         // 显示黑色背景当摄像头未打开时
    //         bool shouldShowBackground = !channelSettings[i].isOpen;
    //         pageBackgrounds[i][0]->setVisible(shouldShowBackground);

    //         // 调试信息
    //         // qDebug() << "Channel" << i << "isOpen:" << channelSettings[i].isOpen
    //         //          << "showBackground:" << shouldShowBackground;
    //     }
    // }

    // // 双摄像头组合页面（4-5）
    // // 页面4（摄像头1+2）
    // if (pageBackgrounds.size() > 4 && pageBackgrounds[4].size() >= 2) {
    //     pageBackgrounds[4][0]->setVisible(!channelSettings[0].isOpen); // 左（摄像头1）
    //     pageBackgrounds[4][1]->setVisible(!channelSettings[1].isOpen); // 右（摄像头2）
    // }
    // // 页面5（摄像头3+4）
    // if (pageBackgrounds.size() > 5 && pageBackgrounds[5].size() >= 2) {
    //     pageBackgrounds[5][0]->setVisible(!channelSettings[2].isOpen); // 左（摄像头3）
    //     pageBackgrounds[5][1]->setVisible(!channelSettings[3].isOpen); // 右（摄像头4）
    // }

    // // 四宫格页面（6）
    // if (pageBackgrounds.size() > 6 && pageBackgrounds[6].size() >= 4) {
    //     pageBackgrounds[6][0]->setVisible(!channelSettings[0].isOpen); // 左上（摄像头1）
    //     pageBackgrounds[6][1]->setVisible(!channelSettings[1].isOpen); // 右上（摄像头2）
    //     pageBackgrounds[6][2]->setVisible(!channelSettings[2].isOpen); // 左下（摄像头3）
    //     pageBackgrounds[6][3]->setVisible(!channelSettings[3].isOpen); // 右下（摄像头4）
    // }
}

void MainWindow::setupSingleCameraPage(QWidget* page, int cameraIndex) {
    QVBoxLayout* layout = new QVBoxLayout(page);
    layout->setContentsMargins(0, 0, 0, 0);
    page->setObjectName(QString("page_%1").arg(cameraIndex));

    // 直接使用page作为容器
    QWidget* labelContainer = setupDeviceInfoLabels(page, cameraIndex + 1,10);
    labelContainer->setObjectName("labelContainer");

    QWidget* buttonContainer = setupCameraButtons(page, pagesButtons[cameraIndex], cameraIndex + 1);
    buttonContainer->setObjectName("buttonContainer");

    layout->addWidget(labelContainer, 0, Qt::AlignHCenter);
    layout->addStretch();
    layout->addWidget(buttonContainer, 0, Qt::AlignHCenter);
}


void MainWindow::setupCompositePage(QWidget* page, int leftCamIndex, int rightCamIndex) {
    // 设置唯一页面对象名（使用摄像头索引）
    page->setObjectName(QString("compositePage_%1_%2").arg(leftCamIndex+1).arg(rightCamIndex+1));

    QHBoxLayout* layout = new QHBoxLayout(page);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->setSpacing(0);

    // 左侧摄像头区域
    QWidget* leftContainer = new QWidget(page);
    leftContainer->setObjectName("leftContainer");
    QVBoxLayout* leftLayout = new QVBoxLayout(leftContainer);
    leftLayout->setContentsMargins(0, 0, 0, 0);

    QWidget* leftLabel = setupDeviceInfoLabels(leftContainer, leftCamIndex + 1,6);
    leftLabel->setObjectName("labelContainer_1");

    QWidget* leftButton = setupCameraButtons(leftContainer, pagesButtons[leftCamIndex], leftCamIndex + 1);
    leftButton->setObjectName("buttonContainer_1");

    leftLayout->addWidget(leftLabel, 0, Qt::AlignHCenter);
    leftLayout->addStretch();
    leftLayout->addWidget(leftButton, 0, Qt::AlignHCenter);

    // 右侧摄像头区域
    QWidget* rightContainer = new QWidget(page);
    rightContainer->setObjectName("rightContainer");
    QVBoxLayout* rightLayout = new QVBoxLayout(rightContainer);
    rightLayout->setContentsMargins(0, 0, 0, 0);

    QWidget* rightLabel = setupDeviceInfoLabels(rightContainer, rightCamIndex + 1,6);
    rightLabel->setObjectName("labelContainer_2");

    QWidget* rightButton = setupCameraButtons(rightContainer, pagesButtons[rightCamIndex], rightCamIndex + 1);
    rightButton->setObjectName("buttonContainer_2");

    rightLayout->addWidget(rightLabel, 0, Qt::AlignHCenter);
    rightLayout->addStretch();
    rightLayout->addWidget(rightButton, 0, Qt::AlignHCenter);

    // 分隔线
    QFrame* vLine = createSeparatorLine(Qt::Vertical, 2);

    layout->addWidget(leftContainer);
    layout->addWidget(vLine);
    layout->addWidget(rightContainer);
}

void MainWindow::setupGridPage(QWidget* page, int tlIndex, int trIndex, int blIndex, int brIndex) {
    // 设置唯一页面对象名（使用四个摄像头索引）
    page->setObjectName(QString("gridPage_%1_%2_%3_%4")
                            .arg(tlIndex+1).arg(trIndex+1)
                            .arg(blIndex+1).arg(brIndex+1));

    QGridLayout* layout = new QGridLayout(page);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->setHorizontalSpacing(0);
    layout->setVerticalSpacing(0);

    // 左上区域（摄像头1）
    QWidget* tlContainer = new QWidget(page);
    tlContainer->setObjectName("tlContainer");
    QVBoxLayout* tlLayout = new QVBoxLayout(tlContainer);
    tlLayout->setContentsMargins(0, 0, 0, 0);
    QWidget* tlLabel = setupDeviceInfoLabels(tlContainer, tlIndex + 1,6);
    tlLabel->setObjectName("labelContainer_1");
    QWidget* tlButton = setupCameraButtons(tlContainer, pagesButtons[tlIndex], tlIndex + 1);
    tlButton->setObjectName("buttonContainer_1");
    tlLayout->addWidget(tlLabel, 0, Qt::AlignHCenter);
    tlLayout->addStretch();
    tlLayout->addWidget(tlButton, 0, Qt::AlignHCenter);

    // 右上区域（摄像头2）
    QWidget* trContainer = new QWidget(page);
    trContainer->setObjectName("trContainer");
    QVBoxLayout* trLayout = new QVBoxLayout(trContainer);
    trLayout->setContentsMargins(0, 0, 0, 0);
    QWidget* trLabel = setupDeviceInfoLabels(trContainer, trIndex + 1,6);
    trLabel->setObjectName("labelContainer_2");
    QWidget* trButton = setupCameraButtons(trContainer, pagesButtons[trIndex], trIndex + 1);
    trButton->setObjectName("buttonContainer_2");
    trLayout->addWidget(trLabel, 0, Qt::AlignHCenter);
    trLayout->addStretch();
    trLayout->addWidget(trButton, 0, Qt::AlignHCenter);

    // 左下区域（摄像头3）
    QWidget* blContainer = new QWidget(page);
    blContainer->setObjectName("blContainer");
    QVBoxLayout* blLayout = new QVBoxLayout(blContainer);
    blLayout->setContentsMargins(0, 0, 0, 0);
    QWidget* blLabel = setupDeviceInfoLabels(blContainer, blIndex + 1,6);
    blLabel->setObjectName("labelContainer_3");
    QWidget* blButton = setupCameraButtons(blContainer, pagesButtons[blIndex], blIndex + 1);
    blButton->setObjectName("buttonContainer_3");
    blLayout->addWidget(blLabel, 0, Qt::AlignHCenter);
    blLayout->addStretch();
    blLayout->addWidget(blButton, 0, Qt::AlignHCenter);

    // 右下区域（摄像头4）
    QWidget* brContainer = new QWidget(page);
    brContainer->setObjectName("brContainer");
    QVBoxLayout* brLayout = new QVBoxLayout(brContainer);
    brLayout->setContentsMargins(0, 0, 0, 0);
    QWidget* brLabel = setupDeviceInfoLabels(brContainer, brIndex + 1,6);
    brLabel->setObjectName("labelContainer_4");
    QWidget* brButton = setupCameraButtons(brContainer, pagesButtons[brIndex], brIndex + 1);
    brButton->setObjectName("buttonContainer_4");
    brLayout->addWidget(brLabel, 0, Qt::AlignHCenter);
    brLayout->addStretch();
    brLayout->addWidget(brButton, 0, Qt::AlignHCenter);

    // 创建分隔线
    QFrame* vLine = createSeparatorLine(Qt::Vertical, 2);
    QFrame* hLine = createSeparatorLine(Qt::Horizontal, 2);

    // 布局设置
    layout->addWidget(tlContainer, 0, 0);
    layout->addWidget(vLine, 0, 1, 3, 1);
    layout->addWidget(trContainer, 0, 2);

    layout->addWidget(hLine, 1, 0, 1, 3);

    layout->addWidget(blContainer, 2, 0);
    layout->addWidget(brContainer, 2, 2);

    layout->setColumnStretch(0, 1);
    layout->setColumnStretch(1, 0);
    layout->setColumnStretch(2, 1);
    layout->setRowStretch(0, 1);
    layout->setRowStretch(1, 0);
    layout->setRowStretch(2, 1);
}


QFrame* MainWindow::createSeparatorLine(Qt::Orientation orientation, int width) {
    QFrame* line = new QFrame();
    line->setFrameShape(orientation == Qt::Horizontal ? QFrame::HLine : QFrame::VLine);
    line->setFrameShadow(QFrame::Sunken);
    line->setLineWidth(width / 2);
    line->setMidLineWidth(0);

    if (orientation == Qt::Horizontal) {
        line->setFixedHeight(width);
        line->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    } else {
        line->setFixedWidth(width);
        line->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Expanding);
    }

    line->setStyleSheet(QString("background-color: rgb(255, 255, 255);"));
    return line;
}

// 创建设备信息标签
QWidget* MainWindow::setupDeviceInfoLabels(QWidget* parent, int cameraId, int fontSize) {
    QWidget* labelContainer = new QWidget(parent);
    labelContainer->setStyleSheet(
        "background-color: rgba(0, 0, 0, 100);"
        "border-radius: 10px;"
        "padding: 8px 15px;"
    );

    QHBoxLayout* labelLayout = new QHBoxLayout(labelContainer);
    labelLayout->setContentsMargins(0, 0, 0, 0);
    labelLayout->setSpacing(8);

    QStringList labelsText = {
        curdevicename[cameraId-1],
        QString("%1: %2").arg(systemmanager->getTranslatedText("resolution_label",currentLanguageIndex,"mainwindow")).arg(curfblname[cameraId-1]),
        systemmanager->getTranslatedText("encoding_format",currentLanguageIndex,"mainwindow"),
        QString("%1: %2").arg(systemmanager->getTranslatedText("audio",currentLanguageIndex,"mainwindow")).arg(systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")),
        QString("%1: %2").arg(systemmanager->getTranslatedText("motion_detection",currentLanguageIndex,"mainwindow")).arg(systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow"))
    };

    for (int i = 0; i < labelsText.size(); ++i) {
        QLabel* label = new QLabel(labelsText[i], labelContainer);

        // 设置字体大小
        QFont font = label->font();
        font.setPointSize(fontSize);
        label->setFont(font);

        // 设置样式（第一个 label 加粗）
        label->setStyleSheet(i == 0 ?
            "QLabel { color: white; font-weight: bold; }" :
            "QLabel { color: white; }");

        labelLayout->addWidget(label);
    }

    labelLayout->insertStretch(0);
    labelLayout->addStretch();

    labelContainer->hide();
    return labelContainer;
}


void MainWindow::updateDeviceLabels(int pageIndex) {
    QWidget* page = findChild<QWidget*>(QString("page_%1").arg(pageIndex));
    if (!page) return;

    QWidget* labelContainer = page->findChild<QWidget*>("labelContainer");
    if (!labelContainer) return;

    QList<QLabel*> labels = labelContainer->findChildren<QLabel*>();
    if (labels.size() >= 2) {
        labels[0]->setText(curdevicename[pageIndex]);
        labels[1]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("resolution_label",currentLanguageIndex,"mainwindow")).arg(curfblname[pageIndex]));
        labels[2]->setText(systemmanager->getTranslatedText("encoding_format",currentLanguageIndex,"mainwindow"));
        labels[3]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("audio",currentLanguageIndex,"mainwindow")) .arg(curaudiostate[pageIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));  // 录音
        labels[4]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("motion_detection",currentLanguageIndex,"mainwindow")).arg(curmotionstate[pageIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));// 移动侦测
    }
}

void MainWindow::updateCompositePageLabels(int leftIndex, int rightIndex, bool updateLeft, bool updateRight) {
    // 使用唯一对象名查找页面
    QWidget* page = findChild<QWidget*>(QString("compositePage_%1_%2").arg(leftIndex+1).arg(rightIndex+1));
    if (!page) {
        qDebug() << "Composite page not found for cameras" << leftIndex+1 << "and" << rightIndex+1;
        return;
    }

    // 更新左侧标签
    if (updateLeft) {
        QWidget* leftContainer = page->findChild<QWidget*>("leftContainer");
        if (leftContainer) {
            QWidget* labelContainer = leftContainer->findChild<QWidget*>("labelContainer_1");
            if (labelContainer) {
                QList<QLabel*> labels = labelContainer->findChildren<QLabel*>();
                if (labels.size() >= 2) {
                    labels[0]->setText(curdevicename[leftIndex]);
                    labels[1]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("resolution_label",currentLanguageIndex,"mainwindow")).arg(curfblname[leftIndex]));
                    labels[2]->setText(systemmanager->getTranslatedText("encoding_format",currentLanguageIndex,"mainwindow"));
                    labels[3]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("audio",currentLanguageIndex,"mainwindow")) .arg(curaudiostate[leftIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));  // 录音
                    labels[4]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("motion_detection",currentLanguageIndex,"mainwindow")).arg(curmotionstate[leftIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));// 移动侦测
                }
            }
        }
    }

    // 更新右侧标签
    if (updateRight) {
        QWidget* rightContainer = page->findChild<QWidget*>("rightContainer");
        if (rightContainer) {
            QWidget* labelContainer = rightContainer->findChild<QWidget*>("labelContainer_2");
            if (labelContainer) {
                QList<QLabel*> labels = labelContainer->findChildren<QLabel*>();
                if (labels.size() >= 2) {
                    labels[0]->setText(curdevicename[leftIndex]);
                    labels[1]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("resolution_label",currentLanguageIndex,"mainwindow")).arg(curfblname[leftIndex]));
                    labels[2]->setText(systemmanager->getTranslatedText("encoding_format",currentLanguageIndex,"mainwindow"));
                    labels[3]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("audio",currentLanguageIndex,"mainwindow")) .arg(curaudiostate[leftIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));  // 录音
                    labels[4]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("motion_detection",currentLanguageIndex,"mainwindow")).arg(curmotionstate[leftIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));// 移动侦测
                }
            }
        }
    }
}

void MainWindow::updateGridPageLabels(int tlIndex, int trIndex, int blIndex, int brIndex,
                                      bool updateTL, bool updateTR, bool updateBL, bool updateBR) {
    // 使用唯一对象名查找页面
    QWidget* page = findChild<QWidget*>(QString("gridPage_%1_%2_%3_%4")
                                             .arg(tlIndex+1).arg(trIndex+1)
                                             .arg(blIndex+1).arg(brIndex+1));
    if (!page) {
        qDebug() << "Grid page not found for cameras"
                 << tlIndex+1 << trIndex+1 << blIndex+1 << brIndex+1;
        return;
    }

    // 更新左上区域
    if (updateTL) {
        QWidget* tlContainer = page->findChild<QWidget*>("tlContainer");
        if (tlContainer) {
            QWidget* labelContainer = tlContainer->findChild<QWidget*>("labelContainer_1");
            if (labelContainer) {
                QList<QLabel*> labels = labelContainer->findChildren<QLabel*>();
                if (labels.size() >= 2) {
                    labels[0]->setText(curdevicename[tlIndex]);
                    labels[1]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("resolution_label",currentLanguageIndex,"mainwindow")).arg(curfblname[tlIndex]));
                    labels[2]->setText(systemmanager->getTranslatedText("encoding_format",currentLanguageIndex,"mainwindow"));
                    labels[3]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("audio",currentLanguageIndex,"mainwindow")) .arg(curaudiostate[tlIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));  // 录音
                    labels[4]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("motion_detection",currentLanguageIndex,"mainwindow")).arg(curmotionstate[tlIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));// 移动侦测
                }
            }
        }
    }

    // 更新右上区域
    if (updateTR) {
        QWidget* trContainer = page->findChild<QWidget*>("trContainer");
        if (trContainer) {
            QWidget* labelContainer = trContainer->findChild<QWidget*>("labelContainer_2");
            if (labelContainer) {
                QList<QLabel*> labels = labelContainer->findChildren<QLabel*>();
                if (labels.size() >= 2) {
                    labels[0]->setText(curdevicename[trIndex]);
                    labels[1]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("resolution_label",currentLanguageIndex,"mainwindow")).arg(curfblname[trIndex]));
                    labels[2]->setText(systemmanager->getTranslatedText("encoding_format",currentLanguageIndex,"mainwindow"));
                    labels[3]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("audio",currentLanguageIndex,"mainwindow")) .arg(curaudiostate[trIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));  // 录音
                    labels[4]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("motion_detection",currentLanguageIndex,"mainwindow")).arg(curmotionstate[trIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));// 移动侦测
                }
            }
        }
    }

    // 更新左下区域
    if (updateBL) {
        QWidget* blContainer = page->findChild<QWidget*>("blContainer");
        if (blContainer) {
            QWidget* labelContainer = blContainer->findChild<QWidget*>("labelContainer_3");
            if (labelContainer) {
                QList<QLabel*> labels = labelContainer->findChildren<QLabel*>();
                if (labels.size() >= 2) {
                    labels[0]->setText(curdevicename[blIndex]);
                    labels[1]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("resolution_label",currentLanguageIndex,"mainwindow")).arg(curfblname[blIndex]));
                    labels[2]->setText(systemmanager->getTranslatedText("encoding_format",currentLanguageIndex,"mainwindow"));
                    labels[3]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("audio",currentLanguageIndex,"mainwindow")) .arg(curaudiostate[blIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));  // 录音
                    labels[4]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("motion_detection",currentLanguageIndex,"mainwindow")).arg(curmotionstate[blIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));// 移动侦测
                }
            }
        }
    }

    // 更新右下区域
    if (updateBR) {
        QWidget* brContainer = page->findChild<QWidget*>("brContainer");
        if (brContainer) {
            QWidget* labelContainer = brContainer->findChild<QWidget*>("labelContainer_4");
            if (labelContainer) {
                QList<QLabel*> labels = labelContainer->findChildren<QLabel*>();
                if (labels.size() >= 2) {
                    labels[0]->setText(curdevicename[brIndex]);
                    labels[1]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("resolution_label",currentLanguageIndex,"mainwindow")).arg(curfblname[brIndex]));
                    labels[2]->setText(systemmanager->getTranslatedText("encoding_format",currentLanguageIndex,"mainwindow"));
                    labels[3]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("audio",currentLanguageIndex,"mainwindow")) .arg(curaudiostate[brIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));  // 录音
                    labels[4]->setText(QString("%1: %2").arg(systemmanager->getTranslatedText("motion_detection",currentLanguageIndex,"mainwindow")).arg(curmotionstate[brIndex] ? systemmanager->getTranslatedText("on",currentLanguageIndex,"mainwindow") : systemmanager->getTranslatedText("off",currentLanguageIndex,"mainwindow")));// 移动侦测
                }
            }
        }
    }
}

QWidget* MainWindow::setupCameraButtons(QWidget* parent, const QVector<ButtonInfo>& buttonsInfo, int cameraId) {
    // 按钮容器
    QWidget* buttonContainer = new QWidget(parent);
    buttonContainer->setStyleSheet("background-color: rgba(0, 0, 0, 100); border-radius: 10px;");
    buttonContainer->setMaximumWidth(350);

    QHBoxLayout* buttonLayout = new QHBoxLayout(buttonContainer);
    buttonLayout->setContentsMargins(10, 5, 10, 5);
    buttonLayout->setSpacing(15);

    // 时间标签
    QLabel* timeLabel = new QLabel("00:00:00", buttonContainer);
    timeLabel->setStyleSheet("color: white; font-weight: bold;");
    timeLabel->setMinimumWidth(60);
    timeLabel->hide();

    // 创建按钮
    for (const ButtonInfo &info : buttonsInfo) {
        QPushButton* button = new QPushButton(info.text, buttonContainer);
        button->setIcon(info.image);
        button->setIconSize(QSize(24, 24));
        //button->setCursor(Qt::PointingHandCursor);
        button->setStyleSheet(
            "QPushButton {"
            "   background-color: #4a86e8;"
            "   color: white;"
            "   border-radius: 6px;"
            "   padding: 2px;"
            "   min-width: 40px;"
            "   min-height: 26px;"
            "}"
            "QPushButton:hover { background-color: #3d73c5; }"
            "QPushButton:pressed { background-color: #2b5699; }"
            );

        if (info.iconName == "录像.png") {
            RecordingButtonData* data = new RecordingButtonData(button, info.image);
            data->timeLabel = timeLabel;
            data->recordingTimer = new QTimer(this);

            connect(data->recordingTimer, &QTimer::timeout, [data]() {
                data->elapsedTime++;
                int h = data->elapsedTime / 3600;
                int m = (data->elapsedTime % 3600) / 60;
                int s = data->elapsedTime % 60;
                data->timeLabel->setText(QString("%1:%2:%3")
                                             .arg(h, 2, 10, QLatin1Char('0'))
                                             .arg(m, 2, 10, QLatin1Char('0'))
                                             .arg(s, 2, 10, QLatin1Char('0')));
            });

            connect(button, &QPushButton::clicked, [this, cameraId]() {
                toggleRecordingForCamera(cameraId);
            });

            // 添加到对应的摄像头组
            switch(cameraId) {
            case 1: camera1Buttons.append(data); break;
            case 2: camera2Buttons.append(data); break;
            case 3: camera3Buttons.append(data); break;
            case 4: camera4Buttons.append(data); break;
            }
        }else if (info.iconName == "拍照.png") {
            // 连接拍照按钮的点击事件
            connect(button, &QPushButton::clicked, [this, cameraId]() {
                takePhoto(cameraId);
            });
        }
        else if (info.iconName == "旋转.png") {
            // 连接旋转按钮的点击事件
            connect(button, &QPushButton::clicked, [this, cameraId]() {
                rotateCamera(cameraId);
            });
        }

        buttonLayout->addWidget(button);
    }

    buttonLayout->addWidget(timeLabel);
    buttonLayout->addStretch();

    buttonContainer->hide(); // 默认隐藏
    return buttonContainer;
}


void MainWindow::toggleRecordingForCamera(int cameraId) {
    static QIcon recordIcon(":/image/icons/录像.png");
    static QIcon stopIcon(":/image/icons/停止.png");
    int i=0;
    QVector<RecordingButtonData*>* targetButtons = nullptr;
    switch(cameraId) {
    case 1: targetButtons = &camera1Buttons; break;
    case 2: targetButtons = &camera2Buttons; break;
    case 3: targetButtons = &camera3Buttons; break;
    case 4: targetButtons = &camera4Buttons; break;
    }

    if (targetButtons && !targetButtons->isEmpty()) {
        bool newState = !targetButtons->first()->isRecording;

        // 如果是要启动录像，先检查录像限制
        if (newState) {
            if (!checkRecordingLimits(cameraId-1)) {
                // 如果超出限制，不改变任何状态，直接返回
                return;
            }
        }

        for (auto data : *targetButtons) {
            data->isRecording = newState;
            data->button->setIcon(newState ? stopIcon : recordIcon);

            if (newState) {
                // 启动录像 - 摄像头ID需要减1以匹配通道索引(0-3)
                if(i==0)
                {
                        startRecordingByChannel(cameraId - 1);
                }
                i=1;
                data->elapsedTime = 0;
                data->timeLabel->setText("00:00:00");
                data->timeLabel->show();
                data->recordingTimer->start(1000);
                ui->recordingSet->setEnabled(false);
            } else {
                data->recordingTimer->stop();
                data->timeLabel->hide();

                // 停止录像 - 摄像头ID需要减1以匹配通道索引(0-3)
                if(i==0)
                    stopRecordingByChannel(cameraId - 1);
                i=1;

            }
        }
    }
}

void MainWindow::TimeWatermark(){
    CameraStream* cameraStreams[4] = {
        cameraStream1,
        cameraStream2,
        cameraStream3,
        cameraStream4
    };
    CameraStream *stream = cameraStreams[curchannel];
    // 只处理已开启的通道
    timewatermark[curchannel]=!timewatermark[curchannel];
    if(channelSettings[curchannel].isOpen) {
        // 切换当前通道时间标签的显示状态
        saveChannelSettingsToIni(channelSettings,iniPath);
        stream->updateTimeWatermark();
    }
}


void MainWindow::onMouseDeviceChanged() {
    // 检查鼠标设备是否存在
    QPair<QString, QString> mouse = findRealMouseDevice();
    if (mouse.first.isEmpty()) {
        stopSystemDoubleClickMonitor();
        qDebug() << "Mouse device removed";
    } else {
        stopSystemDoubleClickMonitor();
        startSystemDoubleClickMonitor();
        qDebug() << "New mouse device detected:" << mouse.second;
    }
}

// 检查录像限制
bool MainWindow::checkRecordingLimits(int channel) {
    if (channel < 0 || channel >= 4 || !channelSettings[channel].isOpen) {
        return false;
    }

    QVariantMap resolution = channelSettings[channel].resolution;
    if (resolution.isEmpty()) {
        return false;
    }

    int width = resolution["width"].toInt();
    int height = resolution["height"].toInt();

    int framerate_num = resolution.contains("framerate_num") ? resolution["framerate_num"].toInt() : 30;
    int framerate_den = resolution.contains("framerate_den") ? resolution["framerate_den"].toInt() : 1;

    // 计算实际帧率
    float fps = (float)framerate_num / framerate_den;

    // 计算当前通道的负载
    int channelLoad = 0;
    if (width == 1280 && height == 720 && fps <= 30) {
        channelLoad = 1; // 720P30 = 1个单位
    } else if (width == 1920 && height == 1080 && fps <= 30) {
        channelLoad = 2; // 1080P30 = 2个单位
    } else if (width == 1920 && height == 1080 && fps <= 60) {
        channelLoad = 4; // 1080P60 = 4个单位
    } else if (width == 2592 && height == 1944 && fps <= 25) {
        channelLoad = 4; // 2592*1944@25 = 4个单位
    }
    else {
        // 其他分辨率按比例计算
        float pixelRatio = (float)(width * height) / (1280 * 720);
        float fpsRatio = fps / 30.0f;
        channelLoad = (int)(pixelRatio * fpsRatio + 0.5f);
    }

    // 计算当前已有的录像负载（不包括要启动的通道）
    int currentLoad = calculateRecordingLoad(channel);

    qDebug()<<"currentLoad channelLoad"<<currentLoad<<channelLoad;
    // 检查总负载是否超过限制（最大4个单位）
    if (currentLoad + channelLoad > 4) {
        // 显示限制提示
        QString currentResStr = QString("%1x%2@%3fps").arg(width).arg(height).arg(fps, 0, 'f', 1);
        // QString message = QString("录像限制：无法启动 %1 录像。\n\n").arg(currentResStr);
        // message += "当前系统支持的录像组合：\n";
        // message += "• 4个 720P30 录像\n";
        // message += "• 2个 1080P30 录像\n";
        // message += "• 1个 1080P60 录像\n";
        // message += "• 1个 2592×1944@25fps 录像\n\n";
        // message += QString("当前负载：%1/4，新增负载：%2").arg(currentLoad).arg(channelLoad);
        QString message =systemmanager->getTranslatedText("recording_limit_message",currentLanguageIndex,"mainwindow").arg(currentResStr).arg(currentLoad).arg(channelLoad);

                // 显示录像限制警告
                CustomMessageBox::warning(this,
                    systemmanager->getTranslatedText("recording_limit_title",currentLanguageIndex,"mainwindow"),
                    message);
                return false;
            }

    return true;
}



// 计算当前录像负载
int MainWindow::calculateRecordingLoad(int channel) {
    int totalLoad = 0;

    for (int i = 0; i < 4; i++) {
        if(channel == i)
            continue;
        if (isRecording[i] && channelSettings[i].isOpen) {
            QVariantMap resolution = channelSettings[i].resolution;
            if (!resolution.isEmpty()) {
                int width = resolution["width"].toInt();
                int height = resolution["height"].toInt();
                int framerate_num = resolution.contains("framerate_num") ? resolution["framerate_num"].toInt() : 30;
                int framerate_den = resolution.contains("framerate_den") ? resolution["framerate_den"].toInt() : 1;

                // 计算实际帧率
                float fps = (float)framerate_num / framerate_den;

                // 根据分辨率和帧率计算负载权重
                if (width == 1280 && height == 720 && fps <= 30) {
                    totalLoad += 1; // 720P30 = 1个单位
                } else if (width == 1920 && height == 1080 && fps <= 30) {
                    totalLoad += 2; // 1080P30 = 2个单位
                } else if (width == 1920 && height == 1080 && fps <= 60) {
                    totalLoad += 4; // 1080P60 = 4个单位
                } else if (width == 2592 && height == 1944 && fps <= 25) {
                    totalLoad += 4; // 2592*1944@25 = 4个单位
                } else {
                    // 其他分辨率按比例计算
                    float pixelRatio = (float)(width * height) / (1280 * 720);
                    float fpsRatio = fps / 30.0f;
                    totalLoad += (int)(pixelRatio * fpsRatio + 0.5f);
                }
            }
        }
    }

    return totalLoad;
}

// 更新控制按钮可见性
void MainWindow::updateControlButtonsVisibility()
{
    QWidget* currentPage = ui->stackedWidget_2->currentWidget();
    if (!currentPage) return;

    auto [areaIndex, isCameraOpen] = getCurrentMouseAreaState();
    int pageIndex = ui->stackedWidget_2->currentIndex();

    // 处理所有页面类型的控件显隐
    if (pageIndex < 4) { // 单摄像头页面
        bool visible = (areaIndex == 0) && isCameraOpen;
        updateSinglePageVisibility(currentPage, visible);
    }
    else if (pageIndex == 4 || pageIndex == 5) { // 双摄像头组合
        // 获取两个区域的控件
        QWidget* buttonContainer1 = currentPage->findChild<QWidget*>("buttonContainer_1");
        QWidget* labelContainer1 = currentPage->findChild<QWidget*>("labelContainer_1");
        QWidget* buttonContainer2 = currentPage->findChild<QWidget*>("buttonContainer_2");
        QWidget* labelContainer2 = currentPage->findChild<QWidget*>("labelContainer_2");

        // 确定当前激活的区域
        bool leftActive = (areaIndex == 1) && isCameraOpen;
        bool rightActive = (areaIndex == 2) && isCameraOpen;

        // 设置控件可见性
        if (buttonContainer1) buttonContainer1->setVisible(leftActive);
        if (labelContainer1) labelContainer1->setVisible(leftActive);
        if (buttonContainer2) buttonContainer2->setVisible(rightActive);
        if (labelContainer2) labelContainer2->setVisible(rightActive);
    }
    else if (pageIndex == 6) { // 四宫格
        // 处理四个区域的控件
        for (int i = 1; i <= 4; ++i) {
            QString suffix = QString::number(i);
            QWidget* buttonContainer = currentPage->findChild<QWidget*>("buttonContainer_" + suffix);
            QWidget* labelContainer = currentPage->findChild<QWidget*>("labelContainer_" + suffix);

            bool isActive = (areaIndex == i) && isCameraOpen;

            if (buttonContainer) buttonContainer->setVisible(isActive);
            if (labelContainer) labelContainer->setVisible(isActive);
        }
    }
}

void MainWindow::updateSinglePageVisibility(QWidget* page, bool visible)
{
    QWidget* buttonContainer = page->findChild<QWidget*>("buttonContainer");
    QWidget* labelContainer = page->findChild<QWidget*>("labelContainer");

    if (buttonContainer) buttonContainer->setVisible(visible);
    if (labelContainer) labelContainer->setVisible(visible);
}




void MainWindow::restoreSelection()
{
    // 设置当前设备、格式和分辨率
    currentDevicePath = channelSettings[curchannel].devicePath;
    currentFormat = channelSettings[curchannel].format;
    currentResolution = channelSettings[curchannel].resolution;

    // 查找并设置设备索引
    for (int i = 0; i < ui->comboBox_cameradev->count(); ++i) {
        QString data = ui->comboBox_cameradev->itemData(i).toString();
        if (data.contains(currentDevicePath)) {
            channelSettings[curchannel].videoIndex = i;
            break;
        }
    }

    // 恢复UI选项
    if (channelSettings[curchannel].videoIndex >= 0)
    {
        ui->comboBox_cameradev->setCurrentIndex(channelSettings[curchannel].videoIndex);

        // 更新视频格式列表
        ui->comboBox_videoformat->clear();
        QList<QPair<QString, QVariantMap>> formats = cameraParams->findVideoFormats(currentDevicePath);
        for (const auto &format : formats) {
            ui->comboBox_videoformat->addItem(format.first, format.second);
        }

        // 恢复格式选择
        if (channelSettings[curchannel].formatIndex >= 0) {
            ui->comboBox_videoformat->setCurrentIndex(channelSettings[curchannel].formatIndex);

            // 更新分辨率列表
            ui->comboBox_fbl->clear();
            uint32_t pixelformat = currentFormat["pixelformat"].toUInt();
            QList<QPair<QString, QVariantMap>> resolutions = cameraParams->findVideoResolutions(currentDevicePath, pixelformat);
            for (const auto &resolution : resolutions) {
                ui->comboBox_fbl->addItem(resolution.first, resolution.second);
            }

            // 恢复分辨率选择
            if (channelSettings[curchannel].resolutionIndex >= 0) {
                ui->comboBox_fbl->setCurrentIndex(channelSettings[curchannel].resolutionIndex);
            }
        }
    }

}

void MainWindow::rotateCamera(int cameraId) {
    if(cameraId == 1)
    {
        cameraStream1->rotateCamera();
    }
    else if(cameraId == 2)
    {
        cameraStream2->rotateCamera();
    }
    else if(cameraId == 3)
    {
        cameraStream3->rotateCamera();
    }
    else if(cameraId == 4)
    {
        cameraStream4->rotateCamera();
    }
}

void MainWindow::takePhoto(int cameraId) {

    // 根据相机ID选择对应的CameraStream对象
    CameraStream* stream = nullptr;

    switch (cameraId) {
        case 1:
            stream = cameraStream1;
            break;
        case 2:
            stream = cameraStream2;
            break;
        case 3:
            stream = cameraStream3;
            break;
        case 4:
            stream = cameraStream4;
            break;
        default:
            qDebug() << "invalidCameraID:" << cameraId;
            return;
    }

    // 确保相机已打开
    if (!stream || !channelSettings[cameraId-1].isOpen) {
        //qDebug() << "camera" << cameraId  << "itIsNotOpenedAndCannotTakePhotos";
        return;
    }

    // 调用对应CameraStream的takePhoto方法
    stream->takePhoto();
}

void MainWindow::handleGstreamerError(const QString &errorMessage, const QString &errorDetails, int channelId)
{
    // 解析错误类型
    QString actualErrorDetails = errorDetails;
    QString errorType = "OTHER_ERROR";  // 默认为其他错误

    // 检查是否包含错误类型信息
    if (errorDetails.contains("|TYPE:")) {
        QStringList parts = errorDetails.split("|TYPE:");
        if (parts.size() == 2) {
            actualErrorDetails = parts[0];
            errorType = parts[1];
        }
    }

    // 如果该通道正在录像，先停止录像（发送EOS）然后重置录像图标和时间标签
    if (isRecording[channelId]) {

        // 获取对应的CameraStream并停止录像（发送EOS信号）
        CameraStream* cameraStreams[4] = {cameraStream1, cameraStream2, cameraStream3, cameraStream4};
        if (channelId >= 0 && channelId < 4 && cameraStreams[channelId]) {
            // 调用stopRecording会发送EOS信号并正确关闭MP4文件
            cameraStreams[channelId]->stopRecording();
            //qDebug() << "通道" << channelId << "录像已停止，MP4文件已正确关闭";
        }

        //isRecording[channelId] = false;

        // 重置录像按钮图标和时间标签
        static QIcon recordIcon(":/image/icons/录像.png");
        QVector<RecordingButtonData*>* targetButtons = nullptr;

        // 根据通道ID选择对应的按钮组（channelId是0-3，cameraId是1-4）
        switch(channelId + 1) {
        case 1: targetButtons = &camera1Buttons; break;
        case 2: targetButtons = &camera2Buttons; break;
        case 3: targetButtons = &camera3Buttons; break;
        case 4: targetButtons = &camera4Buttons; break;
        }

        if (targetButtons && !targetButtons->isEmpty()) {
            for (auto data : *targetButtons) {
                data->isRecording = false;
                data->button->setIcon(recordIcon);
                data->recordingTimer->stop();
                data->timeLabel->hide();
                data->elapsedTime = 0;
            }
        }

        // 启用录像设置按钮
        ui->recordingSet->setEnabled(true);
    } else {
        isRecording[channelId] = false;
    }

    // 切换到对应的通道标签页
    if (channelId >= 0 && channelId < 4) {
        // 保存当前通道
        int prevChannel = curchannel;
        // 设置为出错的通道
        curchannel = channelId;

        // 确保对应通道的视频已经被关闭
        clearWhichVideo(channelId);

        // 重置设备选择
        if (prevChannel == channelId) {
            // 如果当前就在出错的通道页面，直接更新UI
            ui->comboBox_cameradev->setCurrentIndex(-1);
            ui->comboBox_videoformat->clear();
            ui->comboBox_fbl->clear();
            ui->comboBox_audiodev->clear();  // 清空音频设备列表
            updateAudioCheckboxState();  // 更新音频复选框状态
            currentDevicePath = "";
            enableCameraControls();
        }

        // 重置对应CameraStream的预览状态，确保下次切换页面时背景正确
        CameraStream* cameraStreams[4] = {cameraStream1, cameraStream2, cameraStream3, cameraStream4};
        if (channelId >= 0 && channelId < 4 && cameraStreams[channelId]) {
            cameraStreams[channelId]->setPreviewPaused(false);  // 重置预览暂停状态
        }
        // 恢复原来的通道
        if (prevChannel != channelId) {
            curchannel = prevChannel;
        }
    }

    // 根据错误类型决定是否显示弹窗
    if (errorType == "RUNTIME_DISCONNECTION") {
        // 摄像头运行时断开，不显示弹窗，只清理资源

        // 如果该通道已有错误对话框，先关闭
        if (errorDialogs.contains(channelId) && errorDialogs[channelId]) {
            errorDialogs[channelId]->close();
            errorDialogs.remove(channelId);
        }
    } else {
        // 其他错误（包括带宽不足等），显示错误弹窗

        // 如果该通道已有错误对话框，先关闭
        if (errorDialogs.contains(channelId) && errorDialogs[channelId]) {
            errorDialogs[channelId]->close();
            errorDialogs.remove(channelId);
        }

        // 创建新的错误对话框
        CustomMessageBox *errorBox = new CustomMessageBox(this);
        errorBox->setDialogTitle(systemmanager->getTranslatedText("error_title",currentLanguageIndex,"mainwindow"));

        QString errorText;
        if (errorType == "BANDWIDTH_ERROR") {
            errorText = systemmanager->getTranslatedText("bandwidth_error",currentLanguageIndex,"mainwindow")
                       .arg(channelId + 1);
        } else {
            errorText = systemmanager->getTranslatedText("runtime_error",currentLanguageIndex,"mainwindow")
                       .arg(channelId + 1);
        }

        errorBox->setText(errorText);
        errorBox->setIcon(CustomMessageBox::Critical);
        errorBox->addButton(CustomMessageBox::Ok);

        // 存储到 map 中
        errorDialogs[channelId] = errorBox;

        // 对话框关闭时清理指针
        connect(errorBox, &CustomDialog::finished, this, [this, channelId]() {
            if (errorDialogs.contains(channelId)) {
                errorDialogs.remove(channelId);
            }
        });

        errorBox->exec();
    }
}

// 保存热插拔数据到INI文件
void MainWindow::saveHotplugDataToIni(int channel, const QString &iniPath) {
    if (channel < 0 || channel >= 4) return;

    QSettings ini(iniPath, QSettings::IniFormat);
    QString group = QString("Hotplug_Channel_%1").arg(channel);
    ini.beginGroup(group);

    // 保存当前运行状态到热插拔数据
    if (channelSettings[channel].isOpen) {
        channelSettings[channel].wasRunningBeforeUnplug = true;
        channelSettings[channel].unpluggedUsbInterface =channelSettings[channel].UsbInterface;
        channelSettings[channel].unpluggedProductVid =channelSettings[channel].ProductVid;
        channelSettings[channel].unpluggedDeviceName = channelSettings[channel].devicename;
        channelSettings[channel].unpluggedFormat = channelSettings[channel].format;
        channelSettings[channel].unpluggedResolution = channelSettings[channel].resolution;
        channelSettings[channel].unpluggedFormatIndex = channelSettings[channel].formatIndex;
        channelSettings[channel].unpluggedResolutionIndex = channelSettings[channel].resolutionIndex;
        channelSettings[channel].unpluggedFBLname = channelSettings[channel].FBLname;

        // 保存音频设备信息
        channelSettings[channel].unpluggedAudioDeviceName = channelSettings[channel].audioDeviceName;
        channelSettings[channel].unpluggedAudioDevicePath = channelSettings[channel].audioDevicePath;
        channelSettings[channel].unpluggedAudioDeviceIndex = channelSettings[channel].audioDeviceIndex;
        channelSettings[channel].unpluggedAudioEnabled = channelSettings[channel].audioEnabled;

        // 保存到INI文件的Hotplug组
        ini.setValue("wasRunning", true);
        ini.setValue("usbInterface", channelSettings[channel].unpluggedUsbInterface);
        ini.setValue("productVid", channelSettings[channel].unpluggedProductVid);
        ini.setValue("deviceName", channelSettings[channel].unpluggedDeviceName);
        ini.setValue("format", channelSettings[channel].unpluggedFormat);
        ini.setValue("resolution", channelSettings[channel].unpluggedResolution);
        ini.setValue("formatIndex", channelSettings[channel].unpluggedFormatIndex);
        ini.setValue("resolutionIndex", channelSettings[channel].unpluggedResolutionIndex);
        ini.setValue("FBLname", channelSettings[channel].unpluggedFBLname);
        ini.setValue("isRecording",isRecording[channel]);

        // 保存音频设备信息到INI
        ini.setValue("audioDeviceName", channelSettings[channel].unpluggedAudioDeviceName);
        ini.setValue("audioDevicePath", channelSettings[channel].unpluggedAudioDevicePath);
        ini.setValue("audioDeviceIndex", channelSettings[channel].unpluggedAudioDeviceIndex);
        ini.setValue("audioEnabled", channelSettings[channel].unpluggedAudioEnabled);

    }

    ini.endGroup();
}
// 从INI文件加载热插拔数据
bool MainWindow::loadHotplugDataFromIni(int channel, const QString &iniPath) {
    if (channel < 0 || channel >= 4) return false;

    QSettings ini(iniPath, QSettings::IniFormat);
    QString group = QString("Hotplug_Channel_%1").arg(channel);
    ini.beginGroup(group);

    bool wasRunning = ini.value("wasRunning", false).toBool();
    if (wasRunning) {
        channelSettings[channel].wasRunningBeforeUnplug = true;
        channelSettings[channel].unpluggedUsbInterface = ini.value("usbInterface").toString();
        channelSettings[channel].unpluggedProductVid = ini.value("productVid").toString();
        channelSettings[channel].unpluggedDeviceName = ini.value("deviceName").toString();
        channelSettings[channel].unpluggedFormat = ini.value("format").toMap();
        channelSettings[channel].unpluggedResolution = ini.value("resolution").toMap();
        channelSettings[channel].unpluggedFormatIndex = ini.value("formatIndex", -1).toInt();
        channelSettings[channel].unpluggedResolutionIndex = ini.value("resolutionIndex", -1).toInt();
        channelSettings[channel].unpluggedFBLname = ini.value("FBLname").toString();
        isRecording[channel]=ini.value("isRecording").toBool();

        // 加载音频设备信息
        channelSettings[channel].unpluggedAudioDeviceName = ini.value("audioDeviceName").toString();
        channelSettings[channel].unpluggedAudioDevicePath = ini.value("audioDevicePath").toString();
        channelSettings[channel].unpluggedAudioDeviceIndex = ini.value("audioDeviceIndex", -1).toInt();
        channelSettings[channel].unpluggedAudioEnabled = ini.value("audioEnabled", false).toBool();

        printf("加载热插拔数据: %s\n", channelSettings[channel].unpluggedDeviceName.toStdString().c_str());
    }

    ini.endGroup();
    return wasRunning;
}

// 清理热插拔数据从INI文件
void MainWindow::clearHotplugDataFromIni(int channel, const QString &iniPath) {
    if (channel < 0 || channel >= 4) return;

    QSettings ini(iniPath, QSettings::IniFormat);
    QString group = QString("Hotplug_Channel_%1").arg(channel);

    // 删除整个热插拔组
    ini.remove(group);

    // 清理内存中的热插拔数据
    channelSettings[channel].wasRunningBeforeUnplug = false;
    channelSettings[channel].unpluggedUsbInterface = "";
    channelSettings[channel].unpluggedProductVid = "";
    channelSettings[channel].unpluggedDeviceName = "";
    channelSettings[channel].unpluggedFormat = QVariantMap();
    channelSettings[channel].unpluggedResolution = QVariantMap();
    channelSettings[channel].unpluggedFormatIndex = -1;
    channelSettings[channel].unpluggedResolutionIndex = -1;
    channelSettings[channel].unpluggedFBLname = "";

    // 清理音频设备热插拔数据
    channelSettings[channel].unpluggedAudioDeviceName = "";
    channelSettings[channel].unpluggedAudioDevicePath = "";
    channelSettings[channel].unpluggedAudioDeviceIndex = -1;
    channelSettings[channel].unpluggedAudioEnabled = false;

}

void MainWindow::isRecordAudio(bool reaudio){
    if(curchannel < 4){
        if(reaudio){
            curaudiostate[curchannel]=true;
        }
        else{
            curaudiostate[curchannel]=false;
        }
    }
    // 保存音频状态到通道设置
    if (curchannel >= 0 && curchannel < 4) {
        channelSettings[curchannel].audioEnabled = curaudiostate[curchannel];

        // // 同时保存当前音频设备信息
        // if (ui->comboBox_audiodev->currentIndex() >= 0) {
        //     channelSettings[curchannel].audioDeviceName = ui->comboBox_audiodev->currentText();
        //     channelSettings[curchannel].audioDevicePath = ui->comboBox_audiodev->currentData().toString();
        //     channelSettings[curchannel].audioDeviceIndex = ui->comboBox_audiodev->currentIndex();
        // }

        // 保存到INI文件
        saveChannelSettingsToIni(channelSettings, iniPath);
    }

    updateDeviceLabels(curchannel);
    updateCompositePageLabels(0,1,channelSettings[0].isOpen,channelSettings[1].isOpen);
    updateCompositePageLabels(2,3,channelSettings[2].isOpen,channelSettings[3].isOpen);
    updateGridPageLabels(0,1,2,3,channelSettings[0].isOpen,channelSettings[1].isOpen,channelSettings[2].isOpen,channelSettings[3].isOpen);
}

void MainWindow::aboutus(){
    keybod=1;
    stopSystemDoubleClickMonitor();  // 临时禁用
    ui->centralwidget->setEnabled(false);
    Aboutus aboutus(this);
    aboutus.exec();
    ui->centralwidget->setEnabled(true);
    startSystemDoubleClickMonitor(); // 重新启用
    keybod=0;
}

// 辅助函数：从分辨率字符串解析宽高
void MainWindow::parseResolution(const QString& resStr, int& width, int& height) {
    QRegularExpression re("(\\d+)x(\\d+)");
    QRegularExpressionMatch match = re.match(resStr);

    if (match.hasMatch()) {
        width = match.captured(1).toInt();
        height = match.captured(2).toInt();
    } else {
        width = 1920; // 默认值
        height = 1080;
    }
}

// 计算保持比例的矩形
QRect MainWindow::calculateAspectRatioRect(int srcWidth, int srcHeight, const QRect& targetArea) {
    double srcRatio = static_cast<double>(srcWidth) / srcHeight;
    double targetRatio = static_cast<double>(targetArea.width()) / targetArea.height();

    int w, h;
    if (srcRatio > targetRatio) {
        w = targetArea.width();
        h = static_cast<int>(w / srcRatio);
    } else {
        h = targetArea.height();
        w = static_cast<int>(h * srcRatio);
    }

    int x = targetArea.x() + (targetArea.width() - w) / 2;
    int y = targetArea.y() + (targetArea.height() - h) / 2;

    return QRect(x, y, w, h);
}
