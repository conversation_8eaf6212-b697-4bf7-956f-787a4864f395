#include <QString>

class SysComboBoxStyle
{
public:
    static QString getStyleSheet()
    {
        return QString(
            "QComboBox{"
            "color: rgb(0, 0, 0);"
            "background-color: rgb(255, 255, 255);"
            "font-size:12px;"
            "padding: 1px 15px 1px 10px;"
            "border:0px solid rgba(228, 228, 228, 1);"
            "border-radius:5px 5px 0px 0px;"
            "}"
            "QComboBox::drop-down{"
            "image: url(:/image/icons/combox_ico_drop.png);"
            "padding: 3px 6px 1px 3px;"
            "width:18px;"
            "}"
            "QComboBox QAbstractItemView{"
            "outline: 0px solid gray;"
            "border: 1px solid white;"
            "color: rgb(0, 0, 0);"
            "background-color: rgb(255, 255, 255);"
            "selection-background-color: rgb(90, 90, 90);"
            "}"
            "QAbstractItemView::item{"
            "height: 20px;"
            "}"
            "QComboBox QAbstractItemView::item:hover{"
            "color: #FFFFFF;"
            "background-color: rgb(90, 90, 90);"
            "}"
            "QComboBox:disabled{"
            "background-color: rgb(188, 188, 188);"
            "color: rgb(122, 122, 122);"
            "}"
            );
    }
};
