<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1265</width>
    <height>925</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(239, 41, 41,0);</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="styleSheet">
    <string notr="true">background-color: rgb(239, 41, 41,0);</string>
   </property>
   <layout class="QGridLayout" name="gridLayout_4">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <property name="spacing">
     <number>0</number>
    </property>
    <item row="0" column="0" colspan="2">
     <widget class="QWidget" name="widget" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgb(120, 120, 120);</string>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_9">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>10</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="label_mainname">
         <property name="minimumSize">
          <size>
           <width>200</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>20</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">color: rgb(255, 255, 255);</string>
         </property>
         <property name="text">
          <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;justify&quot;&gt;&lt;span style=&quot; font-weight:700; font-style:italic;&quot;&gt;Video Capture&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>100</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>134</width>
           <height>50</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QPushButton{
	background-color: rgb(90, 90, 90);
    border-radius:10px;  
	color: rgb(255, 255, 255);
 padding:10px;

}
QPushButton::pressed,QPushButton::checked{
 padding: 5px;
	color: rgb(0, 0, 0);
	background-color: rgb(255, 255, 255);
background:transparent;
}
QPushButton:hover{
	color: rgb(0, 0, 0);
	background-color: rgb(255, 255, 255);
}</string>
         </property>
         <property name="text">
          <string>关于我们</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>500</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <item>
          <widget class="QPushButton" name="recordingSet">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>150</width>
             <height>50</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>50</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
           <property name="toolTip">
            <string>录像设置</string>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{  
border-radius:5px;
background-position: top;
      background-repeat: repeat-no-repeat;        
	image: url(:/image/录像设置(选中).png);

	color: rgb(255, 255, 255);
text-align : bottom;
 padding:0px;

}
QPushButton::pressed,QPushButton::checked{
background-position: top;
	color: rgb(0, 0, 0);
	image: url(:/image/录像设置.png);
}

QPushButton::disabled{
background-position: top;
	color: rgb(0, 0, 0);
	image: url(:/image/录像设置.png);
}</string>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="fileManage">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>150</width>
             <height>50</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>50</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
           <property name="toolTip">
            <string>文件管理</string>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{  
border-radius:5px;
background-position: top;
      background-repeat: repeat-no-repeat;        
	image: url(:/image/文件管理.png);
	color: rgb(255, 255, 255);
text-align : bottom;
 padding: 0px;
}
QPushButton::pressed,QPushButton::checked{
background-position: top;
	color: rgb(0, 0, 0);
	image: url(:/image/文件管理(选中).png);
}

QPushButton::disabled{
background-position: top;
	color: rgb(0, 0, 0);
	image: url(:/image/文件管理(选中).png);
}</string>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="systemSet">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>150</width>
             <height>50</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>50</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
           <property name="toolTip">
            <string>系统设置</string>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{  
border-radius:5px;
background-position: top;
      background-repeat: repeat-no-repeat;        
	image: url(:/image/系统设置.png);
	color: rgb(255, 255, 255);
text-align : bottom;
 padding: 0px;
}
QPushButton::pressed,QPushButton::checked{
background-position: top;
	color: rgb(0, 0, 0);
image: url(:/image/系统设置(选中).png);
}

QPushButton::disabled{
background-position: top;
	color: rgb(0, 0, 0);
	image: url(:/image/系统设置(选中).png);
}</string>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_12">
         <property name="spacing">
          <number>0</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_6">
           <property name="spacing">
            <number>0</number>
           </property>
           <item>
            <spacer name="horizontalSpacer_4">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Fixed</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>150</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <spacer name="horizontalSpacer_7">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_fullscreen">
             <property name="minimumSize">
              <size>
               <width>25</width>
               <height>24</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>25</width>
               <height>24</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton{
	image: url(:/image/全屏.png);
border-style:none;
padding:5px;

background:transparent;
}
QPushButton:hover{
	background-color: rgb(0, 0, 0);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_min">
             <property name="minimumSize">
              <size>
               <width>25</width>
               <height>24</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>25</width>
               <height>24</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton{
	image: url(:/image/最小化.png);
border-style:none;
padding:5px;

background:transparent;
}
QPushButton:hover{
	background-color: rgb(0, 0, 0);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_max">
             <property name="minimumSize">
              <size>
               <width>25</width>
               <height>24</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>25</width>
               <height>24</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton{

border-style:none;
padding:5px;

background:transparent;
}
QPushButton:hover{
	background-color: rgb(0, 0, 0);
}
QPushButton::pressed,QPushButton::checked{


background:transparent;
}
QPushButton:pressed:hover, QPushButton:checked:hover {
    background-color: rgb(0, 0, 0);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="closeButton">
             <property name="minimumSize">
              <size>
               <width>25</width>
               <height>24</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>25</width>
               <height>24</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton{
	image: url(:/image/关闭.png);
border-style:none;
padding:5px;

background:transparent;
}
QPushButton:hover{
	background-color: rgb(239, 41, 41);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_7">
           <item>
            <widget class="QLabel" name="label_cpu">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>120</width>
               <height>20</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>20</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(255, 255, 255);</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_gpu">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>120</width>
               <height>20</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>20</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(255, 255, 255);</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_8">
           <item>
            <widget class="QLabel" name="label_memory">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>120</width>
               <height>20</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>20</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(255, 255, 255);</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_time">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>120</width>
               <height>20</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>20</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(255, 255, 255);</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
    <item row="1" column="0">
     <widget class="QWidget" name="widget_2" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>265</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>265</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgb(90, 90, 90);</string>
      </property>
      <layout class="QGridLayout" name="gridLayout_3">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item row="0" column="0">
        <layout class="QVBoxLayout" name="verticalLayout_11">
         <property name="spacing">
          <number>8</number>
         </property>
         <property name="sizeConstraint">
          <enum>QLayout::SetFixedSize</enum>
         </property>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_2">
           <property name="spacing">
            <number>15</number>
           </property>
           <property name="sizeConstraint">
            <enum>QLayout::SetFixedSize</enum>
           </property>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="sizeConstraint">
              <enum>QLayout::SetFixedSize</enum>
             </property>
             <item>
              <widget class="QPushButton" name="videoSet">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>65</width>
                 <height>40</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>65</width>
                 <height>40</height>
                </size>
               </property>
               <property name="toolTip">
                <string>视频设置</string>
               </property>
               <property name="autoFillBackground">
                <bool>false</bool>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	image: url(:/image/icons/视频设置(default).png);
border-style:none;
border: 1px solid rgb(0, 170, 255);
 padding: 5px;
background:transparent;
}
QPushButton::pressed,QPushButton::checked{
	image: url(:/image/icons/视频设置.png);
 padding: 5px;
background:transparent;
}
QPushButton:hover{
	image: url(:/image/icons/视频设置.png);
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="autoExclusive">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pictureSet">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>65</width>
                 <height>40</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>65</width>
                 <height>40</height>
                </size>
               </property>
               <property name="toolTip">
                <string>图像设置</string>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	image: url(:/image/icons/图像设置(default).png);
border-style:none;
border: 1px solid rgb(0, 170, 255);
 padding: 5px;
background:transparent;
}
QPushButton::pressed,QPushButton::checked{
	image: url(:/image/icons/图像设置.png);
 padding: 5px;
background:transparent;
}
QPushButton:hover{
	image: url(:/image/icons/图像设置.png);
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="autoExclusive">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="cameraControl">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>65</width>
                 <height>40</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>65</width>
                 <height>40</height>
                </size>
               </property>
               <property name="toolTip">
                <string>相机控制</string>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	image: url(:/image/icons/相机控制(default).png);
border-style:none;
border: 1px solid rgb(0, 170, 255);
 padding: 5px;
background:transparent;
}
QPushButton::pressed,QPushButton::checked{
	image: url(:/image/icons/相机控制.png);
 padding: 5px;
background:transparent;
}
QPushButton:hover{
	image: url(:/image/icons/相机控制.png);
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="autoExclusive">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="textSet">
               <property name="minimumSize">
                <size>
                 <width>65</width>
                 <height>40</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>65</width>
                 <height>40</height>
                </size>
               </property>
               <property name="toolTip">
                <string>文字设置</string>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	image: url(:/image/icons/文字设置(default).png);
border-style:none;
border: 1px solid rgb(0, 170, 255);
 padding: 5px;
background:transparent;
}
QPushButton::pressed,QPushButton::checked{
	image: url(:/image/icons/文字设置.png);
 padding: 5px;
background:transparent;
}
QPushButton:hover{
	image: url(:/image/icons/文字设置.png);
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="autoExclusive">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QVBoxLayout" name="verticalLayout">
             <item>
              <widget class="QLabel" name="label_xinhaoy">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>22</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>22</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>信号源</string>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_2">
               <property name="spacing">
                <number>2</number>
               </property>
               <property name="sizeConstraint">
                <enum>QLayout::SetFixedSize</enum>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QPushButton" name="p1">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}</string>
                 </property>
                 <property name="text">
                  <string>1</string>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                 <property name="autoExclusive">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="p2">
                 <property name="minimumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}</string>
                 </property>
                 <property name="text">
                  <string>2</string>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                 <property name="autoExclusive">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="p3">
                 <property name="minimumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}</string>
                 </property>
                 <property name="text">
                  <string>3</string>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                 <property name="autoExclusive">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="p4">
                 <property name="minimumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>60</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}</string>
                 </property>
                 <property name="text">
                  <string>4</string>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                 <property name="autoExclusive">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QStackedWidget" name="stackedWidget">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="currentIndex">
            <number>0</number>
           </property>
           <widget class="QWidget" name="page_videoset">
            <layout class="QVBoxLayout" name="verticalLayout_9">
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_8">
               <property name="spacing">
                <number>80</number>
               </property>
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_3">
                 <property name="spacing">
                  <number>15</number>
                 </property>
                 <item>
                  <widget class="QLabel" name="label_cameradev">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>22</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16777215</width>
                     <height>22</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);</string>
                   </property>
                   <property name="text">
                    <string>相机设备</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QComboBox" name="comboBox_cameradev">
                   <property name="minimumSize">
                    <size>
                     <width>230</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>230</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QComboBox {
    font: 75 9pt 'Arial';
    color: rgb(0, 0, 0);
    background-color: rgb(255, 255, 255);
    padding: 1px 15px 1px 10px;
    border: none;
    border-radius: 5px 5px 0px 0px;
}
QComboBox::drop-down {
	image: url(:/image/icons/combox_ico_drop.png);
    padding: 3px 6px 1px 3px;
    width: 18px;
}
QComboBox QAbstractItemView {
    outline: none;
    border: 1px solid white;
    color: rgb(0, 0, 0);
    background-color: rgb(255, 255, 255);
    selection-background-color: rgb(90, 90, 90);
}
QAbstractItemView::item {
    height: 30px;
}
QComboBox QAbstractItemView::item:hover {
    color: #FFFFFF;
    background-color: rgb(90, 90, 90);
}
QComboBox:disabled {
    background-color: rgb(188, 188, 188);
    color: rgb(122, 122, 122);
}</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_4">
                 <property name="spacing">
                  <number>15</number>
                 </property>
                 <item>
                  <widget class="QLabel" name="label_videoformat">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>22</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16777215</width>
                     <height>22</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);</string>
                   </property>
                   <property name="text">
                    <string>视频格式</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QComboBox" name="comboBox_videoformat">
                   <property name="minimumSize">
                    <size>
                     <width>230</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>230</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QComboBox {
    font: 75 9pt 'Arial';
    color: rgb(0, 0, 0);
    background-color: rgb(255, 255, 255);
    padding: 1px 15px 1px 10px;
    border: none;
    border-radius: 5px 5px 0px 0px;
}
QComboBox::drop-down {
	image: url(:/image/icons/combox_ico_drop.png);
    padding: 3px 6px 1px 3px;
    width: 18px;
}
QComboBox QAbstractItemView {
    outline: none;
    border: 1px solid white;
    color: rgb(0, 0, 0);
    background-color: rgb(255, 255, 255);
    selection-background-color: rgb(90, 90, 90);
}
QAbstractItemView::item {
    height: 30px;
}
QComboBox QAbstractItemView::item:hover {
    color: #FFFFFF;
    background-color: rgb(90, 90, 90);
}
QComboBox:disabled {
    background-color: rgb(188, 188, 188);
    color: rgb(122, 122, 122);
}</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_5">
                 <property name="spacing">
                  <number>15</number>
                 </property>
                 <item>
                  <widget class="QLabel" name="label_fbl">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>22</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16777215</width>
                     <height>22</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);</string>
                   </property>
                   <property name="text">
                    <string>分辨率</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QComboBox" name="comboBox_fbl">
                   <property name="minimumSize">
                    <size>
                     <width>230</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>230</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QComboBox {
    font: 75 9pt 'Arial';
    color: rgb(0, 0, 0);
    background-color: rgb(255, 255, 255);
    padding: 1px 15px 1px 10px;
    border: none;
    border-radius: 5px 5px 0px 0px;
}
QComboBox::drop-down {
	image: url(:/image/icons/combox_ico_drop.png);
    padding: 3px 6px 1px 3px;
    width: 18px;
}
QComboBox QAbstractItemView {
    outline: none;
    border: 1px solid white;
    color: rgb(0, 0, 0);
    background-color: rgb(255, 255, 255);
    selection-background-color: rgb(90, 90, 90);
}
QAbstractItemView::item {
    height: 30px;
}
QComboBox QAbstractItemView::item:hover {
    color: #FFFFFF;
    background-color: rgb(90, 90, 90);
}
QComboBox:disabled {
    background-color: rgb(188, 188, 188);
    color: rgb(122, 122, 122);
}</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_7">
                 <property name="spacing">
                  <number>25</number>
                 </property>
                 <item>
                  <layout class="QVBoxLayout" name="verticalLayout_6">
                   <property name="spacing">
                    <number>15</number>
                   </property>
                   <item>
                    <widget class="QLabel" name="label_audiodev">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>22</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>22</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">color: rgb(255, 255, 255);</string>
                     </property>
                     <property name="text">
                      <string>音频设备</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QComboBox" name="comboBox_audiodev">
                     <property name="minimumSize">
                      <size>
                       <width>230</width>
                       <height>25</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>230</width>
                       <height>25</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QComboBox {
    font: 75 9pt 'Arial';
    color: rgb(0, 0, 0);
    background-color: rgb(255, 255, 255);
    padding: 1px 15px 1px 10px;
    border: none;
    border-radius: 5px 5px 0px 0px;
}
QComboBox::drop-down {
	image: url(:/image/icons/combox_ico_drop.png);
    padding: 3px 6px 1px 3px;
    width: 18px;
}
QComboBox QAbstractItemView {
    outline: none;
    border: 1px solid white;
    color: rgb(0, 0, 0);
    background-color: rgb(255, 255, 255);
    selection-background-color: rgb(90, 90, 90);
}
QAbstractItemView::item {
    height: 30px;
}
QComboBox QAbstractItemView::item:hover {
    color: #FFFFFF;
    background-color: rgb(90, 90, 90);
}
QComboBox:disabled {
    background-color: rgb(188, 188, 188);
    color: rgb(122, 122, 122);
}</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <widget class="QCheckBox" name="checkBox_audio">
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);</string>
                   </property>
                   <property name="text">
                    <string>开启录音</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
           <widget class="QWidget" name="page_pictureset">
            <layout class="QGridLayout" name="gridLayout_6">
             <item row="0" column="0">
              <widget class="QScrollArea" name="scrollArea">
               <property name="widgetResizable">
                <bool>true</bool>
               </property>
               <widget class="QWidget" name="scrollAreaWidgetContents">
                <property name="geometry">
                 <rect>
                  <x>0</x>
                  <y>0</y>
                  <width>229</width>
                  <height>703</height>
                 </rect>
                </property>
                <layout class="QGridLayout" name="gridLayout_5">
                 <item row="0" column="0">
                  <layout class="QVBoxLayout" name="verticalLayout_22">
                   <property name="spacing">
                    <number>18</number>
                   </property>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_13">
                     <item>
                      <widget class="QLabel" name="label_brightness">
                       <property name="font">
                        <font>
                         <pointsize>10</pointsize>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">color: rgb(255, 255, 255);</string>
                       </property>
                       <property name="text">
                        <string>亮度</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <layout class="QHBoxLayout" name="horizontalLayout_10">
                       <item>
                        <widget class="QSlider" name="horizontalSlider_brightness">
                         <property name="minimumSize">
                          <size>
                           <width>149</width>
                           <height>0</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>149</width>
                           <height>16777215</height>
                          </size>
                         </property>
                         <property name="styleSheet">
                          <string notr="true"/>
                         </property>
                         <property name="orientation">
                          <enum>Qt::Horizontal</enum>
                         </property>
                        </widget>
                       </item>
                       <item>
                        <widget class="QSpinBox" name="spinBox_brightness">
                         <property name="sizePolicy">
                          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                           <horstretch>50</horstretch>
                           <verstretch>25</verstretch>
                          </sizepolicy>
                         </property>
                         <property name="minimumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="styleSheet">
                          <string notr="true">background-color: rgb(255, 255, 255);</string>
                         </property>
                        </widget>
                       </item>
                      </layout>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_14">
                     <item>
                      <widget class="QLabel" name="label_contrast">
                       <property name="font">
                        <font>
                         <pointsize>10</pointsize>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">color: rgb(255, 255, 255);</string>
                       </property>
                       <property name="text">
                        <string>对比度</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <layout class="QHBoxLayout" name="horizontalLayout_11">
                       <item>
                        <widget class="QSlider" name="horizontalSlider_contrast">
                         <property name="minimumSize">
                          <size>
                           <width>149</width>
                           <height>0</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>149</width>
                           <height>16777215</height>
                          </size>
                         </property>
                         <property name="orientation">
                          <enum>Qt::Horizontal</enum>
                         </property>
                        </widget>
                       </item>
                       <item>
                        <widget class="QSpinBox" name="spinBox_contrast">
                         <property name="sizePolicy">
                          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                           <horstretch>50</horstretch>
                           <verstretch>25</verstretch>
                          </sizepolicy>
                         </property>
                         <property name="minimumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="styleSheet">
                          <string notr="true">background-color: rgb(255, 255, 255);</string>
                         </property>
                        </widget>
                       </item>
                      </layout>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_15">
                     <item>
                      <widget class="QLabel" name="label_saturation">
                       <property name="font">
                        <font>
                         <pointsize>10</pointsize>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">color: rgb(255, 255, 255);</string>
                       </property>
                       <property name="text">
                        <string>饱和度</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <layout class="QHBoxLayout" name="horizontalLayout_12">
                       <item>
                        <widget class="QSlider" name="horizontalSlider_saturation">
                         <property name="minimumSize">
                          <size>
                           <width>149</width>
                           <height>0</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>149</width>
                           <height>16777215</height>
                          </size>
                         </property>
                         <property name="orientation">
                          <enum>Qt::Horizontal</enum>
                         </property>
                        </widget>
                       </item>
                       <item>
                        <widget class="QSpinBox" name="spinBox_saturation">
                         <property name="sizePolicy">
                          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                           <horstretch>0</horstretch>
                           <verstretch>0</verstretch>
                          </sizepolicy>
                         </property>
                         <property name="minimumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="styleSheet">
                          <string notr="true">background-color: rgb(255, 255, 255);</string>
                         </property>
                        </widget>
                       </item>
                      </layout>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_16">
                     <item>
                      <widget class="QLabel" name="label_hue">
                       <property name="font">
                        <font>
                         <pointsize>10</pointsize>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">color: rgb(255, 255, 255);</string>
                       </property>
                       <property name="text">
                        <string>色调</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <layout class="QHBoxLayout" name="horizontalLayout_13">
                       <item>
                        <widget class="QSlider" name="horizontalSlider_hue">
                         <property name="minimumSize">
                          <size>
                           <width>149</width>
                           <height>0</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>149</width>
                           <height>16777215</height>
                          </size>
                         </property>
                         <property name="orientation">
                          <enum>Qt::Horizontal</enum>
                         </property>
                        </widget>
                       </item>
                       <item>
                        <widget class="QSpinBox" name="spinBox_hue">
                         <property name="sizePolicy">
                          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                           <horstretch>0</horstretch>
                           <verstretch>0</verstretch>
                          </sizepolicy>
                         </property>
                         <property name="minimumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="styleSheet">
                          <string notr="true">background-color: rgb(255, 255, 255);</string>
                         </property>
                        </widget>
                       </item>
                      </layout>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_17">
                     <item>
                      <widget class="QLabel" name="label_sharpness">
                       <property name="font">
                        <font>
                         <pointsize>10</pointsize>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">color: rgb(255, 255, 255);</string>
                       </property>
                       <property name="text">
                        <string>清晰度</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <layout class="QHBoxLayout" name="horizontalLayout_14">
                       <item>
                        <widget class="QSlider" name="horizontalSlider_sharpness">
                         <property name="minimumSize">
                          <size>
                           <width>149</width>
                           <height>0</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>149</width>
                           <height>16777215</height>
                          </size>
                         </property>
                         <property name="orientation">
                          <enum>Qt::Horizontal</enum>
                         </property>
                        </widget>
                       </item>
                       <item>
                        <widget class="QSpinBox" name="spinBox_sharpness">
                         <property name="sizePolicy">
                          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                           <horstretch>0</horstretch>
                           <verstretch>0</verstretch>
                          </sizepolicy>
                         </property>
                         <property name="minimumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="styleSheet">
                          <string notr="true">background-color: rgb(255, 255, 255);</string>
                         </property>
                        </widget>
                       </item>
                      </layout>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_18">
                     <item>
                      <widget class="QLabel" name="label_gamma">
                       <property name="font">
                        <font>
                         <pointsize>10</pointsize>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">color: rgb(255, 255, 255);</string>
                       </property>
                       <property name="text">
                        <string>伽玛</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <layout class="QHBoxLayout" name="horizontalLayout_15">
                       <item>
                        <widget class="QSlider" name="horizontalSlider_gamma">
                         <property name="minimumSize">
                          <size>
                           <width>149</width>
                           <height>0</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>149</width>
                           <height>16777215</height>
                          </size>
                         </property>
                         <property name="orientation">
                          <enum>Qt::Horizontal</enum>
                         </property>
                        </widget>
                       </item>
                       <item>
                        <widget class="QSpinBox" name="spinBox_gamma">
                         <property name="sizePolicy">
                          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                           <horstretch>0</horstretch>
                           <verstretch>0</verstretch>
                          </sizepolicy>
                         </property>
                         <property name="minimumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="styleSheet">
                          <string notr="true">background-color: rgb(255, 255, 255);</string>
                         </property>
                        </widget>
                       </item>
                      </layout>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_19">
                     <item>
                      <widget class="QLabel" name="label_backlightcompensation">
                       <property name="font">
                        <font>
                         <pointsize>10</pointsize>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">color: rgb(255, 255, 255);</string>
                       </property>
                       <property name="text">
                        <string>逆光对比</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <layout class="QHBoxLayout" name="horizontalLayout_16">
                       <item>
                        <widget class="QSlider" name="horizontalSlider_backlightcompensation">
                         <property name="minimumSize">
                          <size>
                           <width>149</width>
                           <height>0</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>149</width>
                           <height>16777215</height>
                          </size>
                         </property>
                         <property name="orientation">
                          <enum>Qt::Horizontal</enum>
                         </property>
                        </widget>
                       </item>
                       <item>
                        <widget class="QSpinBox" name="spinBox_backlightcompensation">
                         <property name="sizePolicy">
                          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                           <horstretch>0</horstretch>
                           <verstretch>0</verstretch>
                          </sizepolicy>
                         </property>
                         <property name="minimumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="styleSheet">
                          <string notr="true">background-color: rgb(255, 255, 255);</string>
                         </property>
                        </widget>
                       </item>
                      </layout>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_20">
                     <item>
                      <widget class="QLabel" name="label_gain">
                       <property name="font">
                        <font>
                         <pointsize>10</pointsize>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">color: rgb(255, 255, 255);</string>
                       </property>
                       <property name="text">
                        <string>增益</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <layout class="QHBoxLayout" name="horizontalLayout_17">
                       <item>
                        <widget class="QSlider" name="horizontalSlider_gain">
                         <property name="minimumSize">
                          <size>
                           <width>149</width>
                           <height>0</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>149</width>
                           <height>16777215</height>
                          </size>
                         </property>
                         <property name="orientation">
                          <enum>Qt::Horizontal</enum>
                         </property>
                        </widget>
                       </item>
                       <item>
                        <widget class="QSpinBox" name="spinBox_gain">
                         <property name="sizePolicy">
                          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                           <horstretch>0</horstretch>
                           <verstretch>0</verstretch>
                          </sizepolicy>
                         </property>
                         <property name="minimumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="styleSheet">
                          <string notr="true">background-color: rgb(255, 255, 255);</string>
                         </property>
                        </widget>
                       </item>
                      </layout>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_21">
                     <item>
                      <layout class="QHBoxLayout" name="horizontalLayout_20">
                       <item>
                        <widget class="QLabel" name="label_whitebalance">
                         <property name="font">
                          <font>
                           <pointsize>10</pointsize>
                          </font>
                         </property>
                         <property name="styleSheet">
                          <string notr="true">color: rgb(255, 255, 255);</string>
                         </property>
                         <property name="text">
                          <string>白平衡</string>
                         </property>
                        </widget>
                       </item>
                       <item>
                        <widget class="QCheckBox" name="checkBox_whitebalance">
                         <property name="maximumSize">
                          <size>
                           <width>60</width>
                           <height>16777215</height>
                          </size>
                         </property>
                         <property name="font">
                          <font>
                           <pointsize>10</pointsize>
                          </font>
                         </property>
                         <property name="styleSheet">
                          <string notr="true">QCheckBox {
    spacing: 5px;                    /* 文字与打勾框之间的间距 */
	color: rgb(255, 255, 255);
}
</string>
                         </property>
                         <property name="text">
                          <string>自动</string>
                         </property>
                        </widget>
                       </item>
                      </layout>
                     </item>
                     <item>
                      <layout class="QHBoxLayout" name="horizontalLayout_18">
                       <item>
                        <widget class="QSlider" name="horizontalSlider_whitebalance">
                         <property name="minimumSize">
                          <size>
                           <width>149</width>
                           <height>0</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>149</width>
                           <height>16777215</height>
                          </size>
                         </property>
                         <property name="orientation">
                          <enum>Qt::Horizontal</enum>
                         </property>
                        </widget>
                       </item>
                       <item>
                        <widget class="QSpinBox" name="spinBox_whitebalance">
                         <property name="sizePolicy">
                          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                           <horstretch>0</horstretch>
                           <verstretch>0</verstretch>
                          </sizepolicy>
                         </property>
                         <property name="minimumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="maximumSize">
                          <size>
                           <width>50</width>
                           <height>25</height>
                          </size>
                         </property>
                         <property name="styleSheet">
                          <string notr="true">background-color: rgb(255, 255, 255);</string>
                         </property>
                        </widget>
                       </item>
                      </layout>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QHBoxLayout" name="horizontalLayout_19">
                     <item>
                      <widget class="QPushButton" name="pushButton_default1">
                       <property name="minimumSize">
                        <size>
                         <width>70</width>
                         <height>30</height>
                        </size>
                       </property>
                       <property name="font">
                        <font>
                         <pointsize>10</pointsize>
                        </font>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">QPushButton{  
	background-color: rgb(255, 255, 255);
border-radius:5px;      
	color: rgb(0, 0, 0);
 padding: 2px;
}
QPushButton::pressed,QPushButton::checked{
	background-color: rgb(0, 0, 0);
	color: rgb(255, 255, 255);
}
</string>
                       </property>
                       <property name="text">
                        <string>默认值</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <spacer name="horizontalSpacer_5">
                       <property name="orientation">
                        <enum>Qt::Horizontal</enum>
                       </property>
                       <property name="sizeHint" stdset="0">
                        <size>
                         <width>40</width>
                         <height>20</height>
                        </size>
                       </property>
                      </spacer>
                     </item>
                    </layout>
                   </item>
                  </layout>
                 </item>
                </layout>
               </widget>
              </widget>
             </item>
            </layout>
           </widget>
           <widget class="QWidget" name="page_cameracontrol">
            <layout class="QVBoxLayout" name="verticalLayout_28">
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_23">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_21">
                 <item>
                  <widget class="QLabel" name="label_exposure">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>10</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);</string>
                   </property>
                   <property name="text">
                    <string>曝光</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QCheckBox" name="checkBox_exposure">
                   <property name="maximumSize">
                    <size>
                     <width>60</width>
                     <height>20</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>10</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QCheckBox {
    spacing: 5px;                    /* 文字与打勾框之间的间距 */
	color: rgb(255, 255, 255);
}

</string>
                   </property>
                   <property name="text">
                    <string>自动</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_22">
                 <item>
                  <widget class="QSlider" name="horizontalSlider_exposure">
                   <property name="minimumSize">
                    <size>
                     <width>149</width>
                     <height>0</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>149</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QSpinBox" name="spinBox_exposure">
                   <property name="minimumSize">
                    <size>
                     <width>50</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>50</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">background-color: rgb(255, 255, 255);</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_27">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_23">
                 <item>
                  <widget class="QLabel" name="label_focus">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>10</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">color: rgb(255, 255, 255);</string>
                   </property>
                   <property name="text">
                    <string>焦点</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QCheckBox" name="checkBox_focus">
                   <property name="maximumSize">
                    <size>
                     <width>60</width>
                     <height>20</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>10</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">QCheckBox {
    spacing: 5px;                    /* 文字与打勾框之间的间距 */
	color: rgb(255, 255, 255);
}




</string>
                   </property>
                   <property name="text">
                    <string>自动</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_24">
                 <item>
                  <widget class="QSlider" name="horizontalSlider_focus">
                   <property name="minimumSize">
                    <size>
                     <width>149</width>
                     <height>0</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>149</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QSpinBox" name="spinBox_focus">
                   <property name="minimumSize">
                    <size>
                     <width>50</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>50</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">background-color: rgb(255, 255, 255);</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_24">
               <item>
                <widget class="QLabel" name="label_zoom">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>165</width>
                   <height>26</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>165</width>
                   <height>26</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(255, 255, 255);</string>
                 </property>
                 <property name="text">
                  <string>缩放</string>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_25">
                 <item>
                  <widget class="QSlider" name="horizontalSlider_zoom">
                   <property name="minimumSize">
                    <size>
                     <width>149</width>
                     <height>0</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>149</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QSpinBox" name="spinBox_zoom">
                   <property name="minimumSize">
                    <size>
                     <width>50</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>50</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">background-color: rgb(255, 255, 255);</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_25">
               <item>
                <widget class="QLabel" name="label_pan">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>165</width>
                   <height>26</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>165</width>
                   <height>26</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(255, 255, 255);</string>
                 </property>
                 <property name="text">
                  <string>全景</string>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_26">
                 <item>
                  <widget class="QSlider" name="horizontalSlider_pan">
                   <property name="minimumSize">
                    <size>
                     <width>149</width>
                     <height>0</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>149</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QSpinBox" name="spinBox_pan">
                   <property name="minimumSize">
                    <size>
                     <width>50</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>50</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">background-color: rgb(255, 255, 255);</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_26">
               <item>
                <widget class="QLabel" name="label_tilt">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>165</width>
                   <height>26</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>165</width>
                   <height>26</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(255, 255, 255);</string>
                 </property>
                 <property name="text">
                  <string>倾斜</string>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_27">
                 <item>
                  <widget class="QSlider" name="horizontalSlider_tilt">
                   <property name="minimumSize">
                    <size>
                     <width>149</width>
                     <height>0</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>149</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QSpinBox" name="spinBox_tilt">
                   <property name="minimumSize">
                    <size>
                     <width>50</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>50</width>
                     <height>25</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">background-color: rgb(255, 255, 255);</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_28">
               <item>
                <widget class="QPushButton" name="pushButton_default2">
                 <property name="minimumSize">
                  <size>
                   <width>70</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>10</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">QPushButton{  
	background-color: rgb(255, 255, 255);
border-radius:5px;      
	color: rgb(0, 0, 0);
 padding: 2px;
}
QPushButton::pressed,QPushButton::checked{
	background-color: rgb(0, 0, 0);
	color: rgb(255, 255, 255);
}
</string>
                 </property>
                 <property name="text">
                  <string>默认值</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_6">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
           <widget class="QWidget" name="page">
            <widget class="QWidget" name="layoutWidget">
             <property name="geometry">
              <rect>
               <x>70</x>
               <y>20</y>
               <width>157</width>
               <height>27</height>
              </rect>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_31">
              <item>
               <widget class="QLabel" name="label_5">
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);</string>
                </property>
                <property name="text">
                 <string>时间叠加</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="timewmark">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="styleSheet">
                 <string notr="true">QPushButton{

	image: url(:/image/icons/开关-关.png);



border-style:none;

}

QPushButton::pressed,QPushButton::checked{

	image: url(:/image/icons/开关-开.png);

}</string>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="checkable">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
            <widget class="QWidget" name="layoutWidget">
             <property name="geometry">
              <rect>
               <x>60</x>
               <y>60</y>
               <width>157</width>
               <height>27</height>
              </rect>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_32">
              <item>
               <widget class="QLabel" name="label_6">
                <property name="text">
                 <string>文字叠加</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="pushButton_6">
                <property name="text">
                 <string>PushButton</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
            <widget class="QWidget" name="layoutWidget">
             <property name="geometry">
              <rect>
               <x>70</x>
               <y>110</y>
               <width>144</width>
               <height>55</height>
              </rect>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_29">
              <item>
               <widget class="QLabel" name="label_7">
                <property name="text">
                 <string>文字内容</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit"/>
              </item>
             </layout>
            </widget>
            <widget class="QLabel" name="label_8">
             <property name="geometry">
              <rect>
               <x>80</x>
               <y>180</y>
               <width>67</width>
               <height>17</height>
              </rect>
             </property>
             <property name="text">
              <string>文字位置</string>
             </property>
            </widget>
            <widget class="QPushButton" name="pushButton_7">
             <property name="geometry">
              <rect>
               <x>70</x>
               <y>220</y>
               <width>89</width>
               <height>25</height>
              </rect>
             </property>
             <property name="text">
              <string>确认叠加</string>
             </property>
            </widget>
           </widget>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_10">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_3">
             <item>
              <widget class="QPushButton" name="p_1">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>40</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>40</width>
                 <height>30</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}</string>
               </property>
               <property name="text">
                <string>1</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="autoExclusive">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="p_2">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>40</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>40</width>
                 <height>30</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}</string>
               </property>
               <property name="text">
                <string>2</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="autoExclusive">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="p_3">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>40</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>40</width>
                 <height>30</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}</string>
               </property>
               <property name="text">
                <string>3</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="autoExclusive">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="p_4">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>40</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>40</width>
                 <height>30</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}</string>
               </property>
               <property name="text">
                <string>4</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="autoExclusive">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_4">
             <item>
              <widget class="QPushButton" name="p1_2">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>40</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>40</width>
                 <height>30</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}</string>
               </property>
               <property name="text">
                <string>1|2</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="autoExclusive">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="p3_4">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>40</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>40</width>
                 <height>30</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}</string>
               </property>
               <property name="text">
                <string>3|4</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="autoExclusive">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="p1234">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>35</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>35</width>
                 <height>35</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	image: url(:/image/icons/4路.png);
border-style:none;
border: 2px solid rgb(0, 170, 255);
padding:-5px;
background:transparent;

}
QPushButton::pressed,QPushButton::checked{
	image: url(:/image/icons/4路(default).png);
 padding: -5px;
background:transparent;
}
QPushButton:hover{
	image: url(:/image/icons/4路(default).png);
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
               <property name="autoExclusive">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="verticalSpacer_2">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>50</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QWidget" name="video" native="true">
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <layout class="QGridLayout" name="gridLayout">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item row="0" column="0">
        <widget class="QStackedWidget" name="stackedWidget_2">
         <property name="currentIndex">
          <number>0</number>
         </property>
         <widget class="QWidget" name="page_2">
          <layout class="QGridLayout" name="gridLayout_2">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <widget class="QWidget" name="v1" native="true">
             <property name="styleSheet">
              <string notr="true"/>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_3">
          <layout class="QGridLayout" name="gridLayout_7">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <widget class="QWidget" name="v2" native="true"/>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_4">
          <layout class="QGridLayout" name="gridLayout_8">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <widget class="QWidget" name="v3" native="true"/>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_5">
          <layout class="QGridLayout" name="gridLayout_9">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <widget class="QWidget" name="v4" native="true"/>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_6">
          <layout class="QGridLayout" name="gridLayout_10">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="1">
            <widget class="QWidget" name="v5_2" native="true"/>
           </item>
           <item row="0" column="0">
            <widget class="QWidget" name="v5_1" native="true"/>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_7">
          <layout class="QGridLayout" name="gridLayout_11">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <widget class="QWidget" name="v6_1" native="true"/>
           </item>
           <item row="0" column="1">
            <widget class="QWidget" name="v6_2" native="true"/>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_8">
          <layout class="QGridLayout" name="gridLayout_12">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <widget class="QWidget" name="v7_1" native="true"/>
           </item>
           <item row="0" column="1">
            <widget class="QWidget" name="v7_2" native="true"/>
           </item>
           <item row="1" column="0">
            <widget class="QWidget" name="v7_3" native="true"/>
           </item>
           <item row="1" column="1">
            <widget class="QWidget" name="v7_4" native="true"/>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>pushButton_fullscreen</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>toggleVideoFullscreen()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>1171</x>
     <y>14</y>
    </hint>
    <hint type="destinationlabel">
     <x>1166</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>comboBox_cameradev</sender>
   <signal>activated(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_video(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>77</x>
     <y>262</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>263</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>comboBox_videoformat</sender>
   <signal>activated(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_format(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>77</x>
     <y>418</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>416</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>comboBox_fbl</sender>
   <signal>activated(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_resolution(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>55</x>
     <y>562</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>562</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>videoSet</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>VideoSetPage()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>28</x>
     <y>96</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>92</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pictureSet</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>PictureSetPage()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>99</x>
     <y>103</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>119</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>cameraControl</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>CameraControlPage()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>151</x>
     <y>83</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>67</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>textSet</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>TextSetPage()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>225</x>
     <y>83</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>61</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_brightness</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_brightness()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>97</x>
     <y>241</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>257</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_brightness</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_brightness()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>110</x>
     <y>241</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>279</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_contrast</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_contrast()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>97</x>
     <y>241</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>336</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_contrast</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_contrast()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>110</x>
     <y>241</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>350</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_saturation</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_saturation()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>97</x>
     <y>241</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>407</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_saturation</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_saturation()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>60</x>
     <y>236</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>426</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_hue</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_hue()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>97</x>
     <y>241</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>480</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_hue</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_hue()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>60</x>
     <y>236</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>498</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_sharpness</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_sharpness()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>97</x>
     <y>241</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>552</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_sharpness</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_sharpness()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>60</x>
     <y>236</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>573</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_gamma</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_gamma()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>97</x>
     <y>241</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>623</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_gamma</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_gamma()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>60</x>
     <y>236</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>646</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_backlightcompensation</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_backlightcompensation()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>97</x>
     <y>241</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>697</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_backlightcompensation</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_backlightcompensation()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>60</x>
     <y>236</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>715</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_gain</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_gain()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>97</x>
     <y>241</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>613</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_gain</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_gain()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>60</x>
     <y>236</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>635</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_whitebalance</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_whitebalance()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>97</x>
     <y>241</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>686</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_whitebalance</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_whitebalance()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>60</x>
     <y>236</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>705</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_exposure</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_exposure()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>117</x>
     <y>231</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>278</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_exposure</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_exposure()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>50</x>
     <y>226</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>294</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>checkBox_exposure</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>select_exposure_auto()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>60</x>
     <y>221</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>230</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>checkBox_whitebalance</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>select_whitebalance_auto()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>70</x>
     <y>241</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>661</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_focus</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_focus()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>117</x>
     <y>231</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>369</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_focus</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_focus()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>50</x>
     <y>226</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>385</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>checkBox_focus</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>select_focus_auto()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>60</x>
     <y>221</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>322</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_zoom</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_zoom()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>117</x>
     <y>231</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>453</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_zoom</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_zoom()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>50</x>
     <y>226</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>473</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_pan</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_pan()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>117</x>
     <y>231</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>544</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_pan</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_pan()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>50</x>
     <y>226</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>580</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>horizontalSlider_tilt</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_tilt()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>117</x>
     <y>231</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>642</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>spinBox_tilt</sender>
   <signal>valueChanged(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_tilt()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>50</x>
     <y>226</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>675</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton_default1</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>set_default1()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>92</x>
     <y>241</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>743</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton_default2</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>set_default2()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>81</x>
     <y>231</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>754</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>p1</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>Signals1()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>35</x>
     <y>175</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>174</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>p2</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>Signals2()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>95</x>
     <y>174</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>154</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>p3</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>Signals3()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>163</x>
     <y>176</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>196</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>p4</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>Signals4()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>245</x>
     <y>177</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>140</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>p_1</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>Signals1()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>53</x>
     <y>813</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>816</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>p_2</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>Signals2()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>120</x>
     <y>813</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>800</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>p_3</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>Signals3()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>182</x>
     <y>813</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>790</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>p_4</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>Signals4()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>244</x>
     <y>813</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>782</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>p1_2</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>Signals5()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>75</x>
     <y>853</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>859</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>p3_4</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>Signals6()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>154</x>
     <y>853</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>873</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>p1234</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>Signals7()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>228</x>
     <y>856</y>
    </hint>
    <hint type="destinationlabel">
     <x>0</x>
     <y>850</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>recordingSet</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>videoset()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>697</x>
     <y>45</y>
    </hint>
    <hint type="destinationlabel">
     <x>723</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>fileManage</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>filemanage()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>853</x>
     <y>42</y>
    </hint>
    <hint type="destinationlabel">
     <x>855</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>closeButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>onCloseButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>1245</x>
     <y>16</y>
    </hint>
    <hint type="destinationlabel">
     <x>1257</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton_max</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>onMaximizeClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>1221</x>
     <y>15</y>
    </hint>
    <hint type="destinationlabel">
     <x>1223</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton_min</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>onMinimizeClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>1202</x>
     <y>13</y>
    </hint>
    <hint type="destinationlabel">
     <x>1193</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>systemSet</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>systemset()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>950</x>
     <y>37</y>
    </hint>
    <hint type="destinationlabel">
     <x>951</x>
     <y>-36</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>checkBox_audio</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindow</receiver>
   <slot>isRecordAudio(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>30</x>
     <y>740</y>
    </hint>
    <hint type="destinationlabel">
     <x>-109</x>
     <y>743</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>comboBox_audiodev</sender>
   <signal>activated(int)</signal>
   <receiver>MainWindow</receiver>
   <slot>select_audio(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>26</x>
     <y>698</y>
    </hint>
    <hint type="destinationlabel">
     <x>-130</x>
     <y>622</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>timewmark</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>TimeWatermark()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>166</x>
     <y>236</y>
    </hint>
    <hint type="destinationlabel">
     <x>-65</x>
     <y>207</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindow</receiver>
   <slot>aboutus()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>366</x>
     <y>33</y>
    </hint>
    <hint type="destinationlabel">
     <x>345</x>
     <y>-22</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>onCloseButtonClicked()</slot>
  <slot>toggleVideoFullscreen()</slot>
  <slot>select_video(int)</slot>
  <slot>select_format(int)</slot>
  <slot>select_resolution(int)</slot>
  <slot>VideoSetPage()</slot>
  <slot>PictureSetPage()</slot>
  <slot>CameraControlPage()</slot>
  <slot>TextSetPage()</slot>
  <slot>onMinimizeClicked()</slot>
  <slot>onMaximizeClicked()</slot>
  <slot>select_brightness()</slot>
  <slot>select_contrast()</slot>
  <slot>select_saturation()</slot>
  <slot>select_hue()</slot>
  <slot>select_sharpness()</slot>
  <slot>select_gamma()</slot>
  <slot>select_backlightcompensation()</slot>
  <slot>select_gain()</slot>
  <slot>select_whitebalance()</slot>
  <slot>select_exposure()</slot>
  <slot>select_exposure_auto()</slot>
  <slot>select_whitebalance_auto()</slot>
  <slot>select_focus()</slot>
  <slot>select_focus_auto()</slot>
  <slot>select_zoom()</slot>
  <slot>select_pan()</slot>
  <slot>select_tilt()</slot>
  <slot>set_default1()</slot>
  <slot>set_default2()</slot>
  <slot>Signals1()</slot>
  <slot>Signals2()</slot>
  <slot>Signals3()</slot>
  <slot>Signals4()</slot>
  <slot>Signals5()</slot>
  <slot>Signals6()</slot>
  <slot>Signals7()</slot>
  <slot>videoset()</slot>
  <slot>filemanage()</slot>
  <slot>systemset()</slot>
  <slot>isRecordAudio(bool)</slot>
  <slot>select_audio(int)</slot>
  <slot>TimeWatermark()</slot>
  <slot>aboutus()</slot>
 </slots>
</ui>
