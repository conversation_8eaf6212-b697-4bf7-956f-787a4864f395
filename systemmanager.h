#ifndef SYSTEMMANAGER_H
#define SYSTEMMANAGER_H

#include <QObject>

class SystemManager : public QObject
{
    Q_OBJECT
public:
    explicit SystemManager(QObject *parent = nullptr);
    QString getTranslatedText(const QString& key, int languageIndex, QString group);
    //路径 - 对应systemset.cpp中的语言索引
    QStringList TranslatedPaths = {
        "/data/Language/Chinese.ini",           // 0: 中文
        "/data/Language/English.ini",           // 1: English
        "/data/Language/Arabic.ini",            // 2: Arabic
        "/data/Language/TraditionalChinese.ini", // 3: 繁体中文
        "/data/Language/French.ini",            // 4: French
        "/data/Language/German.ini",            // 5: German
        "/data/Language/Italian.ini",           // 6: Italian
        "/data/Language/Japanese.ini",          // 7: Japanese
        "/data/Language/Korean.ini",            // 8: Korean
        "/data/Language/Portuguese.ini",        // 9: Portuguese
        "/data/Language/Russian.ini",           // 10: Russian
        "/data/Language/Spanish.ini",           // 11: Spanish
        "/data/Language/Turkish.ini",           // 12: Turkish
    };

signals:
};


#endif // SYSTEMMANAGER_H
