<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RecordingSet</class>
 <widget class="QDialog" name="RecordingSet">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>484</width>
    <height>495</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QWidget" name="widget" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>24</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(120, 120, 120);</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_24">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_23">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="label_13">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>24</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>24</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>aakar</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>录像设置</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_15">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="recordingset_close">
          <property name="minimumSize">
           <size>
            <width>25</width>
            <height>24</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>25</width>
            <height>24</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
	image: url(:/image/关闭.png);
border-style:none;
padding:5px;

background:transparent;
}
QPushButton:hover{
	background-color: rgb(239, 41, 41);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QWidget" name="widget_2" native="true">
     <property name="styleSheet">
      <string notr="true">background-color: rgb(90, 90, 90);</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <layout class="QVBoxLayout" name="verticalLayout_4">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_18">
            <item>
             <widget class="QLabel" name="label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>25</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>25</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>存储路径</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_11">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <item>
             <widget class="QTextEdit" name="textEdit_storagepath">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>27</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>27</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(255, 255, 255);</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QToolButton" name="toolButton_choosestorage">
              <property name="minimumSize">
               <size>
                <width>35</width>
                <height>27</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>35</width>
                <height>27</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>...</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_21">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <item>
             <widget class="QLabel" name="label_3">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>文件前缀名：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QTextEdit" name="textEdit_prefix">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>27</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>27</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(255, 255, 255);</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="horizontalSpacer_13">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_22">
          <item>
           <layout class="QGridLayout" name="gridLayout_2">
            <item row="0" column="0">
             <widget class="QCheckBox" name="checkBox_segrecord">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>分段录像</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QRadioButton" name="radioButton_time">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>按时间单位中断</string>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QRadioButton" name="radioButton_byte">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>按字节单位中断</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <layout class="QHBoxLayout" name="horizontalLayout_3">
              <item>
               <widget class="QSpinBox" name="spinBox_time">
                <property name="styleSheet">
                 <string notr="true">background-color: rgb(255, 255, 255);</string>
                </property>
                <property name="maximum">
                 <number>9999</number>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_4">
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);</string>
                </property>
                <property name="text">
                 <string>分</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item row="1" column="2">
             <layout class="QHBoxLayout" name="horizontalLayout_4">
              <item>
               <widget class="QSpinBox" name="spinBox_byte">
                <property name="styleSheet">
                 <string notr="true">background-color: rgb(255, 255, 255);</string>
                </property>
                <property name="maximum">
                 <number>9999</number>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_5">
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);</string>
                </property>
                <property name="text">
                 <string>兆</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="horizontalSpacer_14">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_15">
            <item>
             <widget class="QLabel" name="label_6">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>输出设置</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_7">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_13">
            <item>
             <spacer name="horizontalSpacer_4">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_11">
              <item>
               <widget class="QLabel" name="label_7">
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);</string>
                </property>
                <property name="text">
                 <string>文件格式：MPEG MP4</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_8">
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);</string>
                </property>
                <property name="text">
                 <string>视频编码：H264</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="horizontalSpacer_6">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_14">
            <item>
             <spacer name="horizontalSpacer_5">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_12">
              <item>
               <widget class="QLabel" name="label_9">
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);</string>
                </property>
                <property name="text">
                 <string>录像画质：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QRadioButton" name="radioButton_high">
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);</string>
                </property>
                <property name="text">
                 <string>高质量</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QRadioButton" name="radioButton_middle">
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);</string>
                </property>
                <property name="text">
                 <string>中画质</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QRadioButton" name="radioButton_low">
                <property name="styleSheet">
                 <string notr="true">color: rgb(255, 255, 255);</string>
                </property>
                <property name="text">
                 <string>低画质</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="horizontalSpacer_8">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_9">
            <item>
             <widget class="QLabel" name="label_10">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>磁盘设置</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_10">
            <item>
             <spacer name="horizontalSpacer_2">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_8">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_7">
                <item>
                 <widget class="QCheckBox" name="checkBox_storagelimit">
                  <property name="styleSheet">
                   <string notr="true">color: rgb(255, 255, 255);</string>
                  </property>
                  <property name="text">
                   <string>设置录像存储限制</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <layout class="QHBoxLayout" name="horizontalLayout_5">
                  <item>
                   <widget class="QSpinBox" name="spinBox_storagelimit">
                    <property name="styleSheet">
                     <string notr="true">background-color: rgb(255, 255, 255);</string>
                    </property>
                    <property name="maximum">
                     <number>9999</number>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QLabel" name="label_11">
                    <property name="styleSheet">
                     <string notr="true">color: rgb(255, 255, 255);</string>
                    </property>
                    <property name="text">
                     <string>GB</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_6">
                <item>
                 <widget class="QLabel" name="label_12">
                  <property name="styleSheet">
                   <string notr="true">color: rgb(255, 255, 255);</string>
                  </property>
                  <property name="text">
                   <string>可用空间：</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label_freespace">
                  <property name="styleSheet">
                   <string notr="true">color: rgb(255, 255, 255);</string>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="horizontalSpacer_3">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_16">
          <item>
           <widget class="QLabel" name="label_14">
            <property name="text">
             <string>移动侦测</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_9">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_17">
          <item>
           <widget class="QLabel" name="label_15">
            <property name="text">
             <string>定时录像</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_10">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_20">
          <item>
           <spacer name="horizontalSpacer_12">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_19">
            <item>
             <widget class="QPushButton" name="pushButton">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>确定</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_2">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>取消</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="horizontalSpacer_16">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>recordingset_close</sender>
   <signal>clicked()</signal>
   <receiver>RecordingSet</receiver>
   <slot>closewindow()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>469</x>
     <y>14</y>
    </hint>
    <hint type="destinationlabel">
     <x>475</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>toolButton_choosestorage</sender>
   <signal>clicked()</signal>
   <receiver>RecordingSet</receiver>
   <slot>PathSelect()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>451</x>
     <y>82</y>
    </hint>
    <hint type="destinationlabel">
     <x>426</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton</sender>
   <signal>clicked()</signal>
   <receiver>RecordingSet</receiver>
   <slot>recordingSettings()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>187</x>
     <y>465</y>
    </hint>
    <hint type="destinationlabel">
     <x>-41</x>
     <y>465</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton_2</sender>
   <signal>clicked()</signal>
   <receiver>RecordingSet</receiver>
   <slot>closewindow()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>264</x>
     <y>464</y>
    </hint>
    <hint type="destinationlabel">
     <x>272</x>
     <y>490</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>checkBox_segrecord</sender>
   <signal>clicked()</signal>
   <receiver>RecordingSet</receiver>
   <slot>Sectional_recording()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>68</x>
     <y>154</y>
    </hint>
    <hint type="destinationlabel">
     <x>-105</x>
     <y>154</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>radioButton_time</sender>
   <signal>clicked()</signal>
   <receiver>RecordingSet</receiver>
   <slot>time_segment()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>121</x>
     <y>159</y>
    </hint>
    <hint type="destinationlabel">
     <x>-156</x>
     <y>174</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>radioButton_byte</sender>
   <signal>clicked()</signal>
   <receiver>RecordingSet</receiver>
   <slot>byte_segment()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>279</x>
     <y>162</y>
    </hint>
    <hint type="destinationlabel">
     <x>-296</x>
     <y>165</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>checkBox_storagelimit</sender>
   <signal>clicked()</signal>
   <receiver>RecordingSet</receiver>
   <slot>video_storage()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>71</x>
     <y>367</y>
    </hint>
    <hint type="destinationlabel">
     <x>-91</x>
     <y>367</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>closewindow()</slot>
  <slot>PathSelect()</slot>
  <slot>recordingSettings()</slot>
  <slot>Sectional_recording()</slot>
  <slot>time_segment()</slot>
  <slot>byte_segment()</slot>
  <slot>video_storage()</slot>
 </slots>
</ui>
