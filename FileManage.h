#ifndef FILEMANAGE_H
#define FILEMANAGE_H

#include <QDialog>
#include <QMouseEvent>
#include "ui_filemanage.h"
#include <QMessageBox>
#include <QMouseEvent>
#include <QDir>
#include <QFileInfoList>
#include <QStandardItemModel>
#include <QStandardItem>
#include <QFileIconProvider>
#include <QFileSystemModel>
#include <QStyledItemDelegate>

#include <QSortFilterProxyModel>
#include <CustomFileSystemModel.h>
#include "CustomFilterProxyModel.h"
#include <QVBoxLayout>
#include "CustomProgressDialog.h"
#include "CustomMessageBox.h"
#include <QLabel>
#include <QProgressBar>
// 前向声明
class MainWindow;
class CameraStream;

namespace Ui {
class FileManage;
}

class FileManage : public QDialog
{
    Q_OBJECT

public:
    explicit FileManage(QWidget *parent = nullptr);
    ~FileManage();

    QString getCurrentFolderPath() const;

private:
    Ui::FileManage *ui;
    bool mouse_press;
    QPoint mousePoint;

public:
    QFileSystemModel* m_fileSystemModel; // 声明文件系统模型
    QString m_currentFolderPath;//当前文件夹路径
    QString m_currentFolder;
    CustomFilterProxyModel* m_filterProxyModel; // 过滤代理模型

    QMenu* m_contextMenu; // 右键菜单
    QAction* m_deleteAction; // 删除操作



    void navigateToFolder(const QString& folderPath);
    QString getAbsolutePath(const QString& relativePath);
    QString getBasePath() const; // 获取当前配置的基础路径
    bool ensureDirectoryExists(const QString& dirPath); // 确保目录存在
    void initializeFileSystemModel();
    void resetFilters();
    void TreeorListShow();

    //右键菜单
    void showContextMenu(const QPoint& pos);
    //打开
    void onContextOpen(const QModelIndex& index);

    //删除
    // 检查文件是否正在被录像使用
    bool isFileBeingRecorded(const QString& filePath);
    // 递归检查文件夹中是否有正在录像的文件
    bool hasRecordingFilesInDirectory(const QString& dirPath);
    void onContextMenuDelete(const QModelIndex& index);
    //属性
    QString getReadableSize(qint64 bytes);
    qint64 calculateFolderSize(const QString& folderPath);
    void onContextMenuProperties(const QFileInfo& fileInfo);
    void onContextMenuTransfer(const QFileInfo& fileInfo);
    bool copyDirectoryRecursively(const QString& sourceDir, const QString& targetDir);
    void showTransferProgress(const QString& fileName, int progress);

    // 时间格式化函数
    QString formatTransferTime(qint64 durationMs);

    // 实时进度跟踪相关
    void startRealTimeTransfer(const QFileInfo& fileInfo, const QString& targetPath);
    void monitorTransferProgress(const QString& targetPath, qint64 totalSize);
    qint64 calculateDirectorySize(const QString& dirPath);

    // 动画相关函数
    void updateScrollingAnimation();
    void updateProgressBarStyle(int offset);
    void setRealProgress(int progress);

private:
    // 进度跟踪变量
    CustomProgressDialog* m_progressDialog;
    QProgressBar* m_progressBar;
    QLabel* m_progressLabel;
    QLabel* m_speedLabel;
    QTimer* m_progressTimer;
    QTimer* m_animationTimer;
    qint64 m_totalBytes;
    qint64 m_copiedBytes;
    QString m_targetPath;

    // 时间跟踪变量
    QDateTime m_transferStartTime;     // 传输开始时间
    QDateTime m_transferEndTime;       // 传输结束时间
    qint64 m_transferDurationMs;       // 传输持续时间（毫秒）

    // 动画相关变量
    int m_realProgress;            // 真实进度百分比
    int m_animationOffset;         // 滚动动画偏移量

    //重命名
    void onContextMenuRename(const QModelIndex& index);
private:
    VirtualKeyboard* m_virtualKeyboard;  // 虚拟键盘实例
    // 初始化键盘相关功能
    void setupLineEditKeyboard(QLineEdit *lineEdit);
protected:
    // 鼠标按下事件 - 开始拖动
    void mousePressEvent(QMouseEvent* event) override
    {
        // 当左键点击在指定widget区域内时
        if (event->button() == Qt::LeftButton && ui->widget->geometry().contains(event->pos()))
        {
            mouse_press = true;                  // 设置拖动标志
            mousePoint = event->pos();            // 记录鼠标相对窗口的位置
            event->accept();                      // 接受事件
        }
    }

    // 鼠标移动事件 - 处理拖动
    void mouseMoveEvent(QMouseEvent* event) override
    {
        // 如果正在拖动且左键按住
        if (mouse_press && (event->buttons() & Qt::LeftButton))
        {
            QPoint globalPos = mapToGlobal(event->pos());  // 获取鼠标全局位置
            QPoint newPos = globalPos - mousePoint;        // 计算窗口新位置
            move(newPos);                                 // 移动窗口
            event->accept();                              // 接受事件
        }
    }

    // 鼠标释放事件 - 结束拖动
    void mouseReleaseEvent(QMouseEvent* event) override
    {
        if (event->button() == Qt::LeftButton)
        {
            mouse_press = false;  // 清除拖动标志
            event->accept();      // 接受事件
        }
    }
    bool eventFilter(QObject *obj, QEvent *event) override;

private slots:
    void closewindow(){
        close();
    }
    void closedate(){
        ui->widget_4->setVisible(false);
    }
public slots:
    void TreeViewShow();
    void ListViewShow();
    void SearchFile();
    void SelectDate();
    void onPictureButtonClicked();
    void onVideoButtonClicked();
    void onChannel1ButtonClicked();
    void onChannel2ButtonClicked();
    void onChannel3ButtonClicked();
    void onChannel4ButtonClicked();
    void SetSelectDate(const QDate& fdate);
    void onFileDoubleClicked(const QModelIndex& index);
    void onTFcardButtonClicked();
};


#endif // FILEMANAGE_H
