#ifndef SYSTEMSET_H
#define SYSTEMSET_H

#include <QDialog>
#include "ui_systemset.h"
#include "virtualkeyboard.h"
#include <QTextEdit>
#include <QMouseEvent>

namespace Ui {
class Systemset;
}

class Systemset : public QDialog
{
    Q_OBJECT

public:
    explicit Systemset(QWidget *parent = nullptr);
    ~Systemset();

private:
    Ui::Systemset *ui;
    bool mouse_press;
    QPoint mousePoint;

    VirtualKeyboard* m_virtualKeyboard;  // 虚拟键盘实例

    QTimer *timeTimer;  // 定时器用于更新时间显示

    // 初始化函数
    void setupSpinBoxKeyboard(QSpinBox* spinBox);
    void showKeyboardForSpinBox(QSpinBox* spinBox);
    // 事件过滤
    bool eventFilter(QObject* obj, QEvent* event) override;

protected:
    // 鼠标按下事件 - 开始拖动
    void mousePressEvent(QMouseEvent* event) override
    {
        // 当左键点击在指定widget区域内时
        if (event->button() == Qt::LeftButton && ui->widget->geometry().contains(event->pos()))
        {
            mouse_press = true;                  // 设置拖动标志
            mousePoint = event->pos();            // 记录鼠标相对窗口的位置
            event->accept();                      // 接受事件
        }
    }

    // 鼠标移动事件 - 处理拖动
    void mouseMoveEvent(QMouseEvent* event) override
    {
        // 如果正在拖动且左键按住
        if (mouse_press && (event->buttons() & Qt::LeftButton))
        {
            QPoint globalPos = mapToGlobal(event->pos());  // 获取鼠标全局位置
            QPoint newPos = globalPos - mousePoint;        // 计算窗口新位置
            move(newPos);                                 // 移动窗口
            event->accept();                              // 接受事件
        }
    }

    // 鼠标释放事件 - 结束拖动
    void mouseReleaseEvent(QMouseEvent* event) override
    {
        if (event->button() == Qt::LeftButton)
        {
            mouse_press = false;  // 清除拖动标志
            event->accept();      // 接受事件
        }
    }


signals:
    void Languagechanged();  // 定义一个信号，通知语言已切换


private slots:
    void closewindow(){
        close();
    }
    void combo_language(int dex);
    void set_time();
    void updateTimeDisplay();
    void changetime();

};

#endif // SYSTEMSET_H
