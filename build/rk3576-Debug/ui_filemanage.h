/********************************************************************************
** Form generated from reading UI file 'filemanage.ui'
**
** Created by: Qt User Interface Compiler version 5.15.11
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FILEMANAGE_H
#define UI_FILEMANAGE_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCalendarWidget>
#include <QtWidgets/QDialog>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QListView>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTreeView>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_FileManage
{
public:
    QGridLayout *gridLayout_2;
    QWidget *widget;
    QHBoxLayout *horizontalLayout;
    QLabel *label;
    QSpacerItem *horizontalSpacer;
    QPushButton *close1;
    QWidget *widget_2;
    QVBoxLayout *verticalLayout_5;
    QVBoxLayout *verticalLayout_4;
    QVBoxLayout *verticalLayout_3;
    QVBoxLayout *verticalLayout;
    QPushButton *picture;
    QPushButton *video;
    QSpacerItem *verticalSpacer_2;
    QVBoxLayout *verticalLayout_2;
    QPushButton *CH1;
    QPushButton *CH2;
    QPushButton *CH3;
    QPushButton *CH4;
    QSpacerItem *verticalSpacer;
    QPushButton *tfcard;
    QWidget *widget_3;
    QGridLayout *gridLayout;
    QPushButton *Choosedate;
    QHBoxLayout *horizontalLayout_4;
    QLineEdit *lineEdit_search;
    QSpacerItem *horizontalSpacer_2;
    QHBoxLayout *horizontalLayout_3;
    QPushButton *pushButton_9;
    QHBoxLayout *horizontalLayout_2;
    QPushButton *LB;
    QPushButton *SLT;
    QWidget *widget_4;
    QVBoxLayout *verticalLayout_6;
    QWidget *widget_5;
    QHBoxLayout *horizontalLayout_5;
    QLabel *label_2;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *close2;
    QCalendarWidget *calendarWidget;
    QListView *listView;
    QTreeView *treeView;

    void setupUi(QDialog *FileManage)
    {
        if (FileManage->objectName().isEmpty())
            FileManage->setObjectName(QString::fromUtf8("FileManage"));
        FileManage->resize(852, 673);
        gridLayout_2 = new QGridLayout(FileManage);
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        gridLayout_2->setContentsMargins(0, 0, 0, 0);
        widget = new QWidget(FileManage);
        widget->setObjectName(QString::fromUtf8("widget"));
        widget->setMinimumSize(QSize(0, 24));
        widget->setMaximumSize(QSize(16777215, 24));
        widget->setStyleSheet(QString::fromUtf8("background-color: rgb(120, 120, 120);"));
        horizontalLayout = new QHBoxLayout(widget);
        horizontalLayout->setSpacing(0);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        label = new QLabel(widget);
        label->setObjectName(QString::fromUtf8("label"));
        QFont font;
        font.setFamily(QString::fromUtf8("aakar"));
        font.setPointSize(12);
        label->setFont(font);
        label->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout->addWidget(label);

        horizontalSpacer = new QSpacerItem(733, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        close1 = new QPushButton(widget);
        close1->setObjectName(QString::fromUtf8("close1"));
        close1->setMinimumSize(QSize(25, 24));
        close1->setMaximumSize(QSize(25, 24));
        close1->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/image/\345\205\263\351\227\255.png);\n"
"border-style:none;\n"
"padding:5px;\n"
"\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	background-color: rgb(239, 41, 41);\n"
"}"));

        horizontalLayout->addWidget(close1);


        gridLayout_2->addWidget(widget, 0, 0, 1, 2);

        widget_2 = new QWidget(FileManage);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        widget_2->setMinimumSize(QSize(100, 0));
        widget_2->setMaximumSize(QSize(16777215, 16777215));
        widget_2->setStyleSheet(QString::fromUtf8("background-color: rgb(90, 90, 90);"));
        verticalLayout_5 = new QVBoxLayout(widget_2);
        verticalLayout_5->setSpacing(0);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        verticalLayout_5->setContentsMargins(0, 0, 0, 0);
        verticalLayout_4 = new QVBoxLayout();
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        verticalLayout_3 = new QVBoxLayout();
        verticalLayout_3->setSpacing(40);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        picture = new QPushButton(widget_2);
        picture->setObjectName(QString::fromUtf8("picture"));
        picture->setMinimumSize(QSize(100, 30));
        picture->setMaximumSize(QSize(100, 30));
        picture->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}color: rgb(255, 255, 255);"));
        picture->setCheckable(true);
        picture->setAutoExclusive(true);

        verticalLayout->addWidget(picture);

        video = new QPushButton(widget_2);
        video->setObjectName(QString::fromUtf8("video"));
        video->setMinimumSize(QSize(100, 30));
        video->setMaximumSize(QSize(100, 30));
        video->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}color: rgb(255, 255, 255);"));
        video->setCheckable(true);
        video->setAutoExclusive(true);

        verticalLayout->addWidget(video);


        verticalLayout_3->addLayout(verticalLayout);

        verticalSpacer_2 = new QSpacerItem(20, 60, QSizePolicy::Minimum, QSizePolicy::Fixed);

        verticalLayout_3->addItem(verticalSpacer_2);

        verticalLayout_2 = new QVBoxLayout();
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        CH1 = new QPushButton(widget_2);
        CH1->setObjectName(QString::fromUtf8("CH1"));
        CH1->setMinimumSize(QSize(100, 40));
        CH1->setMaximumSize(QSize(100, 40));
        CH1->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}color: rgb(255, 255, 255);"));
        CH1->setCheckable(true);
        CH1->setAutoExclusive(true);

        verticalLayout_2->addWidget(CH1);

        CH2 = new QPushButton(widget_2);
        CH2->setObjectName(QString::fromUtf8("CH2"));
        CH2->setMinimumSize(QSize(100, 40));
        CH2->setMaximumSize(QSize(100, 40));
        CH2->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}color: rgb(255, 255, 255);"));
        CH2->setCheckable(true);
        CH2->setAutoExclusive(true);

        verticalLayout_2->addWidget(CH2);

        CH3 = new QPushButton(widget_2);
        CH3->setObjectName(QString::fromUtf8("CH3"));
        CH3->setMinimumSize(QSize(100, 40));
        CH3->setMaximumSize(QSize(100, 40));
        CH3->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}color: rgb(255, 255, 255);"));
        CH3->setCheckable(true);
        CH3->setAutoExclusive(true);

        verticalLayout_2->addWidget(CH3);

        CH4 = new QPushButton(widget_2);
        CH4->setObjectName(QString::fromUtf8("CH4"));
        CH4->setMinimumSize(QSize(100, 40));
        CH4->setMaximumSize(QSize(100, 40));
        CH4->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}color: rgb(255, 255, 255);"));
        CH4->setCheckable(true);
        CH4->setAutoExclusive(true);

        verticalLayout_2->addWidget(CH4);


        verticalLayout_3->addLayout(verticalLayout_2);


        verticalLayout_4->addLayout(verticalLayout_3);

        verticalSpacer = new QSpacerItem(20, 358, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_4->addItem(verticalSpacer);

        tfcard = new QPushButton(widget_2);
        tfcard->setObjectName(QString::fromUtf8("tfcard"));
        tfcard->setMinimumSize(QSize(100, 40));
        tfcard->setMaximumSize(QSize(100, 40));
        tfcard->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}color: rgb(255, 255, 255);"));
        tfcard->setCheckable(true);

        verticalLayout_4->addWidget(tfcard);


        verticalLayout_5->addLayout(verticalLayout_4);


        gridLayout_2->addWidget(widget_2, 1, 0, 1, 1);

        widget_3 = new QWidget(FileManage);
        widget_3->setObjectName(QString::fromUtf8("widget_3"));
        widget_3->setStyleSheet(QString::fromUtf8("background-color: rgb(90, 90, 90);"));
        gridLayout = new QGridLayout(widget_3);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        gridLayout->setVerticalSpacing(4);
        gridLayout->setContentsMargins(2, 0, 0, 0);
        Choosedate = new QPushButton(widget_3);
        Choosedate->setObjectName(QString::fromUtf8("Choosedate"));
        Choosedate->setMinimumSize(QSize(750, 50));
        Choosedate->setMaximumSize(QSize(750, 50));
        QFont font1;
        font1.setPointSize(26);
        Choosedate->setFont(font1);
        Choosedate->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}color: rgb(255, 255, 255);"));

        gridLayout->addWidget(Choosedate, 0, 0, 1, 1);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        lineEdit_search = new QLineEdit(widget_3);
        lineEdit_search->setObjectName(QString::fromUtf8("lineEdit_search"));
        lineEdit_search->setMinimumSize(QSize(630, 30));
        lineEdit_search->setMaximumSize(QSize(16777215, 30));
        lineEdit_search->setStyleSheet(QString::fromUtf8("QLineEdit {\n"
"	color: rgb(0, 0, 0);\n"
"	background-color: rgb(255, 255, 255);\n"
"border: 0px solid #CCCCCC;\n"
"border-radius: 6px;\n"
"padding: 6px;\n"
"}\n"
"QLineEdit:disabled {\n"
"       background-color: #E0E0E0;\n"
"       color: #999999;\n"
"}"));

        horizontalLayout_4->addWidget(lineEdit_search);

        horizontalSpacer_2 = new QSpacerItem(18, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_2);

        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        pushButton_9 = new QPushButton(widget_3);
        pushButton_9->setObjectName(QString::fromUtf8("pushButton_9"));
        pushButton_9->setMinimumSize(QSize(30, 30));
        pushButton_9->setMaximumSize(QSize(30, 30));
        pushButton_9->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"\n"
"	image: url(:/image/icons/\346\220\234\347\264\242.png);\n"
"\n"
"\n"
"\n"
"border-radius: 6px;\n"
"\n"
"\n"
"\n"
"}\n"
"\n"
"QPushButton::pressed,QPushButton::checked{\n"
"\n"
"	image: url(:/image/icons/\346\220\234\347\264\242(\351\227\252).png);\n"
"\n"
"}"));

        horizontalLayout_3->addWidget(pushButton_9);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setSpacing(0);
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        LB = new QPushButton(widget_3);
        LB->setObjectName(QString::fromUtf8("LB"));
        LB->setMinimumSize(QSize(30, 30));
        LB->setMaximumSize(QSize(30, 30));
        LB->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"\n"
"	image: url(:/image/icons/\345\210\227\350\241\250.png);\n"
"\n"
"\n"
"\n"
"border-radius: 6px;\n"
"\n"
"\n"
"\n"
"}\n"
"\n"
"QPushButton::pressed,QPushButton::checked{\n"
"\n"
"	image: url(:/image/icons/\345\210\227\350\241\250(\351\227\252).png);\n"
"\n"
"}"));
        LB->setCheckable(true);
        LB->setChecked(true);
        LB->setAutoExclusive(true);

        horizontalLayout_2->addWidget(LB);

        SLT = new QPushButton(widget_3);
        SLT->setObjectName(QString::fromUtf8("SLT"));
        SLT->setMinimumSize(QSize(30, 30));
        SLT->setMaximumSize(QSize(30, 30));
        SLT->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"\n"
"	image: url(:/image/icons/\347\274\251\347\225\245\345\233\276.png);\n"
"\n"
"\n"
"\n"
"border-radius: 6px;\n"
"\n"
"\n"
"\n"
"}\n"
"\n"
"QPushButton::pressed,QPushButton::checked{\n"
"\n"
"	image: url(:/image/icons/\347\274\251\347\225\245\345\233\276(\351\227\252).png);\n"
"\n"
"}"));
        SLT->setCheckable(true);
        SLT->setAutoExclusive(true);

        horizontalLayout_2->addWidget(SLT);


        horizontalLayout_3->addLayout(horizontalLayout_2);


        horizontalLayout_4->addLayout(horizontalLayout_3);


        gridLayout->addLayout(horizontalLayout_4, 1, 0, 1, 1);

        widget_4 = new QWidget(widget_3);
        widget_4->setObjectName(QString::fromUtf8("widget_4"));
        widget_4->setStyleSheet(QString::fromUtf8("background-color: rgb(136, 138, 133);"));
        verticalLayout_6 = new QVBoxLayout(widget_4);
        verticalLayout_6->setSpacing(0);
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        verticalLayout_6->setContentsMargins(0, 0, 0, 0);
        widget_5 = new QWidget(widget_4);
        widget_5->setObjectName(QString::fromUtf8("widget_5"));
        widget_5->setMinimumSize(QSize(0, 22));
        widget_5->setMaximumSize(QSize(16777215, 22));
        widget_5->setStyleSheet(QString::fromUtf8("background-color: rgb(120, 120, 120);"));
        horizontalLayout_5 = new QHBoxLayout(widget_5);
        horizontalLayout_5->setSpacing(0);
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        horizontalLayout_5->setContentsMargins(6, 0, 0, 0);
        label_2 = new QLabel(widget_5);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        label_2->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_5->addWidget(label_2);

        horizontalSpacer_3 = new QSpacerItem(689, 19, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_3);

        close2 = new QPushButton(widget_5);
        close2->setObjectName(QString::fromUtf8("close2"));
        close2->setMinimumSize(QSize(22, 22));
        close2->setMaximumSize(QSize(22, 22));
        close2->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/image/\345\205\263\351\227\255.png);\n"
"border-style:none;\n"
"padding:5px;\n"
"\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	background-color: rgb(239, 41, 41);\n"
"}"));

        horizontalLayout_5->addWidget(close2);


        verticalLayout_6->addWidget(widget_5);

        calendarWidget = new QCalendarWidget(widget_4);
        calendarWidget->setObjectName(QString::fromUtf8("calendarWidget"));
        calendarWidget->setStyleSheet(QString::fromUtf8("QCalendarWidget QWidget {\n"
"            alternate-background-color: #f0f0f0;\n"
"        }\n"
"\n"
"        QCalendarWidget QToolButton {\n"
"            \n"
"	background-color: rgb(85, 170, 255);\n"
"            color: white;\n"
"            font-size: 16px;\n"
"            icon-size: 24px, 24px;\n"
"        }\n"
"\n"
"        QCalendarWidget QMenu {\n"
"            background-color: white;\n"
"            color: black;\n"
"        }\n"
"\n"
"        QCalendarWidget QSpinBox {\n"
"            \n"
"	background-color: rgb(85, 170, 255);\n"
"            color: white;\n"
"	selection-background-color: rgb(85, 170, 255);\n"
"        }\n"
"\n"
"        QCalendarWidget QAbstractItemView:enabled {\n"
"            background-color: white;\n"
"            color: black;\n"
"            \n"
"	selection-background-color: rgb(85, 170, 255);\n"
"            selection-color: white;\n"
"        }\n"
"\n"
"        QCalendarWidget QAbstractItemView:disabled {\n"
"            background-color: #f0f0f0;\n"
"            color: #8"
                        "08080;\n"
"        }\n"
"QToolButton#qt_calendar_prevmonth {\n"
"    background: transparent;\n"
"    border: none;\n"
"    width: 40px;\n"
"	qproperty-icon: url(:/new/prefix1/image/icons/\345\267\246\347\256\255\345\244\264.png);\n"
"}\n"
"QToolButton#qt_calendar_nextmonth {\n"
"    background: transparent;\n"
"    border: none;\n"
"    width: 40px;\n"
"    qproperty-icon: url(:/new/prefix1/image/icons/\345\217\263\347\256\255\345\244\264.png);\n"
"}"));

        verticalLayout_6->addWidget(calendarWidget);


        gridLayout->addWidget(widget_4, 2, 0, 1, 1);

        listView = new QListView(widget_3);
        listView->setObjectName(QString::fromUtf8("listView"));
        listView->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        gridLayout->addWidget(listView, 3, 0, 1, 1);

        treeView = new QTreeView(widget_3);
        treeView->setObjectName(QString::fromUtf8("treeView"));
        treeView->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        gridLayout->addWidget(treeView, 4, 0, 1, 1);


        gridLayout_2->addWidget(widget_3, 1, 1, 1, 1);


        retranslateUi(FileManage);
        QObject::connect(close1, SIGNAL(clicked()), FileManage, SLOT(closewindow()));
        QObject::connect(close2, SIGNAL(clicked()), FileManage, SLOT(closedate()));
        QObject::connect(Choosedate, SIGNAL(clicked()), FileManage, SLOT(SelectDate()));
        QObject::connect(picture, SIGNAL(clicked()), FileManage, SLOT(onPictureButtonClicked()));
        QObject::connect(video, SIGNAL(clicked()), FileManage, SLOT(onVideoButtonClicked()));
        QObject::connect(CH1, SIGNAL(clicked()), FileManage, SLOT(onChannel1ButtonClicked()));
        QObject::connect(CH2, SIGNAL(clicked()), FileManage, SLOT(onChannel2ButtonClicked()));
        QObject::connect(CH3, SIGNAL(clicked()), FileManage, SLOT(onChannel3ButtonClicked()));
        QObject::connect(CH4, SIGNAL(clicked()), FileManage, SLOT(onChannel4ButtonClicked()));
        QObject::connect(calendarWidget, SIGNAL(clicked(QDate)), FileManage, SLOT(SetSelectDate(QDate)));
        QObject::connect(LB, SIGNAL(clicked()), FileManage, SLOT(ListViewShow()));
        QObject::connect(SLT, SIGNAL(clicked()), FileManage, SLOT(TreeViewShow()));
        QObject::connect(pushButton_9, SIGNAL(clicked()), FileManage, SLOT(SearchFile()));
        QObject::connect(tfcard, SIGNAL(clicked()), FileManage, SLOT(onTFcardButtonClicked()));

        QMetaObject::connectSlotsByName(FileManage);
    } // setupUi

    void retranslateUi(QDialog *FileManage)
    {
        FileManage->setWindowTitle(QCoreApplication::translate("FileManage", "Dialog", nullptr));
        label->setText(QCoreApplication::translate("FileManage", "\346\226\207\344\273\266\347\256\241\347\220\206", nullptr));
        close1->setText(QString());
        picture->setText(QCoreApplication::translate("FileManage", "\347\205\247\347\211\207", nullptr));
        video->setText(QCoreApplication::translate("FileManage", "\350\247\206\351\242\221", nullptr));
        CH1->setText(QCoreApplication::translate("FileManage", "\351\200\232\351\201\2231", nullptr));
        CH2->setText(QCoreApplication::translate("FileManage", "\351\200\232\351\201\2232", nullptr));
        CH3->setText(QCoreApplication::translate("FileManage", "\351\200\232\351\201\2233", nullptr));
        CH4->setText(QCoreApplication::translate("FileManage", "\351\200\232\351\201\2234", nullptr));
        tfcard->setText(QCoreApplication::translate("FileManage", "T-Flash", nullptr));
        Choosedate->setText(QCoreApplication::translate("FileManage", "\351\200\211    \346\213\251    \346\227\245    \346\234\237", nullptr));
        pushButton_9->setText(QString());
        LB->setText(QString());
        SLT->setText(QString());
        label_2->setText(QCoreApplication::translate("FileManage", "\346\227\245\346\234\237", nullptr));
        close2->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class FileManage: public Ui_FileManage {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FILEMANAGE_H
