/********************************************************************************
** Form generated from reading UI file 'systemset.ui'
**
** Created by: Qt User Interface Compiler version 5.15.11
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SYSTEMSET_H
#define UI_SYSTEMSET_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_Systemset
{
public:
    QGridLayout *gridLayout_3;
    QWidget *widget;
    QHBoxLayout *horizontalLayout_2;
    QHBoxLayout *horizontalLayout;
    QLabel *label;
    QSpacerItem *horizontalSpacer;
    QPushButton *pushButton;
    QWidget *widget_2;
    QGridLayout *gridLayout_2;
    QHBoxLayout *horizontalLayout_3;
    QLabel *language_label;
    QComboBox *comboBox;
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout_12;
    QLabel *label_9;
    QLabel *label_time;
    QGridLayout *gridLayout;
    QHBoxLayout *horizontalLayout_5;
    QSpinBox *spinBox_year;
    QLabel *label_2;
    QHBoxLayout *horizontalLayout_7;
    QComboBox *comboBox_month;
    QLabel *label_5;
    QHBoxLayout *horizontalLayout_6;
    QComboBox *comboBox_day;
    QLabel *label_4;
    QHBoxLayout *horizontalLayout_8;
    QSpinBox *spinBox_hour;
    QLabel *label_6;
    QHBoxLayout *horizontalLayout_9;
    QSpinBox *spinBox_min;
    QLabel *label_7;
    QHBoxLayout *horizontalLayout_10;
    QSpinBox *spinBox_second;
    QLabel *label_8;
    QHBoxLayout *horizontalLayout_11;
    QSpacerItem *horizontalSpacer_4;
    QPushButton *pushButton_2;
    QPushButton *pushButton_OK;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *horizontalLayout_4;
    QSpacerItem *horizontalSpacer_2;
    QLabel *label_3;
    QSpacerItem *horizontalSpacer_3;

    void setupUi(QDialog *Systemset)
    {
        if (Systemset->objectName().isEmpty())
            Systemset->setObjectName(QString::fromUtf8("Systemset"));
        Systemset->resize(303, 404);
        Systemset->setStyleSheet(QString::fromUtf8(""));
        gridLayout_3 = new QGridLayout(Systemset);
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName(QString::fromUtf8("gridLayout_3"));
        gridLayout_3->setContentsMargins(0, 0, 0, 0);
        widget = new QWidget(Systemset);
        widget->setObjectName(QString::fromUtf8("widget"));
        QSizePolicy sizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(widget->sizePolicy().hasHeightForWidth());
        widget->setSizePolicy(sizePolicy);
        widget->setMinimumSize(QSize(244, 24));
        widget->setMaximumSize(QSize(16777215, 24));
        widget->setStyleSheet(QString::fromUtf8("background-color: rgb(120, 120, 120);"));
        horizontalLayout_2 = new QHBoxLayout(widget);
        horizontalLayout_2->setSpacing(0);
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(0, 0, 0, 0);
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        label = new QLabel(widget);
        label->setObjectName(QString::fromUtf8("label"));
        QFont font;
        font.setPointSize(12);
        label->setFont(font);
        label->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout->addWidget(label);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        pushButton = new QPushButton(widget);
        pushButton->setObjectName(QString::fromUtf8("pushButton"));
        pushButton->setMinimumSize(QSize(25, 24));
        pushButton->setMaximumSize(QSize(25, 24));
        pushButton->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/image/\345\205\263\351\227\255.png);\n"
"border-style:none;\n"
"padding:5px;\n"
"\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	background-color: rgb(239, 41, 41);\n"
"}"));

        horizontalLayout->addWidget(pushButton);


        horizontalLayout_2->addLayout(horizontalLayout);


        gridLayout_3->addWidget(widget, 0, 0, 1, 1);

        widget_2 = new QWidget(Systemset);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        widget_2->setStyleSheet(QString::fromUtf8("background-color: rgb(90, 90, 90);"));
        gridLayout_2 = new QGridLayout(widget_2);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        gridLayout_2->setVerticalSpacing(12);
        gridLayout_2->setContentsMargins(3, 10, 3, 3);
        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setSpacing(0);
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        horizontalLayout_3->setSizeConstraint(QLayout::SetFixedSize);
        language_label = new QLabel(widget_2);
        language_label->setObjectName(QString::fromUtf8("language_label"));
        QSizePolicy sizePolicy1(QSizePolicy::Preferred, QSizePolicy::Fixed);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(language_label->sizePolicy().hasHeightForWidth());
        language_label->setSizePolicy(sizePolicy1);
        language_label->setMinimumSize(QSize(0, 25));
        language_label->setMaximumSize(QSize(16777215, 25));
        QFont font1;
        font1.setPointSize(11);
        language_label->setFont(font1);
        language_label->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_3->addWidget(language_label);

        comboBox = new QComboBox(widget_2);
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->setObjectName(QString::fromUtf8("comboBox"));
        comboBox->setMinimumSize(QSize(130, 26));
        comboBox->setMaximumSize(QSize(130, 25));
        comboBox->setStyleSheet(QString::fromUtf8("QComboBox {\n"
"    font: 75 9pt 'Arial';\n"
"    color: rgb(0, 0, 0);\n"
"    background-color: rgb(255, 255, 255);\n"
"    padding: 1px 15px 1px 10px;\n"
"    border: none;\n"
"    border-radius: 5px 5px 0px 0px;\n"
"}\n"
"QComboBox::drop-down {\n"
"	image: url(:/image/icons/combox_ico_drop.png);\n"
"    padding: 3px 6px 1px 3px;\n"
"    width: 18px;\n"
"}\n"
"QComboBox QAbstractItemView {\n"
"    outline: none;\n"
"    border: 1px solid white;\n"
"    color: rgb(0, 0, 0);\n"
"    background-color: rgb(255, 255, 255);\n"
"    selection-background-color: rgb(90, 90, 90);\n"
"}\n"
"QAbstractItemView::item {\n"
"    height: 30px;\n"
"}\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    color: #FFFFFF;\n"
"    background-color: rgb(90, 90, 90);\n"
"}\n"
"QComboBox:disabled {\n"
"    background-color: rgb(188, 188, 188);\n"
"    color: rgb(122, 122, 122);\n"
"}"));

        horizontalLayout_3->addWidget(comboBox);


        gridLayout_2->addLayout(horizontalLayout_3, 0, 0, 1, 1);

        verticalLayout = new QVBoxLayout();
        verticalLayout->setSpacing(12);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        horizontalLayout_12 = new QHBoxLayout();
        horizontalLayout_12->setObjectName(QString::fromUtf8("horizontalLayout_12"));
        label_9 = new QLabel(widget_2);
        label_9->setObjectName(QString::fromUtf8("label_9"));
        sizePolicy1.setHeightForWidth(label_9->sizePolicy().hasHeightForWidth());
        label_9->setSizePolicy(sizePolicy1);
        label_9->setMinimumSize(QSize(0, 25));
        label_9->setMaximumSize(QSize(16777215, 25));
        label_9->setFont(font1);
        label_9->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_12->addWidget(label_9);

        label_time = new QLabel(widget_2);
        label_time->setObjectName(QString::fromUtf8("label_time"));
        sizePolicy.setHeightForWidth(label_time->sizePolicy().hasHeightForWidth());
        label_time->setSizePolicy(sizePolicy);
        label_time->setFont(font1);
        label_time->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));
        label_time->setAlignment(Qt::AlignCenter);

        horizontalLayout_12->addWidget(label_time);


        verticalLayout->addLayout(horizontalLayout_12);

        gridLayout = new QGridLayout();
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        spinBox_year = new QSpinBox(widget_2);
        spinBox_year->setObjectName(QString::fromUtf8("spinBox_year"));
        spinBox_year->setMinimumSize(QSize(56, 30));
        spinBox_year->setMaximumSize(QSize(56, 30));
        spinBox_year->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));
        spinBox_year->setMinimum(2025);
        spinBox_year->setMaximum(3000);

        horizontalLayout_5->addWidget(spinBox_year);

        label_2 = new QLabel(widget_2);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        label_2->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_5->addWidget(label_2);


        gridLayout->addLayout(horizontalLayout_5, 0, 0, 1, 1);

        horizontalLayout_7 = new QHBoxLayout();
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        comboBox_month = new QComboBox(widget_2);
        comboBox_month->addItem(QString());
        comboBox_month->addItem(QString());
        comboBox_month->addItem(QString());
        comboBox_month->addItem(QString());
        comboBox_month->addItem(QString());
        comboBox_month->addItem(QString());
        comboBox_month->addItem(QString());
        comboBox_month->addItem(QString());
        comboBox_month->addItem(QString());
        comboBox_month->addItem(QString());
        comboBox_month->addItem(QString());
        comboBox_month->addItem(QString());
        comboBox_month->setObjectName(QString::fromUtf8("comboBox_month"));
        comboBox_month->setMinimumSize(QSize(56, 30));
        comboBox_month->setMaximumSize(QSize(56, 30));
        comboBox_month->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_7->addWidget(comboBox_month);

        label_5 = new QLabel(widget_2);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        label_5->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_7->addWidget(label_5);


        gridLayout->addLayout(horizontalLayout_7, 0, 1, 1, 1);

        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        comboBox_day = new QComboBox(widget_2);
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->addItem(QString());
        comboBox_day->setObjectName(QString::fromUtf8("comboBox_day"));
        comboBox_day->setMinimumSize(QSize(56, 30));
        comboBox_day->setMaximumSize(QSize(56, 30));
        comboBox_day->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_6->addWidget(comboBox_day);

        label_4 = new QLabel(widget_2);
        label_4->setObjectName(QString::fromUtf8("label_4"));
        label_4->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_6->addWidget(label_4);


        gridLayout->addLayout(horizontalLayout_6, 0, 2, 1, 1);

        horizontalLayout_8 = new QHBoxLayout();
        horizontalLayout_8->setObjectName(QString::fromUtf8("horizontalLayout_8"));
        spinBox_hour = new QSpinBox(widget_2);
        spinBox_hour->setObjectName(QString::fromUtf8("spinBox_hour"));
        spinBox_hour->setMinimumSize(QSize(56, 30));
        spinBox_hour->setMaximumSize(QSize(56, 30));
        spinBox_hour->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));
        spinBox_hour->setMaximum(23);

        horizontalLayout_8->addWidget(spinBox_hour);

        label_6 = new QLabel(widget_2);
        label_6->setObjectName(QString::fromUtf8("label_6"));
        label_6->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_8->addWidget(label_6);


        gridLayout->addLayout(horizontalLayout_8, 1, 0, 1, 1);

        horizontalLayout_9 = new QHBoxLayout();
        horizontalLayout_9->setObjectName(QString::fromUtf8("horizontalLayout_9"));
        spinBox_min = new QSpinBox(widget_2);
        spinBox_min->setObjectName(QString::fromUtf8("spinBox_min"));
        spinBox_min->setMinimumSize(QSize(56, 30));
        spinBox_min->setMaximumSize(QSize(56, 30));
        spinBox_min->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));
        spinBox_min->setMaximum(59);

        horizontalLayout_9->addWidget(spinBox_min);

        label_7 = new QLabel(widget_2);
        label_7->setObjectName(QString::fromUtf8("label_7"));
        label_7->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_9->addWidget(label_7);


        gridLayout->addLayout(horizontalLayout_9, 1, 1, 1, 1);

        horizontalLayout_10 = new QHBoxLayout();
        horizontalLayout_10->setObjectName(QString::fromUtf8("horizontalLayout_10"));
        spinBox_second = new QSpinBox(widget_2);
        spinBox_second->setObjectName(QString::fromUtf8("spinBox_second"));
        spinBox_second->setMinimumSize(QSize(56, 30));
        spinBox_second->setMaximumSize(QSize(56, 30));
        spinBox_second->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));
        spinBox_second->setMaximum(59);

        horizontalLayout_10->addWidget(spinBox_second);

        label_8 = new QLabel(widget_2);
        label_8->setObjectName(QString::fromUtf8("label_8"));
        label_8->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_10->addWidget(label_8);


        gridLayout->addLayout(horizontalLayout_10, 1, 2, 1, 1);


        verticalLayout->addLayout(gridLayout);

        horizontalLayout_11 = new QHBoxLayout();
        horizontalLayout_11->setObjectName(QString::fromUtf8("horizontalLayout_11"));
        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_11->addItem(horizontalSpacer_4);

        pushButton_2 = new QPushButton(widget_2);
        pushButton_2->setObjectName(QString::fromUtf8("pushButton_2"));
        pushButton_2->setMinimumSize(QSize(80, 25));
        pushButton_2->setMaximumSize(QSize(80, 25));
        pushButton_2->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_11->addWidget(pushButton_2);

        pushButton_OK = new QPushButton(widget_2);
        pushButton_OK->setObjectName(QString::fromUtf8("pushButton_OK"));
        pushButton_OK->setMinimumSize(QSize(80, 25));
        pushButton_OK->setMaximumSize(QSize(80, 25));
        pushButton_OK->setStyleSheet(QString::fromUtf8("\n"
"background-color: rgb(255, 255, 255);"));

        horizontalLayout_11->addWidget(pushButton_OK);


        verticalLayout->addLayout(horizontalLayout_11);


        gridLayout_2->addLayout(verticalLayout, 1, 0, 1, 1);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_2->addItem(verticalSpacer, 2, 0, 1, 1);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_2);

        label_3 = new QLabel(widget_2);
        label_3->setObjectName(QString::fromUtf8("label_3"));
        label_3->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_4->addWidget(label_3);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_3);


        gridLayout_2->addLayout(horizontalLayout_4, 3, 0, 1, 1);


        gridLayout_3->addWidget(widget_2, 1, 0, 1, 1);


        retranslateUi(Systemset);
        QObject::connect(pushButton, SIGNAL(clicked()), Systemset, SLOT(closewindow()));
        QObject::connect(comboBox, SIGNAL(activated(int)), Systemset, SLOT(combo_language(int)));
        QObject::connect(pushButton_OK, SIGNAL(clicked()), Systemset, SLOT(set_time()));
        QObject::connect(pushButton_2, SIGNAL(clicked()), Systemset, SLOT(changetime()));

        QMetaObject::connectSlotsByName(Systemset);
    } // setupUi

    void retranslateUi(QDialog *Systemset)
    {
        Systemset->setWindowTitle(QCoreApplication::translate("Systemset", "Dialog", nullptr));
        label->setText(QCoreApplication::translate("Systemset", "\347\263\273\347\273\237\350\256\276\347\275\256", nullptr));
        pushButton->setText(QString());
        language_label->setText(QCoreApplication::translate("Systemset", "\350\257\255\350\250\200\350\256\276\347\275\256\357\274\232", nullptr));
        comboBox->setItemText(0, QCoreApplication::translate("Systemset", "\344\270\255\346\226\207", nullptr));
        comboBox->setItemText(1, QCoreApplication::translate("Systemset", "English", nullptr));
        comboBox->setItemText(2, QCoreApplication::translate("Systemset", "\330\247\331\204\330\271\330\261\330\250\331\212\330\251", nullptr));
        comboBox->setItemText(3, QCoreApplication::translate("Systemset", "\347\271\201\344\275\223\344\270\255\346\226\207", nullptr));
        comboBox->setItemText(4, QCoreApplication::translate("Systemset", "Fran\303\247ais", nullptr));
        comboBox->setItemText(5, QCoreApplication::translate("Systemset", "Deutsch", nullptr));
        comboBox->setItemText(6, QCoreApplication::translate("Systemset", "Italiano", nullptr));
        comboBox->setItemText(7, QCoreApplication::translate("Systemset", "\346\227\245\346\234\254\350\252\236", nullptr));
        comboBox->setItemText(8, QCoreApplication::translate("Systemset", "\355\225\234\352\265\255\354\226\264", nullptr));
        comboBox->setItemText(9, QCoreApplication::translate("Systemset", "Portugu\303\252s", nullptr));
        comboBox->setItemText(10, QCoreApplication::translate("Systemset", "\320\240\321\203\321\201\321\201\320\272\320\270\320\271 \321\217\320\267\321\213\320\272", nullptr));
        comboBox->setItemText(11, QCoreApplication::translate("Systemset", "Espa\303\261ol", nullptr));
        comboBox->setItemText(12, QCoreApplication::translate("Systemset", "T\303\274rk\303\247e", nullptr));

        label_9->setText(QCoreApplication::translate("Systemset", "\346\227\266\351\227\264\350\256\276\347\275\256\357\274\232", nullptr));
        label_time->setText(QString());
        label_2->setText(QCoreApplication::translate("Systemset", "\345\271\264", nullptr));
        comboBox_month->setItemText(0, QCoreApplication::translate("Systemset", "01", nullptr));
        comboBox_month->setItemText(1, QCoreApplication::translate("Systemset", "02", nullptr));
        comboBox_month->setItemText(2, QCoreApplication::translate("Systemset", "03", nullptr));
        comboBox_month->setItemText(3, QCoreApplication::translate("Systemset", "04", nullptr));
        comboBox_month->setItemText(4, QCoreApplication::translate("Systemset", "05", nullptr));
        comboBox_month->setItemText(5, QCoreApplication::translate("Systemset", "06", nullptr));
        comboBox_month->setItemText(6, QCoreApplication::translate("Systemset", "07", nullptr));
        comboBox_month->setItemText(7, QCoreApplication::translate("Systemset", "08", nullptr));
        comboBox_month->setItemText(8, QCoreApplication::translate("Systemset", "09", nullptr));
        comboBox_month->setItemText(9, QCoreApplication::translate("Systemset", "10", nullptr));
        comboBox_month->setItemText(10, QCoreApplication::translate("Systemset", "11", nullptr));
        comboBox_month->setItemText(11, QCoreApplication::translate("Systemset", "12", nullptr));

        label_5->setText(QCoreApplication::translate("Systemset", "\346\234\210", nullptr));
        comboBox_day->setItemText(0, QCoreApplication::translate("Systemset", "01", nullptr));
        comboBox_day->setItemText(1, QCoreApplication::translate("Systemset", "02", nullptr));
        comboBox_day->setItemText(2, QCoreApplication::translate("Systemset", "03", nullptr));
        comboBox_day->setItemText(3, QCoreApplication::translate("Systemset", "04", nullptr));
        comboBox_day->setItemText(4, QCoreApplication::translate("Systemset", "05", nullptr));
        comboBox_day->setItemText(5, QCoreApplication::translate("Systemset", "06", nullptr));
        comboBox_day->setItemText(6, QCoreApplication::translate("Systemset", "07", nullptr));
        comboBox_day->setItemText(7, QCoreApplication::translate("Systemset", "08", nullptr));
        comboBox_day->setItemText(8, QCoreApplication::translate("Systemset", "09", nullptr));
        comboBox_day->setItemText(9, QCoreApplication::translate("Systemset", "10", nullptr));
        comboBox_day->setItemText(10, QCoreApplication::translate("Systemset", "11", nullptr));
        comboBox_day->setItemText(11, QCoreApplication::translate("Systemset", "12", nullptr));
        comboBox_day->setItemText(12, QCoreApplication::translate("Systemset", "13", nullptr));
        comboBox_day->setItemText(13, QCoreApplication::translate("Systemset", "14", nullptr));
        comboBox_day->setItemText(14, QCoreApplication::translate("Systemset", "15", nullptr));
        comboBox_day->setItemText(15, QCoreApplication::translate("Systemset", "16", nullptr));
        comboBox_day->setItemText(16, QCoreApplication::translate("Systemset", "17", nullptr));
        comboBox_day->setItemText(17, QCoreApplication::translate("Systemset", "18", nullptr));
        comboBox_day->setItemText(18, QCoreApplication::translate("Systemset", "19", nullptr));
        comboBox_day->setItemText(19, QCoreApplication::translate("Systemset", "20", nullptr));
        comboBox_day->setItemText(20, QCoreApplication::translate("Systemset", "21", nullptr));
        comboBox_day->setItemText(21, QCoreApplication::translate("Systemset", "22", nullptr));
        comboBox_day->setItemText(22, QCoreApplication::translate("Systemset", "23", nullptr));
        comboBox_day->setItemText(23, QCoreApplication::translate("Systemset", "24", nullptr));
        comboBox_day->setItemText(24, QCoreApplication::translate("Systemset", "25", nullptr));
        comboBox_day->setItemText(25, QCoreApplication::translate("Systemset", "26", nullptr));
        comboBox_day->setItemText(26, QCoreApplication::translate("Systemset", "27", nullptr));
        comboBox_day->setItemText(27, QCoreApplication::translate("Systemset", "28", nullptr));
        comboBox_day->setItemText(28, QCoreApplication::translate("Systemset", "29", nullptr));
        comboBox_day->setItemText(29, QCoreApplication::translate("Systemset", "30", nullptr));
        comboBox_day->setItemText(30, QCoreApplication::translate("Systemset", "31", nullptr));
        comboBox_day->setItemText(31, QString());

        label_4->setText(QCoreApplication::translate("Systemset", "\346\227\245", nullptr));
        label_6->setText(QCoreApplication::translate("Systemset", "\346\227\266", nullptr));
        label_7->setText(QCoreApplication::translate("Systemset", "\345\210\206", nullptr));
        label_8->setText(QCoreApplication::translate("Systemset", "\347\247\222", nullptr));
        pushButton_2->setText(QCoreApplication::translate("Systemset", "Change", nullptr));
        pushButton_OK->setText(QCoreApplication::translate("Systemset", "OK", nullptr));
        label_3->setText(QCoreApplication::translate("Systemset", "\347\211\210\346\234\254\344\277\241\346\201\257\357\274\232", nullptr));
    } // retranslateUi

};

namespace Ui {
    class Systemset: public Ui_Systemset {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SYSTEMSET_H
