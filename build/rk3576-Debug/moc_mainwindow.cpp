/****************************************************************************
** Meta object code from reading C++ file 'mainwindow.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.11)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../mainwindow.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'mainwindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.11. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_MainWindow_t {
    QByteArrayData data[62];
    char stringdata0[935];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MainWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MainWindow_t qt_meta_stringdata_MainWindow = {
    {
QT_MOC_LITERAL(0, 0, 10), // "MainWindow"
QT_MOC_LITERAL(1, 11, 20), // "onCloseButtonClicked"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 21), // "toggleVideoFullscreen"
QT_MOC_LITERAL(4, 55, 22), // "toggleVideoFullscreen2"
QT_MOC_LITERAL(5, 78, 22), // "toggleVideoFullscreen4"
QT_MOC_LITERAL(6, 101, 17), // "onMinimizeClicked"
QT_MOC_LITERAL(7, 119, 17), // "onMaximizeClicked"
QT_MOC_LITERAL(8, 137, 24), // "updateMaximizeButtonIcon"
QT_MOC_LITERAL(9, 162, 18), // "checkForNewDevices"
QT_MOC_LITERAL(10, 181, 12), // "select_video"
QT_MOC_LITERAL(11, 194, 5), // "index"
QT_MOC_LITERAL(12, 200, 13), // "select_format"
QT_MOC_LITERAL(13, 214, 17), // "select_resolution"
QT_MOC_LITERAL(14, 232, 12), // "select_audio"
QT_MOC_LITERAL(15, 245, 17), // "select_brightness"
QT_MOC_LITERAL(16, 263, 15), // "select_contrast"
QT_MOC_LITERAL(17, 279, 17), // "select_saturation"
QT_MOC_LITERAL(18, 297, 10), // "select_hue"
QT_MOC_LITERAL(19, 308, 16), // "select_sharpness"
QT_MOC_LITERAL(20, 325, 12), // "select_gamma"
QT_MOC_LITERAL(21, 338, 28), // "select_backlightcompensation"
QT_MOC_LITERAL(22, 367, 11), // "select_gain"
QT_MOC_LITERAL(23, 379, 19), // "select_whitebalance"
QT_MOC_LITERAL(24, 399, 24), // "select_whitebalance_auto"
QT_MOC_LITERAL(25, 424, 15), // "select_exposure"
QT_MOC_LITERAL(26, 440, 20), // "select_exposure_auto"
QT_MOC_LITERAL(27, 461, 12), // "select_focus"
QT_MOC_LITERAL(28, 474, 17), // "select_focus_auto"
QT_MOC_LITERAL(29, 492, 11), // "select_zoom"
QT_MOC_LITERAL(30, 504, 10), // "select_pan"
QT_MOC_LITERAL(31, 515, 11), // "select_tilt"
QT_MOC_LITERAL(32, 527, 12), // "set_default1"
QT_MOC_LITERAL(33, 540, 12), // "set_default2"
QT_MOC_LITERAL(34, 553, 12), // "VideoSetPage"
QT_MOC_LITERAL(35, 566, 14), // "PictureSetPage"
QT_MOC_LITERAL(36, 581, 17), // "CameraControlPage"
QT_MOC_LITERAL(37, 599, 11), // "TextSetPage"
QT_MOC_LITERAL(38, 611, 8), // "Signals1"
QT_MOC_LITERAL(39, 620, 8), // "Signals2"
QT_MOC_LITERAL(40, 629, 8), // "Signals3"
QT_MOC_LITERAL(41, 638, 8), // "Signals4"
QT_MOC_LITERAL(42, 647, 8), // "Signals5"
QT_MOC_LITERAL(43, 656, 8), // "Signals6"
QT_MOC_LITERAL(44, 665, 8), // "Signals7"
QT_MOC_LITERAL(45, 674, 8), // "videoset"
QT_MOC_LITERAL(46, 683, 10), // "filemanage"
QT_MOC_LITERAL(47, 694, 9), // "systemset"
QT_MOC_LITERAL(48, 704, 23), // "startRecordingByChannel"
QT_MOC_LITERAL(49, 728, 7), // "channel"
QT_MOC_LITERAL(50, 736, 22), // "stopRecordingByChannel"
QT_MOC_LITERAL(51, 759, 34), // "checkMousePositionAndUpdateBu..."
QT_MOC_LITERAL(52, 794, 18), // "hideControlButtons"
QT_MOC_LITERAL(53, 813, 12), // "rotateCamera"
QT_MOC_LITERAL(54, 826, 8), // "cameraId"
QT_MOC_LITERAL(55, 835, 9), // "takePhoto"
QT_MOC_LITERAL(56, 845, 13), // "isRecordAudio"
QT_MOC_LITERAL(57, 859, 7), // "reaudio"
QT_MOC_LITERAL(58, 867, 7), // "aboutus"
QT_MOC_LITERAL(59, 875, 24), // "toggleRecordingForCamera"
QT_MOC_LITERAL(60, 900, 13), // "TimeWatermark"
QT_MOC_LITERAL(61, 914, 20) // "onMouseDeviceChanged"

    },
    "MainWindow\0onCloseButtonClicked\0\0"
    "toggleVideoFullscreen\0toggleVideoFullscreen2\0"
    "toggleVideoFullscreen4\0onMinimizeClicked\0"
    "onMaximizeClicked\0updateMaximizeButtonIcon\0"
    "checkForNewDevices\0select_video\0index\0"
    "select_format\0select_resolution\0"
    "select_audio\0select_brightness\0"
    "select_contrast\0select_saturation\0"
    "select_hue\0select_sharpness\0select_gamma\0"
    "select_backlightcompensation\0select_gain\0"
    "select_whitebalance\0select_whitebalance_auto\0"
    "select_exposure\0select_exposure_auto\0"
    "select_focus\0select_focus_auto\0"
    "select_zoom\0select_pan\0select_tilt\0"
    "set_default1\0set_default2\0VideoSetPage\0"
    "PictureSetPage\0CameraControlPage\0"
    "TextSetPage\0Signals1\0Signals2\0Signals3\0"
    "Signals4\0Signals5\0Signals6\0Signals7\0"
    "videoset\0filemanage\0systemset\0"
    "startRecordingByChannel\0channel\0"
    "stopRecordingByChannel\0"
    "checkMousePositionAndUpdateButtons\0"
    "hideControlButtons\0rotateCamera\0"
    "cameraId\0takePhoto\0isRecordAudio\0"
    "reaudio\0aboutus\0toggleRecordingForCamera\0"
    "TimeWatermark\0onMouseDeviceChanged"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MainWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      56,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  294,    2, 0x08 /* Private */,
       3,    0,  295,    2, 0x08 /* Private */,
       4,    0,  296,    2, 0x08 /* Private */,
       5,    0,  297,    2, 0x08 /* Private */,
       6,    0,  298,    2, 0x08 /* Private */,
       7,    0,  299,    2, 0x08 /* Private */,
       8,    0,  300,    2, 0x08 /* Private */,
       9,    0,  301,    2, 0x08 /* Private */,
      10,    1,  302,    2, 0x08 /* Private */,
      12,    1,  305,    2, 0x08 /* Private */,
      13,    1,  308,    2, 0x08 /* Private */,
      14,    1,  311,    2, 0x08 /* Private */,
      15,    0,  314,    2, 0x08 /* Private */,
      16,    0,  315,    2, 0x08 /* Private */,
      17,    0,  316,    2, 0x08 /* Private */,
      18,    0,  317,    2, 0x08 /* Private */,
      19,    0,  318,    2, 0x08 /* Private */,
      20,    0,  319,    2, 0x08 /* Private */,
      21,    0,  320,    2, 0x08 /* Private */,
      22,    0,  321,    2, 0x08 /* Private */,
      23,    0,  322,    2, 0x08 /* Private */,
      24,    0,  323,    2, 0x08 /* Private */,
      25,    0,  324,    2, 0x08 /* Private */,
      26,    0,  325,    2, 0x08 /* Private */,
      27,    0,  326,    2, 0x08 /* Private */,
      28,    0,  327,    2, 0x08 /* Private */,
      29,    0,  328,    2, 0x08 /* Private */,
      30,    0,  329,    2, 0x08 /* Private */,
      31,    0,  330,    2, 0x08 /* Private */,
      32,    0,  331,    2, 0x08 /* Private */,
      33,    0,  332,    2, 0x08 /* Private */,
      34,    0,  333,    2, 0x08 /* Private */,
      35,    0,  334,    2, 0x08 /* Private */,
      36,    0,  335,    2, 0x08 /* Private */,
      37,    0,  336,    2, 0x08 /* Private */,
      38,    0,  337,    2, 0x08 /* Private */,
      39,    0,  338,    2, 0x08 /* Private */,
      40,    0,  339,    2, 0x08 /* Private */,
      41,    0,  340,    2, 0x08 /* Private */,
      42,    0,  341,    2, 0x08 /* Private */,
      43,    0,  342,    2, 0x08 /* Private */,
      44,    0,  343,    2, 0x08 /* Private */,
      45,    0,  344,    2, 0x08 /* Private */,
      46,    0,  345,    2, 0x08 /* Private */,
      47,    0,  346,    2, 0x08 /* Private */,
      48,    1,  347,    2, 0x08 /* Private */,
      50,    1,  350,    2, 0x08 /* Private */,
      51,    0,  353,    2, 0x08 /* Private */,
      52,    0,  354,    2, 0x08 /* Private */,
      53,    1,  355,    2, 0x08 /* Private */,
      55,    1,  358,    2, 0x08 /* Private */,
      56,    1,  361,    2, 0x08 /* Private */,
      58,    0,  364,    2, 0x08 /* Private */,
      59,    1,  365,    2, 0x08 /* Private */,
      60,    0,  368,    2, 0x08 /* Private */,
      61,    0,  369,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   11,
    QMetaType::Void, QMetaType::Int,   11,
    QMetaType::Void, QMetaType::Int,   11,
    QMetaType::Void, QMetaType::Int,   11,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   49,
    QMetaType::Void, QMetaType::Int,   49,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   54,
    QMetaType::Void, QMetaType::Int,   54,
    QMetaType::Void, QMetaType::Bool,   57,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   54,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MainWindow *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->onCloseButtonClicked(); break;
        case 1: _t->toggleVideoFullscreen(); break;
        case 2: _t->toggleVideoFullscreen2(); break;
        case 3: _t->toggleVideoFullscreen4(); break;
        case 4: _t->onMinimizeClicked(); break;
        case 5: _t->onMaximizeClicked(); break;
        case 6: _t->updateMaximizeButtonIcon(); break;
        case 7: _t->checkForNewDevices(); break;
        case 8: _t->select_video((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 9: _t->select_format((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 10: _t->select_resolution((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 11: _t->select_audio((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 12: _t->select_brightness(); break;
        case 13: _t->select_contrast(); break;
        case 14: _t->select_saturation(); break;
        case 15: _t->select_hue(); break;
        case 16: _t->select_sharpness(); break;
        case 17: _t->select_gamma(); break;
        case 18: _t->select_backlightcompensation(); break;
        case 19: _t->select_gain(); break;
        case 20: _t->select_whitebalance(); break;
        case 21: _t->select_whitebalance_auto(); break;
        case 22: _t->select_exposure(); break;
        case 23: _t->select_exposure_auto(); break;
        case 24: _t->select_focus(); break;
        case 25: _t->select_focus_auto(); break;
        case 26: _t->select_zoom(); break;
        case 27: _t->select_pan(); break;
        case 28: _t->select_tilt(); break;
        case 29: _t->set_default1(); break;
        case 30: _t->set_default2(); break;
        case 31: _t->VideoSetPage(); break;
        case 32: _t->PictureSetPage(); break;
        case 33: _t->CameraControlPage(); break;
        case 34: _t->TextSetPage(); break;
        case 35: _t->Signals1(); break;
        case 36: _t->Signals2(); break;
        case 37: _t->Signals3(); break;
        case 38: _t->Signals4(); break;
        case 39: _t->Signals5(); break;
        case 40: _t->Signals6(); break;
        case 41: _t->Signals7(); break;
        case 42: _t->videoset(); break;
        case 43: _t->filemanage(); break;
        case 44: _t->systemset(); break;
        case 45: _t->startRecordingByChannel((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 46: _t->stopRecordingByChannel((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 47: _t->checkMousePositionAndUpdateButtons(); break;
        case 48: _t->hideControlButtons(); break;
        case 49: _t->rotateCamera((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 50: _t->takePhoto((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 51: _t->isRecordAudio((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 52: _t->aboutus(); break;
        case 53: _t->toggleRecordingForCamera((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 54: _t->TimeWatermark(); break;
        case 55: _t->onMouseDeviceChanged(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject MainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_MainWindow.data,
    qt_meta_data_MainWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_MainWindow.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 56)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 56;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 56)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 56;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
