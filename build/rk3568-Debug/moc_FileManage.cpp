/****************************************************************************
** Meta object code from reading C++ file 'FileManage.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.10)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../FileManage.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'FileManage.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.10. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_FileManage_t {
    QByteArrayData data[20];
    char stringdata0[302];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_FileManage_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_FileManage_t qt_meta_stringdata_FileManage = {
    {
QT_MOC_LITERAL(0, 0, 10), // "FileManage"
QT_MOC_LITERAL(1, 11, 11), // "closewindow"
QT_MOC_LITERAL(2, 23, 0), // ""
QT_MOC_LITERAL(3, 24, 9), // "closedate"
QT_MOC_LITERAL(4, 34, 12), // "TreeViewShow"
QT_MOC_LITERAL(5, 47, 12), // "ListViewShow"
QT_MOC_LITERAL(6, 60, 10), // "SearchFile"
QT_MOC_LITERAL(7, 71, 10), // "SelectDate"
QT_MOC_LITERAL(8, 82, 22), // "onPictureButtonClicked"
QT_MOC_LITERAL(9, 105, 20), // "onVideoButtonClicked"
QT_MOC_LITERAL(10, 126, 23), // "onChannel1ButtonClicked"
QT_MOC_LITERAL(11, 150, 23), // "onChannel2ButtonClicked"
QT_MOC_LITERAL(12, 174, 23), // "onChannel3ButtonClicked"
QT_MOC_LITERAL(13, 198, 23), // "onChannel4ButtonClicked"
QT_MOC_LITERAL(14, 222, 13), // "SetSelectDate"
QT_MOC_LITERAL(15, 236, 5), // "fdate"
QT_MOC_LITERAL(16, 242, 19), // "onFileDoubleClicked"
QT_MOC_LITERAL(17, 262, 11), // "QModelIndex"
QT_MOC_LITERAL(18, 274, 5), // "index"
QT_MOC_LITERAL(19, 280, 21) // "onTFcardButtonClicked"

    },
    "FileManage\0closewindow\0\0closedate\0"
    "TreeViewShow\0ListViewShow\0SearchFile\0"
    "SelectDate\0onPictureButtonClicked\0"
    "onVideoButtonClicked\0onChannel1ButtonClicked\0"
    "onChannel2ButtonClicked\0onChannel3ButtonClicked\0"
    "onChannel4ButtonClicked\0SetSelectDate\0"
    "fdate\0onFileDoubleClicked\0QModelIndex\0"
    "index\0onTFcardButtonClicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_FileManage[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      15,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   89,    2, 0x08 /* Private */,
       3,    0,   90,    2, 0x08 /* Private */,
       4,    0,   91,    2, 0x0a /* Public */,
       5,    0,   92,    2, 0x0a /* Public */,
       6,    0,   93,    2, 0x0a /* Public */,
       7,    0,   94,    2, 0x0a /* Public */,
       8,    0,   95,    2, 0x0a /* Public */,
       9,    0,   96,    2, 0x0a /* Public */,
      10,    0,   97,    2, 0x0a /* Public */,
      11,    0,   98,    2, 0x0a /* Public */,
      12,    0,   99,    2, 0x0a /* Public */,
      13,    0,  100,    2, 0x0a /* Public */,
      14,    1,  101,    2, 0x0a /* Public */,
      16,    1,  104,    2, 0x0a /* Public */,
      19,    0,  107,    2, 0x0a /* Public */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QDate,   15,
    QMetaType::Void, 0x80000000 | 17,   18,
    QMetaType::Void,

       0        // eod
};

void FileManage::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<FileManage *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->closewindow(); break;
        case 1: _t->closedate(); break;
        case 2: _t->TreeViewShow(); break;
        case 3: _t->ListViewShow(); break;
        case 4: _t->SearchFile(); break;
        case 5: _t->SelectDate(); break;
        case 6: _t->onPictureButtonClicked(); break;
        case 7: _t->onVideoButtonClicked(); break;
        case 8: _t->onChannel1ButtonClicked(); break;
        case 9: _t->onChannel2ButtonClicked(); break;
        case 10: _t->onChannel3ButtonClicked(); break;
        case 11: _t->onChannel4ButtonClicked(); break;
        case 12: _t->SetSelectDate((*reinterpret_cast< const QDate(*)>(_a[1]))); break;
        case 13: _t->onFileDoubleClicked((*reinterpret_cast< const QModelIndex(*)>(_a[1]))); break;
        case 14: _t->onTFcardButtonClicked(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject FileManage::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_meta_stringdata_FileManage.data,
    qt_meta_data_FileManage,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *FileManage::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *FileManage::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_FileManage.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int FileManage::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 15)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 15;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
