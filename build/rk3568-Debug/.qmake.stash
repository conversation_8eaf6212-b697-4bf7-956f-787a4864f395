QMAKE_CXX.QT_COMPILER_STDCXX = 201703L
QMAKE_CXX.QMAKE_GCC_MAJOR_VERSION = 12
QMAKE_CXX.QMAKE_GCC_MINOR_VERSION = 3
QMAKE_CXX.QMAKE_GCC_PATCH_VERSION = 0
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_GCC_MAJOR_VERSION \
    QMAKE_GCC_MINOR_VERSION \
    QMAKE_GCC_PATCH_VERSION
QMAKE_CXX.INCDIRS = \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/include/c++/12.3.0 \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/include/c++/12.3.0/aarch64-buildroot-linux-gnu \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/include/c++/12.3.0/backward \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/gcc/aarch64-buildroot-linux-gnu/12.3.0/include \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/gcc/aarch64-buildroot-linux-gnu/12.3.0/include-fixed \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/include \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include
QMAKE_CXX.LIBDIRS = \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/gcc/aarch64-buildroot-linux-gnu/12.3.0 \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/lib64 \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/lib64 \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/lib64 \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/lib \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/lib \
    /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/lib
