/********************************************************************************
** Form generated from reading UI file 'recordingset.ui'
**
** Created by: Qt User Interface Compiler version 5.15.10
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_RECORDINGSET_H
#define UI_RECORDINGSET_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_RecordingSet
{
public:
    QGridLayout *gridLayout_3;
    QWidget *widget;
    QHBoxLayout *horizontalLayout_24;
    QHBoxLayout *horizontalLayout_23;
    QLabel *label_13;
    QSpacerItem *horizontalSpacer_15;
    QPushButton *recordingset_close;
    QWidget *widget_2;
    QGridLayout *gridLayout;
    QVBoxLayout *verticalLayout_4;
    QHBoxLayout *horizontalLayout_21;
    QHBoxLayout *horizontalLayout_2;
    QLabel *label_3;
    QTextEdit *textEdit_prefix;
    QSpacerItem *horizontalSpacer_13;
    QHBoxLayout *horizontalLayout_22;
    QGridLayout *gridLayout_2;
    QCheckBox *checkBox_segrecord;
    QRadioButton *radioButton_time;
    QRadioButton *radioButton_byte;
    QHBoxLayout *horizontalLayout_3;
    QSpinBox *spinBox_time;
    QLabel *label_4;
    QHBoxLayout *horizontalLayout_4;
    QSpinBox *spinBox_byte;
    QLabel *label_5;
    QSpacerItem *horizontalSpacer_14;
    QVBoxLayout *verticalLayout_2;
    QHBoxLayout *horizontalLayout_15;
    QLabel *label_6;
    QSpacerItem *horizontalSpacer_7;
    QHBoxLayout *horizontalLayout_13;
    QSpacerItem *horizontalSpacer_4;
    QHBoxLayout *horizontalLayout_11;
    QLabel *label_7;
    QLabel *label_8;
    QSpacerItem *horizontalSpacer_6;
    QHBoxLayout *horizontalLayout_14;
    QSpacerItem *horizontalSpacer_5;
    QHBoxLayout *horizontalLayout_12;
    QLabel *label_9;
    QRadioButton *radioButton_high;
    QRadioButton *radioButton_middle;
    QRadioButton *radioButton_low;
    QSpacerItem *horizontalSpacer_8;
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout_9;
    QLabel *label_10;
    QSpacerItem *horizontalSpacer;
    QHBoxLayout *horizontalLayout_10;
    QSpacerItem *horizontalSpacer_2;
    QHBoxLayout *horizontalLayout_8;
    QHBoxLayout *horizontalLayout_7;
    QCheckBox *checkBox_storagelimit;
    QHBoxLayout *horizontalLayout_5;
    QSpinBox *spinBox_storagelimit;
    QLabel *label_11;
    QHBoxLayout *horizontalLayout_6;
    QLabel *label_12;
    QLabel *label_freespace;
    QSpacerItem *horizontalSpacer_3;
    QHBoxLayout *horizontalLayout_16;
    QLabel *label_14;
    QSpacerItem *horizontalSpacer_9;
    QHBoxLayout *horizontalLayout_17;
    QLabel *label_15;
    QSpacerItem *horizontalSpacer_10;
    QHBoxLayout *horizontalLayout_20;
    QSpacerItem *horizontalSpacer_12;
    QHBoxLayout *horizontalLayout_19;
    QPushButton *pushButton;
    QPushButton *pushButton_2;
    QSpacerItem *horizontalSpacer_16;

    void setupUi(QDialog *RecordingSet)
    {
        if (RecordingSet->objectName().isEmpty())
            RecordingSet->setObjectName(QString::fromUtf8("RecordingSet"));
        RecordingSet->resize(484, 425);
        gridLayout_3 = new QGridLayout(RecordingSet);
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName(QString::fromUtf8("gridLayout_3"));
        gridLayout_3->setContentsMargins(0, 0, 0, 0);
        widget = new QWidget(RecordingSet);
        widget->setObjectName(QString::fromUtf8("widget"));
        widget->setMinimumSize(QSize(0, 24));
        widget->setMaximumSize(QSize(16777215, 24));
        widget->setStyleSheet(QString::fromUtf8("background-color: rgb(120, 120, 120);"));
        horizontalLayout_24 = new QHBoxLayout(widget);
        horizontalLayout_24->setSpacing(0);
        horizontalLayout_24->setObjectName(QString::fromUtf8("horizontalLayout_24"));
        horizontalLayout_24->setContentsMargins(0, 0, 0, 0);
        horizontalLayout_23 = new QHBoxLayout();
        horizontalLayout_23->setSpacing(0);
        horizontalLayout_23->setObjectName(QString::fromUtf8("horizontalLayout_23"));
        label_13 = new QLabel(widget);
        label_13->setObjectName(QString::fromUtf8("label_13"));
        QSizePolicy sizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(label_13->sizePolicy().hasHeightForWidth());
        label_13->setSizePolicy(sizePolicy);
        label_13->setMinimumSize(QSize(0, 24));
        label_13->setMaximumSize(QSize(16777215, 24));
        QFont font;
        font.setFamily(QString::fromUtf8("aakar"));
        font.setPointSize(12);
        label_13->setFont(font);
        label_13->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_23->addWidget(label_13);

        horizontalSpacer_15 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_23->addItem(horizontalSpacer_15);

        recordingset_close = new QPushButton(widget);
        recordingset_close->setObjectName(QString::fromUtf8("recordingset_close"));
        recordingset_close->setMinimumSize(QSize(25, 24));
        recordingset_close->setMaximumSize(QSize(25, 24));
        recordingset_close->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/image/\345\205\263\351\227\255.png);\n"
"border-style:none;\n"
"padding:5px;\n"
"\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	background-color: rgb(239, 41, 41);\n"
"}"));

        horizontalLayout_23->addWidget(recordingset_close);


        horizontalLayout_24->addLayout(horizontalLayout_23);


        gridLayout_3->addWidget(widget, 0, 0, 1, 1);

        widget_2 = new QWidget(RecordingSet);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        widget_2->setStyleSheet(QString::fromUtf8("background-color: rgb(90, 90, 90);"));
        gridLayout = new QGridLayout(widget_2);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        verticalLayout_4 = new QVBoxLayout();
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        horizontalLayout_21 = new QHBoxLayout();
        horizontalLayout_21->setObjectName(QString::fromUtf8("horizontalLayout_21"));
        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        label_3 = new QLabel(widget_2);
        label_3->setObjectName(QString::fromUtf8("label_3"));
        label_3->setMinimumSize(QSize(0, 27));
        label_3->setMaximumSize(QSize(16777215, 27));
        label_3->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_2->addWidget(label_3);

        textEdit_prefix = new QTextEdit(widget_2);
        textEdit_prefix->setObjectName(QString::fromUtf8("textEdit_prefix"));
        textEdit_prefix->setMinimumSize(QSize(0, 27));
        textEdit_prefix->setMaximumSize(QSize(16777215, 27));
        textEdit_prefix->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_2->addWidget(textEdit_prefix);


        horizontalLayout_21->addLayout(horizontalLayout_2);

        horizontalSpacer_13 = new QSpacerItem(40, 20, QSizePolicy::Preferred, QSizePolicy::Minimum);

        horizontalLayout_21->addItem(horizontalSpacer_13);


        verticalLayout_4->addLayout(horizontalLayout_21);

        horizontalLayout_22 = new QHBoxLayout();
        horizontalLayout_22->setObjectName(QString::fromUtf8("horizontalLayout_22"));
        gridLayout_2 = new QGridLayout();
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        checkBox_segrecord = new QCheckBox(widget_2);
        checkBox_segrecord->setObjectName(QString::fromUtf8("checkBox_segrecord"));
        checkBox_segrecord->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        gridLayout_2->addWidget(checkBox_segrecord, 0, 0, 1, 1);

        radioButton_time = new QRadioButton(widget_2);
        radioButton_time->setObjectName(QString::fromUtf8("radioButton_time"));
        radioButton_time->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        gridLayout_2->addWidget(radioButton_time, 0, 1, 1, 1);

        radioButton_byte = new QRadioButton(widget_2);
        radioButton_byte->setObjectName(QString::fromUtf8("radioButton_byte"));
        radioButton_byte->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        gridLayout_2->addWidget(radioButton_byte, 0, 2, 1, 1);

        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        spinBox_time = new QSpinBox(widget_2);
        spinBox_time->setObjectName(QString::fromUtf8("spinBox_time"));
        spinBox_time->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));
        spinBox_time->setMaximum(9999);

        horizontalLayout_3->addWidget(spinBox_time);

        label_4 = new QLabel(widget_2);
        label_4->setObjectName(QString::fromUtf8("label_4"));
        label_4->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_3->addWidget(label_4);


        gridLayout_2->addLayout(horizontalLayout_3, 1, 1, 1, 1);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        spinBox_byte = new QSpinBox(widget_2);
        spinBox_byte->setObjectName(QString::fromUtf8("spinBox_byte"));
        spinBox_byte->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));
        spinBox_byte->setMaximum(9999);

        horizontalLayout_4->addWidget(spinBox_byte);

        label_5 = new QLabel(widget_2);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        label_5->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_4->addWidget(label_5);


        gridLayout_2->addLayout(horizontalLayout_4, 1, 2, 1, 1);


        horizontalLayout_22->addLayout(gridLayout_2);

        horizontalSpacer_14 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_22->addItem(horizontalSpacer_14);


        verticalLayout_4->addLayout(horizontalLayout_22);

        verticalLayout_2 = new QVBoxLayout();
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        horizontalLayout_15 = new QHBoxLayout();
        horizontalLayout_15->setObjectName(QString::fromUtf8("horizontalLayout_15"));
        label_6 = new QLabel(widget_2);
        label_6->setObjectName(QString::fromUtf8("label_6"));
        label_6->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_15->addWidget(label_6);

        horizontalSpacer_7 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_15->addItem(horizontalSpacer_7);


        verticalLayout_2->addLayout(horizontalLayout_15);

        horizontalLayout_13 = new QHBoxLayout();
        horizontalLayout_13->setObjectName(QString::fromUtf8("horizontalLayout_13"));
        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_13->addItem(horizontalSpacer_4);

        horizontalLayout_11 = new QHBoxLayout();
        horizontalLayout_11->setObjectName(QString::fromUtf8("horizontalLayout_11"));
        label_7 = new QLabel(widget_2);
        label_7->setObjectName(QString::fromUtf8("label_7"));
        label_7->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_11->addWidget(label_7);

        label_8 = new QLabel(widget_2);
        label_8->setObjectName(QString::fromUtf8("label_8"));
        label_8->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_11->addWidget(label_8);


        horizontalLayout_13->addLayout(horizontalLayout_11);

        horizontalSpacer_6 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_13->addItem(horizontalSpacer_6);


        verticalLayout_2->addLayout(horizontalLayout_13);

        horizontalLayout_14 = new QHBoxLayout();
        horizontalLayout_14->setObjectName(QString::fromUtf8("horizontalLayout_14"));
        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_14->addItem(horizontalSpacer_5);

        horizontalLayout_12 = new QHBoxLayout();
        horizontalLayout_12->setObjectName(QString::fromUtf8("horizontalLayout_12"));
        label_9 = new QLabel(widget_2);
        label_9->setObjectName(QString::fromUtf8("label_9"));
        label_9->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_12->addWidget(label_9);

        radioButton_high = new QRadioButton(widget_2);
        radioButton_high->setObjectName(QString::fromUtf8("radioButton_high"));
        radioButton_high->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_12->addWidget(radioButton_high);

        radioButton_middle = new QRadioButton(widget_2);
        radioButton_middle->setObjectName(QString::fromUtf8("radioButton_middle"));
        radioButton_middle->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_12->addWidget(radioButton_middle);

        radioButton_low = new QRadioButton(widget_2);
        radioButton_low->setObjectName(QString::fromUtf8("radioButton_low"));
        radioButton_low->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_12->addWidget(radioButton_low);


        horizontalLayout_14->addLayout(horizontalLayout_12);

        horizontalSpacer_8 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_14->addItem(horizontalSpacer_8);


        verticalLayout_2->addLayout(horizontalLayout_14);


        verticalLayout_4->addLayout(verticalLayout_2);

        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        horizontalLayout_9 = new QHBoxLayout();
        horizontalLayout_9->setObjectName(QString::fromUtf8("horizontalLayout_9"));
        label_10 = new QLabel(widget_2);
        label_10->setObjectName(QString::fromUtf8("label_10"));
        label_10->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_9->addWidget(label_10);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_9->addItem(horizontalSpacer);


        verticalLayout->addLayout(horizontalLayout_9);

        horizontalLayout_10 = new QHBoxLayout();
        horizontalLayout_10->setObjectName(QString::fromUtf8("horizontalLayout_10"));
        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_10->addItem(horizontalSpacer_2);

        horizontalLayout_8 = new QHBoxLayout();
        horizontalLayout_8->setObjectName(QString::fromUtf8("horizontalLayout_8"));
        horizontalLayout_7 = new QHBoxLayout();
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        checkBox_storagelimit = new QCheckBox(widget_2);
        checkBox_storagelimit->setObjectName(QString::fromUtf8("checkBox_storagelimit"));
        checkBox_storagelimit->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_7->addWidget(checkBox_storagelimit);

        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        spinBox_storagelimit = new QSpinBox(widget_2);
        spinBox_storagelimit->setObjectName(QString::fromUtf8("spinBox_storagelimit"));
        spinBox_storagelimit->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));
        spinBox_storagelimit->setMaximum(9999);

        horizontalLayout_5->addWidget(spinBox_storagelimit);

        label_11 = new QLabel(widget_2);
        label_11->setObjectName(QString::fromUtf8("label_11"));
        label_11->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_5->addWidget(label_11);


        horizontalLayout_7->addLayout(horizontalLayout_5);


        horizontalLayout_8->addLayout(horizontalLayout_7);

        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        label_12 = new QLabel(widget_2);
        label_12->setObjectName(QString::fromUtf8("label_12"));
        label_12->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_6->addWidget(label_12);

        label_freespace = new QLabel(widget_2);
        label_freespace->setObjectName(QString::fromUtf8("label_freespace"));
        label_freespace->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_6->addWidget(label_freespace);


        horizontalLayout_8->addLayout(horizontalLayout_6);


        horizontalLayout_10->addLayout(horizontalLayout_8);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_10->addItem(horizontalSpacer_3);


        verticalLayout->addLayout(horizontalLayout_10);


        verticalLayout_4->addLayout(verticalLayout);

        horizontalLayout_16 = new QHBoxLayout();
        horizontalLayout_16->setObjectName(QString::fromUtf8("horizontalLayout_16"));
        label_14 = new QLabel(widget_2);
        label_14->setObjectName(QString::fromUtf8("label_14"));

        horizontalLayout_16->addWidget(label_14);

        horizontalSpacer_9 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_16->addItem(horizontalSpacer_9);


        verticalLayout_4->addLayout(horizontalLayout_16);

        horizontalLayout_17 = new QHBoxLayout();
        horizontalLayout_17->setObjectName(QString::fromUtf8("horizontalLayout_17"));
        label_15 = new QLabel(widget_2);
        label_15->setObjectName(QString::fromUtf8("label_15"));

        horizontalLayout_17->addWidget(label_15);

        horizontalSpacer_10 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_17->addItem(horizontalSpacer_10);


        verticalLayout_4->addLayout(horizontalLayout_17);

        horizontalLayout_20 = new QHBoxLayout();
        horizontalLayout_20->setObjectName(QString::fromUtf8("horizontalLayout_20"));
        horizontalSpacer_12 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_20->addItem(horizontalSpacer_12);

        horizontalLayout_19 = new QHBoxLayout();
        horizontalLayout_19->setObjectName(QString::fromUtf8("horizontalLayout_19"));
        pushButton = new QPushButton(widget_2);
        pushButton->setObjectName(QString::fromUtf8("pushButton"));
        pushButton->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_19->addWidget(pushButton);

        pushButton_2 = new QPushButton(widget_2);
        pushButton_2->setObjectName(QString::fromUtf8("pushButton_2"));
        pushButton_2->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_19->addWidget(pushButton_2);


        horizontalLayout_20->addLayout(horizontalLayout_19);

        horizontalSpacer_16 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_20->addItem(horizontalSpacer_16);


        verticalLayout_4->addLayout(horizontalLayout_20);


        gridLayout->addLayout(verticalLayout_4, 0, 0, 1, 1);


        gridLayout_3->addWidget(widget_2, 1, 0, 1, 1);


        retranslateUi(RecordingSet);
        QObject::connect(recordingset_close, SIGNAL(clicked()), RecordingSet, SLOT(closewindow()));
        QObject::connect(pushButton, SIGNAL(clicked()), RecordingSet, SLOT(recordingSettings()));
        QObject::connect(pushButton_2, SIGNAL(clicked()), RecordingSet, SLOT(closewindow()));
        QObject::connect(checkBox_segrecord, SIGNAL(clicked()), RecordingSet, SLOT(Sectional_recording()));
        QObject::connect(radioButton_time, SIGNAL(clicked()), RecordingSet, SLOT(time_segment()));
        QObject::connect(radioButton_byte, SIGNAL(clicked()), RecordingSet, SLOT(byte_segment()));
        QObject::connect(checkBox_storagelimit, SIGNAL(clicked()), RecordingSet, SLOT(video_storage()));

        QMetaObject::connectSlotsByName(RecordingSet);
    } // setupUi

    void retranslateUi(QDialog *RecordingSet)
    {
        RecordingSet->setWindowTitle(QCoreApplication::translate("RecordingSet", "Dialog", nullptr));
        label_13->setText(QCoreApplication::translate("RecordingSet", "\345\275\225\345\203\217\350\256\276\347\275\256", nullptr));
        recordingset_close->setText(QString());
        label_3->setText(QCoreApplication::translate("RecordingSet", "\346\226\207\344\273\266\345\211\215\347\274\200\345\220\215\357\274\232", nullptr));
        checkBox_segrecord->setText(QCoreApplication::translate("RecordingSet", "\345\210\206\346\256\265\345\275\225\345\203\217", nullptr));
        radioButton_time->setText(QCoreApplication::translate("RecordingSet", "\346\214\211\346\227\266\351\227\264\345\215\225\344\275\215\344\270\255\346\226\255", nullptr));
        radioButton_byte->setText(QCoreApplication::translate("RecordingSet", "\346\214\211\345\255\227\350\212\202\345\215\225\344\275\215\344\270\255\346\226\255", nullptr));
        label_4->setText(QCoreApplication::translate("RecordingSet", "\345\210\206", nullptr));
        label_5->setText(QCoreApplication::translate("RecordingSet", "\345\205\206", nullptr));
        label_6->setText(QCoreApplication::translate("RecordingSet", "\350\276\223\345\207\272\350\256\276\347\275\256", nullptr));
        label_7->setText(QCoreApplication::translate("RecordingSet", "\346\226\207\344\273\266\346\240\274\345\274\217\357\274\232MPEG MP4", nullptr));
        label_8->setText(QCoreApplication::translate("RecordingSet", "\350\247\206\351\242\221\347\274\226\347\240\201\357\274\232H264", nullptr));
        label_9->setText(QCoreApplication::translate("RecordingSet", "\345\275\225\345\203\217\347\224\273\350\264\250\357\274\232", nullptr));
        radioButton_high->setText(QCoreApplication::translate("RecordingSet", "\351\253\230\350\264\250\351\207\217", nullptr));
        radioButton_middle->setText(QCoreApplication::translate("RecordingSet", "\344\270\255\347\224\273\350\264\250", nullptr));
        radioButton_low->setText(QCoreApplication::translate("RecordingSet", "\344\275\216\347\224\273\350\264\250", nullptr));
        label_10->setText(QCoreApplication::translate("RecordingSet", "\347\243\201\347\233\230\350\256\276\347\275\256", nullptr));
        checkBox_storagelimit->setText(QCoreApplication::translate("RecordingSet", "\350\256\276\347\275\256\345\275\225\345\203\217\345\255\230\345\202\250\351\231\220\345\210\266", nullptr));
        label_11->setText(QCoreApplication::translate("RecordingSet", "GB", nullptr));
        label_12->setText(QCoreApplication::translate("RecordingSet", "\345\217\257\347\224\250\347\251\272\351\227\264\357\274\232", nullptr));
        label_freespace->setText(QString());
        label_14->setText(QCoreApplication::translate("RecordingSet", "\347\247\273\345\212\250\344\276\246\346\265\213", nullptr));
        label_15->setText(QCoreApplication::translate("RecordingSet", "\345\256\232\346\227\266\345\275\225\345\203\217", nullptr));
        pushButton->setText(QCoreApplication::translate("RecordingSet", "\347\241\256\345\256\232", nullptr));
        pushButton_2->setText(QCoreApplication::translate("RecordingSet", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class RecordingSet: public Ui_RecordingSet {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_RECORDINGSET_H
