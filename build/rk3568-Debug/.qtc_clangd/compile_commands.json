[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomDialog.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomFileSystemModel.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomFileSystemModel.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomFilterProxyModel.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomFilterProxyModel.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomMessageBox.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomMessageBox.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomProgressDialog.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomProgressDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/FileManage.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/FileManage.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/aboutus.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/aboutus.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/camera_param.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/camera_param.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/camerastream.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/camerastream.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/main.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/mainwindow.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/recordingset.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/recordingset.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/systemmanager.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/systemmanager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/systemset.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/systemset.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/udevmonitor.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/udevmonitor.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-7.26/RWS/virtualkeyboard.cpp"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/virtualkeyboard.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomDialog.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomFileSystemModel.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomFileSystemModel.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomFilterProxyModel.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomFilterProxyModel.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomMessageBox.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomMessageBox.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomProgressDialog.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/CustomProgressDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/FileManage.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/FileManage.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/SysComboBoxStyle.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/SysComboBoxStyle.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/aboutus.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/aboutus.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/camera_param.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/camera_param.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/camerastream.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/camerastream.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/mainwindow.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/recordingset.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/recordingset.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/systemmanager.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/systemmanager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/systemset.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/systemset.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/udevmonitor.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/udevmonitor.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/virtualkeyboard.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/virtualkeyboard.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/ui_systemset.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/ui_systemset.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/ui_aboutus.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/ui_aboutus.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/ui_filemanage.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/ui_filemanage.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/ui_mainwindow.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/ui_mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/gstreamer1-1.22.9/build", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/lib/glib-2.0/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/include/glib-2.0", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtWidgets", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtGui", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtConcurrent", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/include/QtCore", "-I/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/build/qt5base-e44097b63d17ba3178a637df7fac51ddc51cb48b/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/ui_recordingset.h"], "directory": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-7.26/RWS/build/rk3568-Debug/ui_recordingset.h"}]