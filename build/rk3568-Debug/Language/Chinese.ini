[General]
# 中文翻译文件
[mainwindow]
close_device=关闭设备
memory_usage_label=内存：
program_runtime_label=程序运行：
# 对话框标题
warning_title=警告
error_title=错误
confirm_title=确认
info_title=信息
recording_limit_title=录像限制

# 警告和错误消息
recording_warning=有设备正在录像，禁止关闭！
bandwidth_error=通道%1的摄像头打开失败：带宽不足或资源分配失败，请检查设备连接或尝试其他摄像头。
runtime_error=通道%1的摄像头运行时出现错误：可能是摄像头掉了或带宽问题，请检查设备连接或尝试其他摄像头。
camera_duplicate_warning=摄像头 %1 已在通道 %2 中打开，不可重复打开！

# 录像限制消息
recording_limit_message=录像限制：无法启动 %1 录像。\n\n当前系统支持的录像组合：\n• 4个 720P30 录像\n• 2个 1080P30 录像\n• 1个 1080P60 录像\n• 1个 2592×1944@25fps 录像\n\n当前负载：%2/4，新增负载：%3

# 按钮文本
ok_button=确定
yes_button=是
no_button=否
cancel_button=取消

# 其他标签
resolution_label=分辨率
encoding_format=编码格式: H264
audio=录音 
motion_detection=移动侦测 
microphone=麦克风
on=开启
off=关闭

[filemanage]
#右键对话框
open_action=打开
delete_action=删除
properties_action=属性
transfer_action=传送
# 文件管理对话框
confirm_title=确认
delete_failed_title=删除失败
confirm_delete=确定要删除此文件吗？
file_open_error=无法打开文件: %1

successful=成功
error=错误
tf_card_not_detected=未检测到TF卡，没有目标路径
transfer_confirm_overwrite=目标文件已存在，是否覆盖？
transfer_progress_title=传输进度
transfer_progress_text=正在传输文件...
transfer_success=文件传输成功！
transfer_error=文件传输失败！
inPreparation=准备中...
totalSize=总大小: %1
hasBeenTransmitted=已传输: %1 / %2 (%3%)
transfer_detail=传输用时: %1\n平均速度: %2\n文件大小: %3
speed=速度: %1/s
remaining_time=剩余时间: %1


player_error=无法调用播放器打开文件
image_load_error=无法加载图片
cannot_Open_File=无法打开文件


file_properties_title=文件属性
file_name=文件名：
path=路径：
file_size_label=文件大小：
cannot_calculate=无法计算
modified_date_label=修改日期：
file_type_label=文件类型：
delete_folder_recording_error=无法删除文件夹，因为其中包含正在录像的文件。\n请先停止相关录像后再尝试删除。
delete_file_recording_error=无法删除文件，该文件正在录像中。\n请先停止录像后再尝试删除。
rename_exists_error=文件或文件夹已存在
rename_folder_error=无法重命名文件夹
rename_file_error=无法重命名文件
file_not_readable_error=文件不可读: %1
player_error=无法调用播放器打开文件: %1
image_load_error=无法加载图片: %1
[filesystemmodel]
name=名称
modifydate=修改日期
type=类型
size=大小

[recordingset]
choose_storage_directory=选择存储目录
