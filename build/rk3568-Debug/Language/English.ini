[General]
# English translation file
[mainwindow]
close_device=Close Device
memory_usage_label=Memory:
program_runtime_label=Program Runtime:
# Dialog titles
warning_title=Warning
error_title=Error
confirm_title=Confirm
info_title=Information
recording_limit_title=Recording Limit

# Warning and error messages
recording_warning=Device is recording cannot close!
bandwidth_error=Camera on channel %1 failed to open: insufficient bandwidth or resource allocation failure please check device connection or try another camera.
runtime_error=Camera on channel %1 encountered runtime error: camera may be disconnected or bandwidth issue please check device connection or try another camera.
camera_duplicate_warning=Camera %1 is already open on channel %2 cannot open duplicate!

# Recording limit messages
recording_limit_message=Recording Limit: Cannot start %1 recording.\n\nSupported recording combinations:\n• 4x 720P30 recordings\n• 2x 1080P30 recordings\n• 1x 1080P60 recording\n• 1x 2592×1944@25fps recording\n\nCurrent load: %2/4 Additional load: %3

# Button text
ok_button=OK
yes_button=Yes
no_button=No
cancel_button=Cancel

# Other labels
resolution_label=Resolution
encoding_format=Encoding Format: H264
audio=Audio Recording
motion_detection=Motion Detection
microphone=Microphone
on=On
off=Off

[filemanage]
#Right-click dialog
open_action=Open
delete_action=Delete
properties_action=Properties
transfer_action=Transfer
# File management dialog
confirm_title=Confirm
delete_failed_title=Delete Failed
confirm_delete=Are you sure you want to delete this file?
file_open_error=Cannot open file: %1

successful=Successful
error=Error
tf_card_not_detected=TF card not detected  no target path
transfer_confirm_overwrite=Target file already exists  overwrite?
transfer_progress_title=Transfer Progress
transfer_progress_text=Transferring file...
transfer_success=File transfer successful!
transfer_error=File transfer failed!
inPreparation=Preparing...
totalSize=Total size: %1
hasBeenTransmitted=Transmitted: %1 / %2 (%3%)
transfer_detail=Transfer time: %1\nAverage speed: %2\nFile size: %3
speed=Speed: %1/s
remaining_time=Remaining time: %1

player_error=Cannot invoke player to open file
image_load_error=Cannot load image
cannot_Open_File=Cannot open file

file_properties_title=File Properties
file_name=File Name:
path=Path:
file_size_label=File Size:
cannot_calculate=Cannot calculate
modified_date_label=Modified Date:
file_type_label=File Type:
delete_folder_recording_error=Cannot delete folder because it contains files being recorded.\nPlease stop related recordings first and try again.
delete_file_recording_error=Cannot delete file the file is being recorded.\nPlease stop recording first and try again.
rename_exists_error=File or folder already exists
rename_folder_error=Cannot rename folder
rename_file_error=Cannot rename file
file_not_readable_error=File not readable: %1
player_error=Cannot invoke player to open file: %1
image_load_error=Cannot load image: %1
[filesystemmodel]
name=Name
modifydate=Modified Date
type=Type
size=Size

[recordingset]
choose_storage_directory=Choose Storage Directory
