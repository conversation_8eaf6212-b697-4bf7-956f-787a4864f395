/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.15.10
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QSlider>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QGridLayout *gridLayout_4;
    QWidget *widget;
    QHBoxLayout *horizontalLayout_9;
    QSpacerItem *horizontalSpacer;
    QLabel *label_mainname;
    QSpacerItem *horizontalSpacer_2;
    QPushButton *pushButton;
    QSpacerItem *horizontalSpacer_3;
    QHBoxLayout *horizontalLayout_5;
    QPushButton *recordingSet;
    QPushButton *fileManage;
    QPushButton *systemSet;
    QVBoxLayout *verticalLayout_12;
    QHBoxLayout *horizontalLayout_6;
    QSpacerItem *horizontalSpacer_4;
    QSpacerItem *horizontalSpacer_7;
    QPushButton *pushButton_fullscreen;
    QPushButton *pushButton_min;
    QPushButton *pushButton_max;
    QPushButton *closeButton;
    QHBoxLayout *horizontalLayout_7;
    QLabel *label_cpu;
    QLabel *label_gpu;
    QHBoxLayout *horizontalLayout_8;
    QLabel *label_memory;
    QLabel *label_time;
    QWidget *widget_2;
    QGridLayout *gridLayout_3;
    QVBoxLayout *verticalLayout_11;
    QVBoxLayout *verticalLayout_2;
    QHBoxLayout *horizontalLayout;
    QPushButton *videoSet;
    QPushButton *pictureSet;
    QPushButton *cameraControl;
    QPushButton *textSet;
    QVBoxLayout *verticalLayout;
    QLabel *label_xinhaoy;
    QHBoxLayout *horizontalLayout_2;
    QPushButton *p1;
    QPushButton *p2;
    QPushButton *p3;
    QPushButton *p4;
    QStackedWidget *stackedWidget;
    QWidget *page_videoset;
    QVBoxLayout *verticalLayout_9;
    QVBoxLayout *verticalLayout_8;
    QVBoxLayout *verticalLayout_3;
    QLabel *label_cameradev;
    QComboBox *comboBox_cameradev;
    QVBoxLayout *verticalLayout_4;
    QLabel *label_videoformat;
    QComboBox *comboBox_videoformat;
    QVBoxLayout *verticalLayout_5;
    QLabel *label_fbl;
    QComboBox *comboBox_fbl;
    QVBoxLayout *verticalLayout_7;
    QVBoxLayout *verticalLayout_6;
    QLabel *label_audiodev;
    QComboBox *comboBox_audiodev;
    QCheckBox *checkBox_audio;
    QWidget *page_pictureset;
    QGridLayout *gridLayout_6;
    QScrollArea *scrollArea;
    QWidget *scrollAreaWidgetContents;
    QGridLayout *gridLayout_5;
    QVBoxLayout *verticalLayout_22;
    QVBoxLayout *verticalLayout_13;
    QLabel *label_brightness;
    QHBoxLayout *horizontalLayout_10;
    QSlider *horizontalSlider_brightness;
    QSpinBox *spinBox_brightness;
    QVBoxLayout *verticalLayout_14;
    QLabel *label_contrast;
    QHBoxLayout *horizontalLayout_11;
    QSlider *horizontalSlider_contrast;
    QSpinBox *spinBox_contrast;
    QVBoxLayout *verticalLayout_15;
    QLabel *label_saturation;
    QHBoxLayout *horizontalLayout_12;
    QSlider *horizontalSlider_saturation;
    QSpinBox *spinBox_saturation;
    QVBoxLayout *verticalLayout_16;
    QLabel *label_hue;
    QHBoxLayout *horizontalLayout_13;
    QSlider *horizontalSlider_hue;
    QSpinBox *spinBox_hue;
    QVBoxLayout *verticalLayout_17;
    QLabel *label_sharpness;
    QHBoxLayout *horizontalLayout_14;
    QSlider *horizontalSlider_sharpness;
    QSpinBox *spinBox_sharpness;
    QVBoxLayout *verticalLayout_18;
    QLabel *label_gamma;
    QHBoxLayout *horizontalLayout_15;
    QSlider *horizontalSlider_gamma;
    QSpinBox *spinBox_gamma;
    QVBoxLayout *verticalLayout_19;
    QLabel *label_backlightcompensation;
    QHBoxLayout *horizontalLayout_16;
    QSlider *horizontalSlider_backlightcompensation;
    QSpinBox *spinBox_backlightcompensation;
    QVBoxLayout *verticalLayout_20;
    QLabel *label_gain;
    QHBoxLayout *horizontalLayout_17;
    QSlider *horizontalSlider_gain;
    QSpinBox *spinBox_gain;
    QVBoxLayout *verticalLayout_21;
    QHBoxLayout *horizontalLayout_20;
    QLabel *label_whitebalance;
    QCheckBox *checkBox_whitebalance;
    QHBoxLayout *horizontalLayout_18;
    QSlider *horizontalSlider_whitebalance;
    QSpinBox *spinBox_whitebalance;
    QHBoxLayout *horizontalLayout_19;
    QPushButton *pushButton_default1;
    QSpacerItem *horizontalSpacer_5;
    QWidget *page_cameracontrol;
    QVBoxLayout *verticalLayout_28;
    QVBoxLayout *verticalLayout_23;
    QHBoxLayout *horizontalLayout_21;
    QLabel *label_exposure;
    QCheckBox *checkBox_exposure;
    QHBoxLayout *horizontalLayout_22;
    QSlider *horizontalSlider_exposure;
    QSpinBox *spinBox_exposure;
    QVBoxLayout *verticalLayout_27;
    QHBoxLayout *horizontalLayout_23;
    QLabel *label_focus;
    QCheckBox *checkBox_focus;
    QHBoxLayout *horizontalLayout_24;
    QSlider *horizontalSlider_focus;
    QSpinBox *spinBox_focus;
    QVBoxLayout *verticalLayout_24;
    QLabel *label_zoom;
    QHBoxLayout *horizontalLayout_25;
    QSlider *horizontalSlider_zoom;
    QSpinBox *spinBox_zoom;
    QVBoxLayout *verticalLayout_25;
    QLabel *label_pan;
    QHBoxLayout *horizontalLayout_26;
    QSlider *horizontalSlider_pan;
    QSpinBox *spinBox_pan;
    QVBoxLayout *verticalLayout_26;
    QLabel *label_tilt;
    QHBoxLayout *horizontalLayout_27;
    QSlider *horizontalSlider_tilt;
    QSpinBox *spinBox_tilt;
    QHBoxLayout *horizontalLayout_28;
    QPushButton *pushButton_default2;
    QSpacerItem *horizontalSpacer_6;
    QWidget *page;
    QWidget *layoutWidget;
    QHBoxLayout *horizontalLayout_31;
    QLabel *label_5;
    QPushButton *timewmark;
    QWidget *layoutWidget1;
    QHBoxLayout *horizontalLayout_32;
    QLabel *label_6;
    QPushButton *pushButton_6;
    QWidget *layoutWidget2;
    QVBoxLayout *verticalLayout_29;
    QLabel *label_7;
    QLineEdit *lineEdit;
    QLabel *label_8;
    QPushButton *pushButton_7;
    QSpacerItem *verticalSpacer;
    QVBoxLayout *verticalLayout_10;
    QHBoxLayout *horizontalLayout_3;
    QPushButton *p_1;
    QPushButton *p_2;
    QPushButton *p_3;
    QPushButton *p_4;
    QHBoxLayout *horizontalLayout_4;
    QPushButton *p1_2;
    QPushButton *p3_4;
    QPushButton *p1234;
    QSpacerItem *verticalSpacer_2;
    QWidget *video;
    QGridLayout *gridLayout;
    QStackedWidget *stackedWidget_2;
    QWidget *page_2;
    QGridLayout *gridLayout_2;
    QWidget *v1;
    QWidget *page_3;
    QGridLayout *gridLayout_7;
    QWidget *v2;
    QWidget *page_4;
    QGridLayout *gridLayout_8;
    QWidget *v3;
    QWidget *page_5;
    QGridLayout *gridLayout_9;
    QWidget *v4;
    QWidget *page_6;
    QGridLayout *gridLayout_10;
    QWidget *v5_2;
    QWidget *v5_1;
    QWidget *page_7;
    QGridLayout *gridLayout_11;
    QWidget *v6_1;
    QWidget *v6_2;
    QWidget *page_8;
    QGridLayout *gridLayout_12;
    QWidget *v7_1;
    QWidget *v7_2;
    QWidget *v7_3;
    QWidget *v7_4;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QString::fromUtf8("MainWindow"));
        MainWindow->resize(1265, 925);
        QSizePolicy sizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(MainWindow->sizePolicy().hasHeightForWidth());
        MainWindow->setSizePolicy(sizePolicy);
        MainWindow->setStyleSheet(QString::fromUtf8("background-color: rgb(239, 41, 41,0);"));
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        centralwidget->setStyleSheet(QString::fromUtf8("background-color: rgb(239, 41, 41,0);"));
        gridLayout_4 = new QGridLayout(centralwidget);
        gridLayout_4->setSpacing(0);
        gridLayout_4->setObjectName(QString::fromUtf8("gridLayout_4"));
        gridLayout_4->setContentsMargins(0, 0, 0, 0);
        widget = new QWidget(centralwidget);
        widget->setObjectName(QString::fromUtf8("widget"));
        QSizePolicy sizePolicy1(QSizePolicy::Preferred, QSizePolicy::Fixed);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(widget->sizePolicy().hasHeightForWidth());
        widget->setSizePolicy(sizePolicy1);
        widget->setStyleSheet(QString::fromUtf8("background-color: rgb(120, 120, 120);"));
        horizontalLayout_9 = new QHBoxLayout(widget);
        horizontalLayout_9->setSpacing(0);
        horizontalLayout_9->setObjectName(QString::fromUtf8("horizontalLayout_9"));
        horizontalLayout_9->setContentsMargins(0, 0, 0, 0);
        horizontalSpacer = new QSpacerItem(10, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_9->addItem(horizontalSpacer);

        label_mainname = new QLabel(widget);
        label_mainname->setObjectName(QString::fromUtf8("label_mainname"));
        label_mainname->setMinimumSize(QSize(200, 0));
        QFont font;
        font.setPointSize(20);
        label_mainname->setFont(font);
        label_mainname->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_9->addWidget(label_mainname);

        horizontalSpacer_2 = new QSpacerItem(100, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_9->addItem(horizontalSpacer_2);

        pushButton = new QPushButton(widget);
        pushButton->setObjectName(QString::fromUtf8("pushButton"));
        sizePolicy1.setHeightForWidth(pushButton->sizePolicy().hasHeightForWidth());
        pushButton->setSizePolicy(sizePolicy1);
        pushButton->setMinimumSize(QSize(134, 50));
        pushButton->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	background-color: rgb(90, 90, 90);\n"
"    border-radius:10px;  \n"
"	color: rgb(255, 255, 255);\n"
" padding:10px;\n"
"\n"
"}\n"
"QPushButton::pressed,QPushButton::checked{\n"
" padding: 5px;\n"
"	color: rgb(0, 0, 0);\n"
"	background-color: rgb(255, 255, 255);\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	color: rgb(0, 0, 0);\n"
"	background-color: rgb(255, 255, 255);\n"
"}"));

        horizontalLayout_9->addWidget(pushButton);

        horizontalSpacer_3 = new QSpacerItem(500, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_9->addItem(horizontalSpacer_3);

        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        recordingSet = new QPushButton(widget);
        recordingSet->setObjectName(QString::fromUtf8("recordingSet"));
        QSizePolicy sizePolicy2(QSizePolicy::Fixed, QSizePolicy::Fixed);
        sizePolicy2.setHorizontalStretch(0);
        sizePolicy2.setVerticalStretch(0);
        sizePolicy2.setHeightForWidth(recordingSet->sizePolicy().hasHeightForWidth());
        recordingSet->setSizePolicy(sizePolicy2);
        recordingSet->setMinimumSize(QSize(150, 50));
        recordingSet->setMaximumSize(QSize(16777215, 50));
        QFont font1;
        font1.setPointSize(10);
        recordingSet->setFont(font1);
        recordingSet->setStyleSheet(QString::fromUtf8("QPushButton{  \n"
"border-radius:5px;\n"
"background-position: top;\n"
"      background-repeat: repeat-no-repeat;        \n"
"	image: url(:/image/\345\275\225\345\203\217\350\256\276\347\275\256(\351\200\211\344\270\255).png);\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"text-align : bottom;\n"
" padding:0px;\n"
"\n"
"}\n"
"QPushButton::pressed,QPushButton::checked{\n"
"background-position: top;\n"
"	color: rgb(0, 0, 0);\n"
"	image: url(:/image/\345\275\225\345\203\217\350\256\276\347\275\256.png);\n"
"}\n"
"\n"
"QPushButton::disabled{\n"
"background-position: top;\n"
"	color: rgb(0, 0, 0);\n"
"	image: url(:/image/\345\275\225\345\203\217\350\256\276\347\275\256.png);\n"
"}"));

        horizontalLayout_5->addWidget(recordingSet);

        fileManage = new QPushButton(widget);
        fileManage->setObjectName(QString::fromUtf8("fileManage"));
        sizePolicy2.setHeightForWidth(fileManage->sizePolicy().hasHeightForWidth());
        fileManage->setSizePolicy(sizePolicy2);
        fileManage->setMinimumSize(QSize(150, 50));
        fileManage->setMaximumSize(QSize(16777215, 50));
        fileManage->setFont(font1);
        fileManage->setStyleSheet(QString::fromUtf8("QPushButton{  \n"
"border-radius:5px;\n"
"background-position: top;\n"
"      background-repeat: repeat-no-repeat;        \n"
"	image: url(:/image/\346\226\207\344\273\266\347\256\241\347\220\206.png);\n"
"	color: rgb(255, 255, 255);\n"
"text-align : bottom;\n"
" padding: 0px;\n"
"}\n"
"QPushButton::pressed,QPushButton::checked{\n"
"background-position: top;\n"
"	color: rgb(0, 0, 0);\n"
"	image: url(:/image/\346\226\207\344\273\266\347\256\241\347\220\206(\351\200\211\344\270\255).png);\n"
"}\n"
"\n"
"QPushButton::disabled{\n"
"background-position: top;\n"
"	color: rgb(0, 0, 0);\n"
"	image: url(:/image/\346\226\207\344\273\266\347\256\241\347\220\206(\351\200\211\344\270\255).png);\n"
"}"));

        horizontalLayout_5->addWidget(fileManage);

        systemSet = new QPushButton(widget);
        systemSet->setObjectName(QString::fromUtf8("systemSet"));
        sizePolicy2.setHeightForWidth(systemSet->sizePolicy().hasHeightForWidth());
        systemSet->setSizePolicy(sizePolicy2);
        systemSet->setMinimumSize(QSize(150, 50));
        systemSet->setMaximumSize(QSize(16777215, 50));
        systemSet->setFont(font1);
        systemSet->setStyleSheet(QString::fromUtf8("QPushButton{  \n"
"border-radius:5px;\n"
"background-position: top;\n"
"      background-repeat: repeat-no-repeat;        \n"
"	image: url(:/image/\347\263\273\347\273\237\350\256\276\347\275\256.png);\n"
"	color: rgb(255, 255, 255);\n"
"text-align : bottom;\n"
" padding: 0px;\n"
"}\n"
"QPushButton::pressed,QPushButton::checked{\n"
"background-position: top;\n"
"	color: rgb(0, 0, 0);\n"
"image: url(:/image/\347\263\273\347\273\237\350\256\276\347\275\256(\351\200\211\344\270\255).png);\n"
"}\n"
"\n"
"QPushButton::disabled{\n"
"background-position: top;\n"
"	color: rgb(0, 0, 0);\n"
"	image: url(:/image/\347\263\273\347\273\237\350\256\276\347\275\256(\351\200\211\344\270\255).png);\n"
"}"));

        horizontalLayout_5->addWidget(systemSet);


        horizontalLayout_9->addLayout(horizontalLayout_5);

        verticalLayout_12 = new QVBoxLayout();
        verticalLayout_12->setSpacing(0);
        verticalLayout_12->setObjectName(QString::fromUtf8("verticalLayout_12"));
        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setSpacing(0);
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        horizontalSpacer_4 = new QSpacerItem(150, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_6->addItem(horizontalSpacer_4);

        horizontalSpacer_7 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_6->addItem(horizontalSpacer_7);

        pushButton_fullscreen = new QPushButton(widget);
        pushButton_fullscreen->setObjectName(QString::fromUtf8("pushButton_fullscreen"));
        pushButton_fullscreen->setMinimumSize(QSize(25, 24));
        pushButton_fullscreen->setMaximumSize(QSize(25, 24));
        pushButton_fullscreen->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/image/\345\205\250\345\261\217.png);\n"
"border-style:none;\n"
"padding:5px;\n"
"\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	background-color: rgb(0, 0, 0);\n"
"}"));

        horizontalLayout_6->addWidget(pushButton_fullscreen);

        pushButton_min = new QPushButton(widget);
        pushButton_min->setObjectName(QString::fromUtf8("pushButton_min"));
        pushButton_min->setMinimumSize(QSize(25, 24));
        pushButton_min->setMaximumSize(QSize(25, 24));
        pushButton_min->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/image/\346\234\200\345\260\217\345\214\226.png);\n"
"border-style:none;\n"
"padding:5px;\n"
"\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	background-color: rgb(0, 0, 0);\n"
"}"));

        horizontalLayout_6->addWidget(pushButton_min);

        pushButton_max = new QPushButton(widget);
        pushButton_max->setObjectName(QString::fromUtf8("pushButton_max"));
        pushButton_max->setMinimumSize(QSize(25, 24));
        pushButton_max->setMaximumSize(QSize(25, 24));
        pushButton_max->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"\n"
"border-style:none;\n"
"padding:5px;\n"
"\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	background-color: rgb(0, 0, 0);\n"
"}\n"
"QPushButton::pressed,QPushButton::checked{\n"
"\n"
"\n"
"background:transparent;\n"
"}\n"
"QPushButton:pressed:hover, QPushButton:checked:hover {\n"
"    background-color: rgb(0, 0, 0);\n"
"}"));

        horizontalLayout_6->addWidget(pushButton_max);

        closeButton = new QPushButton(widget);
        closeButton->setObjectName(QString::fromUtf8("closeButton"));
        closeButton->setMinimumSize(QSize(25, 24));
        closeButton->setMaximumSize(QSize(25, 24));
        closeButton->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/image/\345\205\263\351\227\255.png);\n"
"border-style:none;\n"
"padding:5px;\n"
"\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	background-color: rgb(239, 41, 41);\n"
"}"));

        horizontalLayout_6->addWidget(closeButton);


        verticalLayout_12->addLayout(horizontalLayout_6);

        horizontalLayout_7 = new QHBoxLayout();
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        label_cpu = new QLabel(widget);
        label_cpu->setObjectName(QString::fromUtf8("label_cpu"));
        QSizePolicy sizePolicy3(QSizePolicy::Expanding, QSizePolicy::Fixed);
        sizePolicy3.setHorizontalStretch(0);
        sizePolicy3.setVerticalStretch(0);
        sizePolicy3.setHeightForWidth(label_cpu->sizePolicy().hasHeightForWidth());
        label_cpu->setSizePolicy(sizePolicy3);
        label_cpu->setMinimumSize(QSize(120, 20));
        label_cpu->setMaximumSize(QSize(16777215, 20));
        label_cpu->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_7->addWidget(label_cpu);

        label_gpu = new QLabel(widget);
        label_gpu->setObjectName(QString::fromUtf8("label_gpu"));
        label_gpu->setEnabled(true);
        sizePolicy3.setHeightForWidth(label_gpu->sizePolicy().hasHeightForWidth());
        label_gpu->setSizePolicy(sizePolicy3);
        label_gpu->setMinimumSize(QSize(120, 20));
        label_gpu->setMaximumSize(QSize(16777215, 20));
        label_gpu->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_7->addWidget(label_gpu);


        verticalLayout_12->addLayout(horizontalLayout_7);

        horizontalLayout_8 = new QHBoxLayout();
        horizontalLayout_8->setObjectName(QString::fromUtf8("horizontalLayout_8"));
        label_memory = new QLabel(widget);
        label_memory->setObjectName(QString::fromUtf8("label_memory"));
        sizePolicy3.setHeightForWidth(label_memory->sizePolicy().hasHeightForWidth());
        label_memory->setSizePolicy(sizePolicy3);
        label_memory->setMinimumSize(QSize(120, 20));
        label_memory->setMaximumSize(QSize(16777215, 20));
        label_memory->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_8->addWidget(label_memory);

        label_time = new QLabel(widget);
        label_time->setObjectName(QString::fromUtf8("label_time"));
        sizePolicy3.setHeightForWidth(label_time->sizePolicy().hasHeightForWidth());
        label_time->setSizePolicy(sizePolicy3);
        label_time->setMinimumSize(QSize(120, 20));
        label_time->setMaximumSize(QSize(16777215, 20));
        label_time->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_8->addWidget(label_time);


        verticalLayout_12->addLayout(horizontalLayout_8);


        horizontalLayout_9->addLayout(verticalLayout_12);


        gridLayout_4->addWidget(widget, 0, 0, 1, 2);

        widget_2 = new QWidget(centralwidget);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        QSizePolicy sizePolicy4(QSizePolicy::Preferred, QSizePolicy::Expanding);
        sizePolicy4.setHorizontalStretch(0);
        sizePolicy4.setVerticalStretch(0);
        sizePolicy4.setHeightForWidth(widget_2->sizePolicy().hasHeightForWidth());
        widget_2->setSizePolicy(sizePolicy4);
        widget_2->setMinimumSize(QSize(265, 0));
        widget_2->setMaximumSize(QSize(265, 16777215));
        widget_2->setStyleSheet(QString::fromUtf8("background-color: rgb(90, 90, 90);"));
        gridLayout_3 = new QGridLayout(widget_2);
        gridLayout_3->setObjectName(QString::fromUtf8("gridLayout_3"));
        gridLayout_3->setContentsMargins(0, 0, 0, 0);
        verticalLayout_11 = new QVBoxLayout();
        verticalLayout_11->setSpacing(8);
        verticalLayout_11->setObjectName(QString::fromUtf8("verticalLayout_11"));
        verticalLayout_11->setSizeConstraint(QLayout::SetFixedSize);
        verticalLayout_2 = new QVBoxLayout();
        verticalLayout_2->setSpacing(15);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setSizeConstraint(QLayout::SetFixedSize);
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setSpacing(0);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalLayout->setSizeConstraint(QLayout::SetFixedSize);
        videoSet = new QPushButton(widget_2);
        videoSet->setObjectName(QString::fromUtf8("videoSet"));
        sizePolicy2.setHeightForWidth(videoSet->sizePolicy().hasHeightForWidth());
        videoSet->setSizePolicy(sizePolicy2);
        videoSet->setMinimumSize(QSize(65, 40));
        videoSet->setMaximumSize(QSize(65, 40));
        videoSet->setAutoFillBackground(false);
        videoSet->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/image/icons/\350\247\206\351\242\221\350\256\276\347\275\256(default).png);\n"
"border-style:none;\n"
"border: 1px solid rgb(0, 170, 255);\n"
" padding: 5px;\n"
"background:transparent;\n"
"}\n"
"QPushButton::pressed,QPushButton::checked{\n"
"	image: url(:/image/icons/\350\247\206\351\242\221\350\256\276\347\275\256.png);\n"
" padding: 5px;\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	image: url(:/image/icons/\350\247\206\351\242\221\350\256\276\347\275\256.png);\n"
"}"));
        videoSet->setCheckable(true);
        videoSet->setAutoExclusive(true);

        horizontalLayout->addWidget(videoSet);

        pictureSet = new QPushButton(widget_2);
        pictureSet->setObjectName(QString::fromUtf8("pictureSet"));
        sizePolicy2.setHeightForWidth(pictureSet->sizePolicy().hasHeightForWidth());
        pictureSet->setSizePolicy(sizePolicy2);
        pictureSet->setMinimumSize(QSize(65, 40));
        pictureSet->setMaximumSize(QSize(65, 40));
        pictureSet->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/image/icons/\345\233\276\345\203\217\350\256\276\347\275\256(default).png);\n"
"border-style:none;\n"
"border: 1px solid rgb(0, 170, 255);\n"
" padding: 5px;\n"
"background:transparent;\n"
"}\n"
"QPushButton::pressed,QPushButton::checked{\n"
"	image: url(:/image/icons/\345\233\276\345\203\217\350\256\276\347\275\256.png);\n"
" padding: 5px;\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	image: url(:/image/icons/\345\233\276\345\203\217\350\256\276\347\275\256.png);\n"
"}"));
        pictureSet->setCheckable(true);
        pictureSet->setAutoExclusive(true);

        horizontalLayout->addWidget(pictureSet);

        cameraControl = new QPushButton(widget_2);
        cameraControl->setObjectName(QString::fromUtf8("cameraControl"));
        sizePolicy2.setHeightForWidth(cameraControl->sizePolicy().hasHeightForWidth());
        cameraControl->setSizePolicy(sizePolicy2);
        cameraControl->setMinimumSize(QSize(65, 40));
        cameraControl->setMaximumSize(QSize(65, 40));
        cameraControl->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/image/icons/\347\233\270\346\234\272\346\216\247\345\210\266(default).png);\n"
"border-style:none;\n"
"border: 1px solid rgb(0, 170, 255);\n"
" padding: 5px;\n"
"background:transparent;\n"
"}\n"
"QPushButton::pressed,QPushButton::checked{\n"
"	image: url(:/image/icons/\347\233\270\346\234\272\346\216\247\345\210\266.png);\n"
" padding: 5px;\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	image: url(:/image/icons/\347\233\270\346\234\272\346\216\247\345\210\266.png);\n"
"}"));
        cameraControl->setCheckable(true);
        cameraControl->setAutoExclusive(true);

        horizontalLayout->addWidget(cameraControl);

        textSet = new QPushButton(widget_2);
        textSet->setObjectName(QString::fromUtf8("textSet"));
        textSet->setMinimumSize(QSize(65, 40));
        textSet->setMaximumSize(QSize(65, 40));
        textSet->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/image/icons/\346\226\207\345\255\227\350\256\276\347\275\256(default).png);\n"
"border-style:none;\n"
"border: 1px solid rgb(0, 170, 255);\n"
" padding: 5px;\n"
"background:transparent;\n"
"}\n"
"QPushButton::pressed,QPushButton::checked{\n"
"	image: url(:/image/icons/\346\226\207\345\255\227\350\256\276\347\275\256.png);\n"
" padding: 5px;\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	image: url(:/image/icons/\346\226\207\345\255\227\350\256\276\347\275\256.png);\n"
"}"));
        textSet->setCheckable(true);
        textSet->setAutoExclusive(true);

        horizontalLayout->addWidget(textSet);


        verticalLayout_2->addLayout(horizontalLayout);

        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        label_xinhaoy = new QLabel(widget_2);
        label_xinhaoy->setObjectName(QString::fromUtf8("label_xinhaoy"));
        sizePolicy1.setHeightForWidth(label_xinhaoy->sizePolicy().hasHeightForWidth());
        label_xinhaoy->setSizePolicy(sizePolicy1);
        label_xinhaoy->setMinimumSize(QSize(0, 22));
        label_xinhaoy->setMaximumSize(QSize(16777215, 22));
        label_xinhaoy->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout->addWidget(label_xinhaoy);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setSpacing(2);
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        horizontalLayout_2->setSizeConstraint(QLayout::SetFixedSize);
        horizontalLayout_2->setContentsMargins(-1, -1, 0, -1);
        p1 = new QPushButton(widget_2);
        p1->setObjectName(QString::fromUtf8("p1"));
        sizePolicy2.setHeightForWidth(p1->sizePolicy().hasHeightForWidth());
        p1->setSizePolicy(sizePolicy2);
        p1->setMinimumSize(QSize(60, 30));
        p1->setMaximumSize(QSize(60, 30));
        p1->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}"));
        p1->setCheckable(true);
        p1->setAutoExclusive(true);

        horizontalLayout_2->addWidget(p1);

        p2 = new QPushButton(widget_2);
        p2->setObjectName(QString::fromUtf8("p2"));
        p2->setMinimumSize(QSize(60, 30));
        p2->setMaximumSize(QSize(60, 30));
        p2->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}"));
        p2->setCheckable(true);
        p2->setAutoExclusive(true);

        horizontalLayout_2->addWidget(p2);

        p3 = new QPushButton(widget_2);
        p3->setObjectName(QString::fromUtf8("p3"));
        p3->setMinimumSize(QSize(60, 30));
        p3->setMaximumSize(QSize(60, 30));
        p3->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}"));
        p3->setCheckable(true);
        p3->setAutoExclusive(true);

        horizontalLayout_2->addWidget(p3);

        p4 = new QPushButton(widget_2);
        p4->setObjectName(QString::fromUtf8("p4"));
        p4->setMinimumSize(QSize(60, 30));
        p4->setMaximumSize(QSize(60, 30));
        p4->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}"));
        p4->setCheckable(true);
        p4->setAutoExclusive(true);

        horizontalLayout_2->addWidget(p4);


        verticalLayout->addLayout(horizontalLayout_2);


        verticalLayout_2->addLayout(verticalLayout);


        verticalLayout_11->addLayout(verticalLayout_2);

        stackedWidget = new QStackedWidget(widget_2);
        stackedWidget->setObjectName(QString::fromUtf8("stackedWidget"));
        sizePolicy.setHeightForWidth(stackedWidget->sizePolicy().hasHeightForWidth());
        stackedWidget->setSizePolicy(sizePolicy);
        page_videoset = new QWidget();
        page_videoset->setObjectName(QString::fromUtf8("page_videoset"));
        verticalLayout_9 = new QVBoxLayout(page_videoset);
        verticalLayout_9->setObjectName(QString::fromUtf8("verticalLayout_9"));
        verticalLayout_8 = new QVBoxLayout();
        verticalLayout_8->setSpacing(80);
        verticalLayout_8->setObjectName(QString::fromUtf8("verticalLayout_8"));
        verticalLayout_3 = new QVBoxLayout();
        verticalLayout_3->setSpacing(15);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        label_cameradev = new QLabel(page_videoset);
        label_cameradev->setObjectName(QString::fromUtf8("label_cameradev"));
        sizePolicy1.setHeightForWidth(label_cameradev->sizePolicy().hasHeightForWidth());
        label_cameradev->setSizePolicy(sizePolicy1);
        label_cameradev->setMinimumSize(QSize(0, 22));
        label_cameradev->setMaximumSize(QSize(16777215, 22));
        label_cameradev->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_3->addWidget(label_cameradev);

        comboBox_cameradev = new QComboBox(page_videoset);
        comboBox_cameradev->setObjectName(QString::fromUtf8("comboBox_cameradev"));
        comboBox_cameradev->setMinimumSize(QSize(230, 25));
        comboBox_cameradev->setMaximumSize(QSize(230, 25));
        comboBox_cameradev->setStyleSheet(QString::fromUtf8("QComboBox {\n"
"    font: 75 9pt 'Arial';\n"
"    color: rgb(0, 0, 0);\n"
"    background-color: rgb(255, 255, 255);\n"
"    padding: 1px 15px 1px 10px;\n"
"    border: none;\n"
"    border-radius: 5px 5px 0px 0px;\n"
"}\n"
"QComboBox::drop-down {\n"
"	image: url(:/image/icons/combox_ico_drop.png);\n"
"    padding: 3px 6px 1px 3px;\n"
"    width: 18px;\n"
"}\n"
"QComboBox QAbstractItemView {\n"
"    outline: none;\n"
"    border: 1px solid white;\n"
"    color: rgb(0, 0, 0);\n"
"    background-color: rgb(255, 255, 255);\n"
"    selection-background-color: rgb(90, 90, 90);\n"
"}\n"
"QAbstractItemView::item {\n"
"    height: 30px;\n"
"}\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    color: #FFFFFF;\n"
"    background-color: rgb(90, 90, 90);\n"
"}\n"
"QComboBox:disabled {\n"
"    background-color: rgb(188, 188, 188);\n"
"    color: rgb(122, 122, 122);\n"
"}"));

        verticalLayout_3->addWidget(comboBox_cameradev);


        verticalLayout_8->addLayout(verticalLayout_3);

        verticalLayout_4 = new QVBoxLayout();
        verticalLayout_4->setSpacing(15);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        label_videoformat = new QLabel(page_videoset);
        label_videoformat->setObjectName(QString::fromUtf8("label_videoformat"));
        sizePolicy1.setHeightForWidth(label_videoformat->sizePolicy().hasHeightForWidth());
        label_videoformat->setSizePolicy(sizePolicy1);
        label_videoformat->setMinimumSize(QSize(0, 22));
        label_videoformat->setMaximumSize(QSize(16777215, 22));
        label_videoformat->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_4->addWidget(label_videoformat);

        comboBox_videoformat = new QComboBox(page_videoset);
        comboBox_videoformat->setObjectName(QString::fromUtf8("comboBox_videoformat"));
        comboBox_videoformat->setMinimumSize(QSize(230, 25));
        comboBox_videoformat->setMaximumSize(QSize(230, 25));
        comboBox_videoformat->setStyleSheet(QString::fromUtf8("QComboBox {\n"
"    font: 75 9pt 'Arial';\n"
"    color: rgb(0, 0, 0);\n"
"    background-color: rgb(255, 255, 255);\n"
"    padding: 1px 15px 1px 10px;\n"
"    border: none;\n"
"    border-radius: 5px 5px 0px 0px;\n"
"}\n"
"QComboBox::drop-down {\n"
"	image: url(:/image/icons/combox_ico_drop.png);\n"
"    padding: 3px 6px 1px 3px;\n"
"    width: 18px;\n"
"}\n"
"QComboBox QAbstractItemView {\n"
"    outline: none;\n"
"    border: 1px solid white;\n"
"    color: rgb(0, 0, 0);\n"
"    background-color: rgb(255, 255, 255);\n"
"    selection-background-color: rgb(90, 90, 90);\n"
"}\n"
"QAbstractItemView::item {\n"
"    height: 30px;\n"
"}\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    color: #FFFFFF;\n"
"    background-color: rgb(90, 90, 90);\n"
"}\n"
"QComboBox:disabled {\n"
"    background-color: rgb(188, 188, 188);\n"
"    color: rgb(122, 122, 122);\n"
"}"));

        verticalLayout_4->addWidget(comboBox_videoformat);


        verticalLayout_8->addLayout(verticalLayout_4);

        verticalLayout_5 = new QVBoxLayout();
        verticalLayout_5->setSpacing(15);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        label_fbl = new QLabel(page_videoset);
        label_fbl->setObjectName(QString::fromUtf8("label_fbl"));
        sizePolicy1.setHeightForWidth(label_fbl->sizePolicy().hasHeightForWidth());
        label_fbl->setSizePolicy(sizePolicy1);
        label_fbl->setMinimumSize(QSize(0, 22));
        label_fbl->setMaximumSize(QSize(16777215, 22));
        label_fbl->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_5->addWidget(label_fbl);

        comboBox_fbl = new QComboBox(page_videoset);
        comboBox_fbl->setObjectName(QString::fromUtf8("comboBox_fbl"));
        comboBox_fbl->setMinimumSize(QSize(230, 25));
        comboBox_fbl->setMaximumSize(QSize(230, 25));
        comboBox_fbl->setStyleSheet(QString::fromUtf8("QComboBox {\n"
"    font: 75 9pt 'Arial';\n"
"    color: rgb(0, 0, 0);\n"
"    background-color: rgb(255, 255, 255);\n"
"    padding: 1px 15px 1px 10px;\n"
"    border: none;\n"
"    border-radius: 5px 5px 0px 0px;\n"
"}\n"
"QComboBox::drop-down {\n"
"	image: url(:/image/icons/combox_ico_drop.png);\n"
"    padding: 3px 6px 1px 3px;\n"
"    width: 18px;\n"
"}\n"
"QComboBox QAbstractItemView {\n"
"    outline: none;\n"
"    border: 1px solid white;\n"
"    color: rgb(0, 0, 0);\n"
"    background-color: rgb(255, 255, 255);\n"
"    selection-background-color: rgb(90, 90, 90);\n"
"}\n"
"QAbstractItemView::item {\n"
"    height: 30px;\n"
"}\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    color: #FFFFFF;\n"
"    background-color: rgb(90, 90, 90);\n"
"}\n"
"QComboBox:disabled {\n"
"    background-color: rgb(188, 188, 188);\n"
"    color: rgb(122, 122, 122);\n"
"}"));

        verticalLayout_5->addWidget(comboBox_fbl);


        verticalLayout_8->addLayout(verticalLayout_5);

        verticalLayout_7 = new QVBoxLayout();
        verticalLayout_7->setSpacing(25);
        verticalLayout_7->setObjectName(QString::fromUtf8("verticalLayout_7"));
        verticalLayout_6 = new QVBoxLayout();
        verticalLayout_6->setSpacing(15);
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        label_audiodev = new QLabel(page_videoset);
        label_audiodev->setObjectName(QString::fromUtf8("label_audiodev"));
        sizePolicy1.setHeightForWidth(label_audiodev->sizePolicy().hasHeightForWidth());
        label_audiodev->setSizePolicy(sizePolicy1);
        label_audiodev->setMinimumSize(QSize(0, 22));
        label_audiodev->setMaximumSize(QSize(16777215, 22));
        label_audiodev->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_6->addWidget(label_audiodev);

        comboBox_audiodev = new QComboBox(page_videoset);
        comboBox_audiodev->setObjectName(QString::fromUtf8("comboBox_audiodev"));
        comboBox_audiodev->setMinimumSize(QSize(230, 25));
        comboBox_audiodev->setMaximumSize(QSize(230, 25));
        comboBox_audiodev->setStyleSheet(QString::fromUtf8("QComboBox {\n"
"    font: 75 9pt 'Arial';\n"
"    color: rgb(0, 0, 0);\n"
"    background-color: rgb(255, 255, 255);\n"
"    padding: 1px 15px 1px 10px;\n"
"    border: none;\n"
"    border-radius: 5px 5px 0px 0px;\n"
"}\n"
"QComboBox::drop-down {\n"
"	image: url(:/image/icons/combox_ico_drop.png);\n"
"    padding: 3px 6px 1px 3px;\n"
"    width: 18px;\n"
"}\n"
"QComboBox QAbstractItemView {\n"
"    outline: none;\n"
"    border: 1px solid white;\n"
"    color: rgb(0, 0, 0);\n"
"    background-color: rgb(255, 255, 255);\n"
"    selection-background-color: rgb(90, 90, 90);\n"
"}\n"
"QAbstractItemView::item {\n"
"    height: 30px;\n"
"}\n"
"QComboBox QAbstractItemView::item:hover {\n"
"    color: #FFFFFF;\n"
"    background-color: rgb(90, 90, 90);\n"
"}\n"
"QComboBox:disabled {\n"
"    background-color: rgb(188, 188, 188);\n"
"    color: rgb(122, 122, 122);\n"
"}"));

        verticalLayout_6->addWidget(comboBox_audiodev);


        verticalLayout_7->addLayout(verticalLayout_6);

        checkBox_audio = new QCheckBox(page_videoset);
        checkBox_audio->setObjectName(QString::fromUtf8("checkBox_audio"));
        checkBox_audio->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_7->addWidget(checkBox_audio);


        verticalLayout_8->addLayout(verticalLayout_7);


        verticalLayout_9->addLayout(verticalLayout_8);

        stackedWidget->addWidget(page_videoset);
        page_pictureset = new QWidget();
        page_pictureset->setObjectName(QString::fromUtf8("page_pictureset"));
        gridLayout_6 = new QGridLayout(page_pictureset);
        gridLayout_6->setObjectName(QString::fromUtf8("gridLayout_6"));
        scrollArea = new QScrollArea(page_pictureset);
        scrollArea->setObjectName(QString::fromUtf8("scrollArea"));
        scrollArea->setWidgetResizable(true);
        scrollAreaWidgetContents = new QWidget();
        scrollAreaWidgetContents->setObjectName(QString::fromUtf8("scrollAreaWidgetContents"));
        scrollAreaWidgetContents->setGeometry(QRect(0, 0, 229, 703));
        gridLayout_5 = new QGridLayout(scrollAreaWidgetContents);
        gridLayout_5->setObjectName(QString::fromUtf8("gridLayout_5"));
        verticalLayout_22 = new QVBoxLayout();
        verticalLayout_22->setSpacing(18);
        verticalLayout_22->setObjectName(QString::fromUtf8("verticalLayout_22"));
        verticalLayout_13 = new QVBoxLayout();
        verticalLayout_13->setObjectName(QString::fromUtf8("verticalLayout_13"));
        label_brightness = new QLabel(scrollAreaWidgetContents);
        label_brightness->setObjectName(QString::fromUtf8("label_brightness"));
        label_brightness->setFont(font1);
        label_brightness->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_13->addWidget(label_brightness);

        horizontalLayout_10 = new QHBoxLayout();
        horizontalLayout_10->setObjectName(QString::fromUtf8("horizontalLayout_10"));
        horizontalSlider_brightness = new QSlider(scrollAreaWidgetContents);
        horizontalSlider_brightness->setObjectName(QString::fromUtf8("horizontalSlider_brightness"));
        horizontalSlider_brightness->setMinimumSize(QSize(149, 0));
        horizontalSlider_brightness->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_brightness->setStyleSheet(QString::fromUtf8(""));
        horizontalSlider_brightness->setOrientation(Qt::Horizontal);

        horizontalLayout_10->addWidget(horizontalSlider_brightness);

        spinBox_brightness = new QSpinBox(scrollAreaWidgetContents);
        spinBox_brightness->setObjectName(QString::fromUtf8("spinBox_brightness"));
        QSizePolicy sizePolicy5(QSizePolicy::Fixed, QSizePolicy::Fixed);
        sizePolicy5.setHorizontalStretch(50);
        sizePolicy5.setVerticalStretch(25);
        sizePolicy5.setHeightForWidth(spinBox_brightness->sizePolicy().hasHeightForWidth());
        spinBox_brightness->setSizePolicy(sizePolicy5);
        spinBox_brightness->setMinimumSize(QSize(50, 25));
        spinBox_brightness->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_10->addWidget(spinBox_brightness);


        verticalLayout_13->addLayout(horizontalLayout_10);


        verticalLayout_22->addLayout(verticalLayout_13);

        verticalLayout_14 = new QVBoxLayout();
        verticalLayout_14->setObjectName(QString::fromUtf8("verticalLayout_14"));
        label_contrast = new QLabel(scrollAreaWidgetContents);
        label_contrast->setObjectName(QString::fromUtf8("label_contrast"));
        label_contrast->setFont(font1);
        label_contrast->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_14->addWidget(label_contrast);

        horizontalLayout_11 = new QHBoxLayout();
        horizontalLayout_11->setObjectName(QString::fromUtf8("horizontalLayout_11"));
        horizontalSlider_contrast = new QSlider(scrollAreaWidgetContents);
        horizontalSlider_contrast->setObjectName(QString::fromUtf8("horizontalSlider_contrast"));
        horizontalSlider_contrast->setMinimumSize(QSize(149, 0));
        horizontalSlider_contrast->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_contrast->setOrientation(Qt::Horizontal);

        horizontalLayout_11->addWidget(horizontalSlider_contrast);

        spinBox_contrast = new QSpinBox(scrollAreaWidgetContents);
        spinBox_contrast->setObjectName(QString::fromUtf8("spinBox_contrast"));
        sizePolicy5.setHeightForWidth(spinBox_contrast->sizePolicy().hasHeightForWidth());
        spinBox_contrast->setSizePolicy(sizePolicy5);
        spinBox_contrast->setMinimumSize(QSize(50, 25));
        spinBox_contrast->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_11->addWidget(spinBox_contrast);


        verticalLayout_14->addLayout(horizontalLayout_11);


        verticalLayout_22->addLayout(verticalLayout_14);

        verticalLayout_15 = new QVBoxLayout();
        verticalLayout_15->setObjectName(QString::fromUtf8("verticalLayout_15"));
        label_saturation = new QLabel(scrollAreaWidgetContents);
        label_saturation->setObjectName(QString::fromUtf8("label_saturation"));
        label_saturation->setFont(font1);
        label_saturation->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_15->addWidget(label_saturation);

        horizontalLayout_12 = new QHBoxLayout();
        horizontalLayout_12->setObjectName(QString::fromUtf8("horizontalLayout_12"));
        horizontalSlider_saturation = new QSlider(scrollAreaWidgetContents);
        horizontalSlider_saturation->setObjectName(QString::fromUtf8("horizontalSlider_saturation"));
        horizontalSlider_saturation->setMinimumSize(QSize(149, 0));
        horizontalSlider_saturation->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_saturation->setOrientation(Qt::Horizontal);

        horizontalLayout_12->addWidget(horizontalSlider_saturation);

        spinBox_saturation = new QSpinBox(scrollAreaWidgetContents);
        spinBox_saturation->setObjectName(QString::fromUtf8("spinBox_saturation"));
        sizePolicy2.setHeightForWidth(spinBox_saturation->sizePolicy().hasHeightForWidth());
        spinBox_saturation->setSizePolicy(sizePolicy2);
        spinBox_saturation->setMinimumSize(QSize(50, 25));
        spinBox_saturation->setMaximumSize(QSize(50, 25));
        spinBox_saturation->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_12->addWidget(spinBox_saturation);


        verticalLayout_15->addLayout(horizontalLayout_12);


        verticalLayout_22->addLayout(verticalLayout_15);

        verticalLayout_16 = new QVBoxLayout();
        verticalLayout_16->setObjectName(QString::fromUtf8("verticalLayout_16"));
        label_hue = new QLabel(scrollAreaWidgetContents);
        label_hue->setObjectName(QString::fromUtf8("label_hue"));
        label_hue->setFont(font1);
        label_hue->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_16->addWidget(label_hue);

        horizontalLayout_13 = new QHBoxLayout();
        horizontalLayout_13->setObjectName(QString::fromUtf8("horizontalLayout_13"));
        horizontalSlider_hue = new QSlider(scrollAreaWidgetContents);
        horizontalSlider_hue->setObjectName(QString::fromUtf8("horizontalSlider_hue"));
        horizontalSlider_hue->setMinimumSize(QSize(149, 0));
        horizontalSlider_hue->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_hue->setOrientation(Qt::Horizontal);

        horizontalLayout_13->addWidget(horizontalSlider_hue);

        spinBox_hue = new QSpinBox(scrollAreaWidgetContents);
        spinBox_hue->setObjectName(QString::fromUtf8("spinBox_hue"));
        sizePolicy2.setHeightForWidth(spinBox_hue->sizePolicy().hasHeightForWidth());
        spinBox_hue->setSizePolicy(sizePolicy2);
        spinBox_hue->setMinimumSize(QSize(50, 25));
        spinBox_hue->setMaximumSize(QSize(50, 25));
        spinBox_hue->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_13->addWidget(spinBox_hue);


        verticalLayout_16->addLayout(horizontalLayout_13);


        verticalLayout_22->addLayout(verticalLayout_16);

        verticalLayout_17 = new QVBoxLayout();
        verticalLayout_17->setObjectName(QString::fromUtf8("verticalLayout_17"));
        label_sharpness = new QLabel(scrollAreaWidgetContents);
        label_sharpness->setObjectName(QString::fromUtf8("label_sharpness"));
        label_sharpness->setFont(font1);
        label_sharpness->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_17->addWidget(label_sharpness);

        horizontalLayout_14 = new QHBoxLayout();
        horizontalLayout_14->setObjectName(QString::fromUtf8("horizontalLayout_14"));
        horizontalSlider_sharpness = new QSlider(scrollAreaWidgetContents);
        horizontalSlider_sharpness->setObjectName(QString::fromUtf8("horizontalSlider_sharpness"));
        horizontalSlider_sharpness->setMinimumSize(QSize(149, 0));
        horizontalSlider_sharpness->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_sharpness->setOrientation(Qt::Horizontal);

        horizontalLayout_14->addWidget(horizontalSlider_sharpness);

        spinBox_sharpness = new QSpinBox(scrollAreaWidgetContents);
        spinBox_sharpness->setObjectName(QString::fromUtf8("spinBox_sharpness"));
        sizePolicy2.setHeightForWidth(spinBox_sharpness->sizePolicy().hasHeightForWidth());
        spinBox_sharpness->setSizePolicy(sizePolicy2);
        spinBox_sharpness->setMinimumSize(QSize(50, 25));
        spinBox_sharpness->setMaximumSize(QSize(50, 25));
        spinBox_sharpness->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_14->addWidget(spinBox_sharpness);


        verticalLayout_17->addLayout(horizontalLayout_14);


        verticalLayout_22->addLayout(verticalLayout_17);

        verticalLayout_18 = new QVBoxLayout();
        verticalLayout_18->setObjectName(QString::fromUtf8("verticalLayout_18"));
        label_gamma = new QLabel(scrollAreaWidgetContents);
        label_gamma->setObjectName(QString::fromUtf8("label_gamma"));
        label_gamma->setFont(font1);
        label_gamma->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_18->addWidget(label_gamma);

        horizontalLayout_15 = new QHBoxLayout();
        horizontalLayout_15->setObjectName(QString::fromUtf8("horizontalLayout_15"));
        horizontalSlider_gamma = new QSlider(scrollAreaWidgetContents);
        horizontalSlider_gamma->setObjectName(QString::fromUtf8("horizontalSlider_gamma"));
        horizontalSlider_gamma->setMinimumSize(QSize(149, 0));
        horizontalSlider_gamma->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_gamma->setOrientation(Qt::Horizontal);

        horizontalLayout_15->addWidget(horizontalSlider_gamma);

        spinBox_gamma = new QSpinBox(scrollAreaWidgetContents);
        spinBox_gamma->setObjectName(QString::fromUtf8("spinBox_gamma"));
        sizePolicy2.setHeightForWidth(spinBox_gamma->sizePolicy().hasHeightForWidth());
        spinBox_gamma->setSizePolicy(sizePolicy2);
        spinBox_gamma->setMinimumSize(QSize(50, 25));
        spinBox_gamma->setMaximumSize(QSize(50, 25));
        spinBox_gamma->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_15->addWidget(spinBox_gamma);


        verticalLayout_18->addLayout(horizontalLayout_15);


        verticalLayout_22->addLayout(verticalLayout_18);

        verticalLayout_19 = new QVBoxLayout();
        verticalLayout_19->setObjectName(QString::fromUtf8("verticalLayout_19"));
        label_backlightcompensation = new QLabel(scrollAreaWidgetContents);
        label_backlightcompensation->setObjectName(QString::fromUtf8("label_backlightcompensation"));
        label_backlightcompensation->setFont(font1);
        label_backlightcompensation->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_19->addWidget(label_backlightcompensation);

        horizontalLayout_16 = new QHBoxLayout();
        horizontalLayout_16->setObjectName(QString::fromUtf8("horizontalLayout_16"));
        horizontalSlider_backlightcompensation = new QSlider(scrollAreaWidgetContents);
        horizontalSlider_backlightcompensation->setObjectName(QString::fromUtf8("horizontalSlider_backlightcompensation"));
        horizontalSlider_backlightcompensation->setMinimumSize(QSize(149, 0));
        horizontalSlider_backlightcompensation->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_backlightcompensation->setOrientation(Qt::Horizontal);

        horizontalLayout_16->addWidget(horizontalSlider_backlightcompensation);

        spinBox_backlightcompensation = new QSpinBox(scrollAreaWidgetContents);
        spinBox_backlightcompensation->setObjectName(QString::fromUtf8("spinBox_backlightcompensation"));
        sizePolicy2.setHeightForWidth(spinBox_backlightcompensation->sizePolicy().hasHeightForWidth());
        spinBox_backlightcompensation->setSizePolicy(sizePolicy2);
        spinBox_backlightcompensation->setMinimumSize(QSize(50, 25));
        spinBox_backlightcompensation->setMaximumSize(QSize(50, 25));
        spinBox_backlightcompensation->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_16->addWidget(spinBox_backlightcompensation);


        verticalLayout_19->addLayout(horizontalLayout_16);


        verticalLayout_22->addLayout(verticalLayout_19);

        verticalLayout_20 = new QVBoxLayout();
        verticalLayout_20->setObjectName(QString::fromUtf8("verticalLayout_20"));
        label_gain = new QLabel(scrollAreaWidgetContents);
        label_gain->setObjectName(QString::fromUtf8("label_gain"));
        label_gain->setFont(font1);
        label_gain->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_20->addWidget(label_gain);

        horizontalLayout_17 = new QHBoxLayout();
        horizontalLayout_17->setObjectName(QString::fromUtf8("horizontalLayout_17"));
        horizontalSlider_gain = new QSlider(scrollAreaWidgetContents);
        horizontalSlider_gain->setObjectName(QString::fromUtf8("horizontalSlider_gain"));
        horizontalSlider_gain->setMinimumSize(QSize(149, 0));
        horizontalSlider_gain->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_gain->setOrientation(Qt::Horizontal);

        horizontalLayout_17->addWidget(horizontalSlider_gain);

        spinBox_gain = new QSpinBox(scrollAreaWidgetContents);
        spinBox_gain->setObjectName(QString::fromUtf8("spinBox_gain"));
        sizePolicy2.setHeightForWidth(spinBox_gain->sizePolicy().hasHeightForWidth());
        spinBox_gain->setSizePolicy(sizePolicy2);
        spinBox_gain->setMinimumSize(QSize(50, 25));
        spinBox_gain->setMaximumSize(QSize(50, 25));
        spinBox_gain->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_17->addWidget(spinBox_gain);


        verticalLayout_20->addLayout(horizontalLayout_17);


        verticalLayout_22->addLayout(verticalLayout_20);

        verticalLayout_21 = new QVBoxLayout();
        verticalLayout_21->setObjectName(QString::fromUtf8("verticalLayout_21"));
        horizontalLayout_20 = new QHBoxLayout();
        horizontalLayout_20->setObjectName(QString::fromUtf8("horizontalLayout_20"));
        label_whitebalance = new QLabel(scrollAreaWidgetContents);
        label_whitebalance->setObjectName(QString::fromUtf8("label_whitebalance"));
        label_whitebalance->setFont(font1);
        label_whitebalance->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_20->addWidget(label_whitebalance);

        checkBox_whitebalance = new QCheckBox(scrollAreaWidgetContents);
        checkBox_whitebalance->setObjectName(QString::fromUtf8("checkBox_whitebalance"));
        checkBox_whitebalance->setMaximumSize(QSize(60, 16777215));
        checkBox_whitebalance->setFont(font1);
        checkBox_whitebalance->setStyleSheet(QString::fromUtf8("QCheckBox {\n"
"    spacing: 5px;                    /* \346\226\207\345\255\227\344\270\216\346\211\223\345\213\276\346\241\206\344\271\213\351\227\264\347\232\204\351\227\264\350\267\235 */\n"
"	color: rgb(255, 255, 255);\n"
"}\n"
""));

        horizontalLayout_20->addWidget(checkBox_whitebalance);


        verticalLayout_21->addLayout(horizontalLayout_20);

        horizontalLayout_18 = new QHBoxLayout();
        horizontalLayout_18->setObjectName(QString::fromUtf8("horizontalLayout_18"));
        horizontalSlider_whitebalance = new QSlider(scrollAreaWidgetContents);
        horizontalSlider_whitebalance->setObjectName(QString::fromUtf8("horizontalSlider_whitebalance"));
        horizontalSlider_whitebalance->setMinimumSize(QSize(149, 0));
        horizontalSlider_whitebalance->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_whitebalance->setOrientation(Qt::Horizontal);

        horizontalLayout_18->addWidget(horizontalSlider_whitebalance);

        spinBox_whitebalance = new QSpinBox(scrollAreaWidgetContents);
        spinBox_whitebalance->setObjectName(QString::fromUtf8("spinBox_whitebalance"));
        sizePolicy2.setHeightForWidth(spinBox_whitebalance->sizePolicy().hasHeightForWidth());
        spinBox_whitebalance->setSizePolicy(sizePolicy2);
        spinBox_whitebalance->setMinimumSize(QSize(50, 25));
        spinBox_whitebalance->setMaximumSize(QSize(50, 25));
        spinBox_whitebalance->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_18->addWidget(spinBox_whitebalance);


        verticalLayout_21->addLayout(horizontalLayout_18);


        verticalLayout_22->addLayout(verticalLayout_21);

        horizontalLayout_19 = new QHBoxLayout();
        horizontalLayout_19->setObjectName(QString::fromUtf8("horizontalLayout_19"));
        pushButton_default1 = new QPushButton(scrollAreaWidgetContents);
        pushButton_default1->setObjectName(QString::fromUtf8("pushButton_default1"));
        pushButton_default1->setMinimumSize(QSize(70, 30));
        pushButton_default1->setFont(font1);
        pushButton_default1->setStyleSheet(QString::fromUtf8("QPushButton{  \n"
"	background-color: rgb(255, 255, 255);\n"
"border-radius:5px;      \n"
"	color: rgb(0, 0, 0);\n"
" padding: 2px;\n"
"}\n"
"QPushButton::pressed,QPushButton::checked{\n"
"	background-color: rgb(0, 0, 0);\n"
"	color: rgb(255, 255, 255);\n"
"}\n"
""));

        horizontalLayout_19->addWidget(pushButton_default1);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_19->addItem(horizontalSpacer_5);


        verticalLayout_22->addLayout(horizontalLayout_19);


        gridLayout_5->addLayout(verticalLayout_22, 0, 0, 1, 1);

        scrollArea->setWidget(scrollAreaWidgetContents);

        gridLayout_6->addWidget(scrollArea, 0, 0, 1, 1);

        stackedWidget->addWidget(page_pictureset);
        page_cameracontrol = new QWidget();
        page_cameracontrol->setObjectName(QString::fromUtf8("page_cameracontrol"));
        verticalLayout_28 = new QVBoxLayout(page_cameracontrol);
        verticalLayout_28->setObjectName(QString::fromUtf8("verticalLayout_28"));
        verticalLayout_23 = new QVBoxLayout();
        verticalLayout_23->setObjectName(QString::fromUtf8("verticalLayout_23"));
        horizontalLayout_21 = new QHBoxLayout();
        horizontalLayout_21->setObjectName(QString::fromUtf8("horizontalLayout_21"));
        label_exposure = new QLabel(page_cameracontrol);
        label_exposure->setObjectName(QString::fromUtf8("label_exposure"));
        sizePolicy1.setHeightForWidth(label_exposure->sizePolicy().hasHeightForWidth());
        label_exposure->setSizePolicy(sizePolicy1);
        label_exposure->setFont(font1);
        label_exposure->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_21->addWidget(label_exposure);

        checkBox_exposure = new QCheckBox(page_cameracontrol);
        checkBox_exposure->setObjectName(QString::fromUtf8("checkBox_exposure"));
        checkBox_exposure->setMaximumSize(QSize(60, 20));
        checkBox_exposure->setFont(font1);
        checkBox_exposure->setStyleSheet(QString::fromUtf8("QCheckBox {\n"
"    spacing: 5px;                    /* \346\226\207\345\255\227\344\270\216\346\211\223\345\213\276\346\241\206\344\271\213\351\227\264\347\232\204\351\227\264\350\267\235 */\n"
"	color: rgb(255, 255, 255);\n"
"}\n"
"\n"
""));

        horizontalLayout_21->addWidget(checkBox_exposure);


        verticalLayout_23->addLayout(horizontalLayout_21);

        horizontalLayout_22 = new QHBoxLayout();
        horizontalLayout_22->setObjectName(QString::fromUtf8("horizontalLayout_22"));
        horizontalSlider_exposure = new QSlider(page_cameracontrol);
        horizontalSlider_exposure->setObjectName(QString::fromUtf8("horizontalSlider_exposure"));
        horizontalSlider_exposure->setMinimumSize(QSize(149, 0));
        horizontalSlider_exposure->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_exposure->setOrientation(Qt::Horizontal);

        horizontalLayout_22->addWidget(horizontalSlider_exposure);

        spinBox_exposure = new QSpinBox(page_cameracontrol);
        spinBox_exposure->setObjectName(QString::fromUtf8("spinBox_exposure"));
        spinBox_exposure->setMinimumSize(QSize(50, 25));
        spinBox_exposure->setMaximumSize(QSize(50, 25));
        spinBox_exposure->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_22->addWidget(spinBox_exposure);


        verticalLayout_23->addLayout(horizontalLayout_22);


        verticalLayout_28->addLayout(verticalLayout_23);

        verticalLayout_27 = new QVBoxLayout();
        verticalLayout_27->setObjectName(QString::fromUtf8("verticalLayout_27"));
        horizontalLayout_23 = new QHBoxLayout();
        horizontalLayout_23->setObjectName(QString::fromUtf8("horizontalLayout_23"));
        label_focus = new QLabel(page_cameracontrol);
        label_focus->setObjectName(QString::fromUtf8("label_focus"));
        sizePolicy1.setHeightForWidth(label_focus->sizePolicy().hasHeightForWidth());
        label_focus->setSizePolicy(sizePolicy1);
        label_focus->setFont(font1);
        label_focus->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_23->addWidget(label_focus);

        checkBox_focus = new QCheckBox(page_cameracontrol);
        checkBox_focus->setObjectName(QString::fromUtf8("checkBox_focus"));
        checkBox_focus->setMaximumSize(QSize(60, 20));
        checkBox_focus->setFont(font1);
        checkBox_focus->setStyleSheet(QString::fromUtf8("QCheckBox {\n"
"    spacing: 5px;                    /* \346\226\207\345\255\227\344\270\216\346\211\223\345\213\276\346\241\206\344\271\213\351\227\264\347\232\204\351\227\264\350\267\235 */\n"
"	color: rgb(255, 255, 255);\n"
"}\n"
"\n"
"\n"
"\n"
"\n"
""));

        horizontalLayout_23->addWidget(checkBox_focus);


        verticalLayout_27->addLayout(horizontalLayout_23);

        horizontalLayout_24 = new QHBoxLayout();
        horizontalLayout_24->setObjectName(QString::fromUtf8("horizontalLayout_24"));
        horizontalSlider_focus = new QSlider(page_cameracontrol);
        horizontalSlider_focus->setObjectName(QString::fromUtf8("horizontalSlider_focus"));
        horizontalSlider_focus->setMinimumSize(QSize(149, 0));
        horizontalSlider_focus->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_focus->setOrientation(Qt::Horizontal);

        horizontalLayout_24->addWidget(horizontalSlider_focus);

        spinBox_focus = new QSpinBox(page_cameracontrol);
        spinBox_focus->setObjectName(QString::fromUtf8("spinBox_focus"));
        spinBox_focus->setMinimumSize(QSize(50, 25));
        spinBox_focus->setMaximumSize(QSize(50, 25));
        spinBox_focus->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_24->addWidget(spinBox_focus);


        verticalLayout_27->addLayout(horizontalLayout_24);


        verticalLayout_28->addLayout(verticalLayout_27);

        verticalLayout_24 = new QVBoxLayout();
        verticalLayout_24->setObjectName(QString::fromUtf8("verticalLayout_24"));
        label_zoom = new QLabel(page_cameracontrol);
        label_zoom->setObjectName(QString::fromUtf8("label_zoom"));
        sizePolicy1.setHeightForWidth(label_zoom->sizePolicy().hasHeightForWidth());
        label_zoom->setSizePolicy(sizePolicy1);
        label_zoom->setMinimumSize(QSize(165, 26));
        label_zoom->setMaximumSize(QSize(165, 26));
        label_zoom->setFont(font1);
        label_zoom->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_24->addWidget(label_zoom);

        horizontalLayout_25 = new QHBoxLayout();
        horizontalLayout_25->setObjectName(QString::fromUtf8("horizontalLayout_25"));
        horizontalSlider_zoom = new QSlider(page_cameracontrol);
        horizontalSlider_zoom->setObjectName(QString::fromUtf8("horizontalSlider_zoom"));
        horizontalSlider_zoom->setMinimumSize(QSize(149, 0));
        horizontalSlider_zoom->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_zoom->setOrientation(Qt::Horizontal);

        horizontalLayout_25->addWidget(horizontalSlider_zoom);

        spinBox_zoom = new QSpinBox(page_cameracontrol);
        spinBox_zoom->setObjectName(QString::fromUtf8("spinBox_zoom"));
        spinBox_zoom->setMinimumSize(QSize(50, 25));
        spinBox_zoom->setMaximumSize(QSize(50, 25));
        spinBox_zoom->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_25->addWidget(spinBox_zoom);


        verticalLayout_24->addLayout(horizontalLayout_25);


        verticalLayout_28->addLayout(verticalLayout_24);

        verticalLayout_25 = new QVBoxLayout();
        verticalLayout_25->setObjectName(QString::fromUtf8("verticalLayout_25"));
        label_pan = new QLabel(page_cameracontrol);
        label_pan->setObjectName(QString::fromUtf8("label_pan"));
        sizePolicy1.setHeightForWidth(label_pan->sizePolicy().hasHeightForWidth());
        label_pan->setSizePolicy(sizePolicy1);
        label_pan->setMinimumSize(QSize(165, 26));
        label_pan->setMaximumSize(QSize(165, 26));
        label_pan->setFont(font1);
        label_pan->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_25->addWidget(label_pan);

        horizontalLayout_26 = new QHBoxLayout();
        horizontalLayout_26->setObjectName(QString::fromUtf8("horizontalLayout_26"));
        horizontalSlider_pan = new QSlider(page_cameracontrol);
        horizontalSlider_pan->setObjectName(QString::fromUtf8("horizontalSlider_pan"));
        horizontalSlider_pan->setMinimumSize(QSize(149, 0));
        horizontalSlider_pan->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_pan->setOrientation(Qt::Horizontal);

        horizontalLayout_26->addWidget(horizontalSlider_pan);

        spinBox_pan = new QSpinBox(page_cameracontrol);
        spinBox_pan->setObjectName(QString::fromUtf8("spinBox_pan"));
        spinBox_pan->setMinimumSize(QSize(50, 25));
        spinBox_pan->setMaximumSize(QSize(50, 25));
        spinBox_pan->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_26->addWidget(spinBox_pan);


        verticalLayout_25->addLayout(horizontalLayout_26);


        verticalLayout_28->addLayout(verticalLayout_25);

        verticalLayout_26 = new QVBoxLayout();
        verticalLayout_26->setObjectName(QString::fromUtf8("verticalLayout_26"));
        label_tilt = new QLabel(page_cameracontrol);
        label_tilt->setObjectName(QString::fromUtf8("label_tilt"));
        sizePolicy1.setHeightForWidth(label_tilt->sizePolicy().hasHeightForWidth());
        label_tilt->setSizePolicy(sizePolicy1);
        label_tilt->setMinimumSize(QSize(165, 26));
        label_tilt->setMaximumSize(QSize(165, 26));
        label_tilt->setFont(font1);
        label_tilt->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        verticalLayout_26->addWidget(label_tilt);

        horizontalLayout_27 = new QHBoxLayout();
        horizontalLayout_27->setObjectName(QString::fromUtf8("horizontalLayout_27"));
        horizontalSlider_tilt = new QSlider(page_cameracontrol);
        horizontalSlider_tilt->setObjectName(QString::fromUtf8("horizontalSlider_tilt"));
        horizontalSlider_tilt->setMinimumSize(QSize(149, 0));
        horizontalSlider_tilt->setMaximumSize(QSize(149, 16777215));
        horizontalSlider_tilt->setOrientation(Qt::Horizontal);

        horizontalLayout_27->addWidget(horizontalSlider_tilt);

        spinBox_tilt = new QSpinBox(page_cameracontrol);
        spinBox_tilt->setObjectName(QString::fromUtf8("spinBox_tilt"));
        spinBox_tilt->setMinimumSize(QSize(50, 25));
        spinBox_tilt->setMaximumSize(QSize(50, 25));
        spinBox_tilt->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));

        horizontalLayout_27->addWidget(spinBox_tilt);


        verticalLayout_26->addLayout(horizontalLayout_27);


        verticalLayout_28->addLayout(verticalLayout_26);

        horizontalLayout_28 = new QHBoxLayout();
        horizontalLayout_28->setObjectName(QString::fromUtf8("horizontalLayout_28"));
        pushButton_default2 = new QPushButton(page_cameracontrol);
        pushButton_default2->setObjectName(QString::fromUtf8("pushButton_default2"));
        pushButton_default2->setMinimumSize(QSize(70, 30));
        pushButton_default2->setFont(font1);
        pushButton_default2->setStyleSheet(QString::fromUtf8("QPushButton{  \n"
"	background-color: rgb(255, 255, 255);\n"
"border-radius:5px;      \n"
"	color: rgb(0, 0, 0);\n"
" padding: 2px;\n"
"}\n"
"QPushButton::pressed,QPushButton::checked{\n"
"	background-color: rgb(0, 0, 0);\n"
"	color: rgb(255, 255, 255);\n"
"}\n"
""));

        horizontalLayout_28->addWidget(pushButton_default2);

        horizontalSpacer_6 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_28->addItem(horizontalSpacer_6);


        verticalLayout_28->addLayout(horizontalLayout_28);

        stackedWidget->addWidget(page_cameracontrol);
        page = new QWidget();
        page->setObjectName(QString::fromUtf8("page"));
        layoutWidget = new QWidget(page);
        layoutWidget->setObjectName(QString::fromUtf8("layoutWidget"));
        layoutWidget->setGeometry(QRect(70, 20, 157, 27));
        horizontalLayout_31 = new QHBoxLayout(layoutWidget);
        horizontalLayout_31->setObjectName(QString::fromUtf8("horizontalLayout_31"));
        horizontalLayout_31->setContentsMargins(0, 0, 0, 0);
        label_5 = new QLabel(layoutWidget);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        label_5->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);"));

        horizontalLayout_31->addWidget(label_5);

        timewmark = new QPushButton(layoutWidget);
        timewmark->setObjectName(QString::fromUtf8("timewmark"));
        sizePolicy.setHeightForWidth(timewmark->sizePolicy().hasHeightForWidth());
        timewmark->setSizePolicy(sizePolicy);
        timewmark->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"\n"
"	image: url(:/image/icons/\345\274\200\345\205\263-\345\205\263.png);\n"
"\n"
"\n"
"\n"
"border-style:none;\n"
"\n"
"}\n"
"\n"
"QPushButton::pressed,QPushButton::checked{\n"
"\n"
"	image: url(:/image/icons/\345\274\200\345\205\263-\345\274\200.png);\n"
"\n"
"}"));
        timewmark->setCheckable(true);

        horizontalLayout_31->addWidget(timewmark);

        layoutWidget1 = new QWidget(page);
        layoutWidget1->setObjectName(QString::fromUtf8("layoutWidget1"));
        layoutWidget1->setGeometry(QRect(60, 60, 157, 27));
        horizontalLayout_32 = new QHBoxLayout(layoutWidget1);
        horizontalLayout_32->setObjectName(QString::fromUtf8("horizontalLayout_32"));
        horizontalLayout_32->setContentsMargins(0, 0, 0, 0);
        label_6 = new QLabel(layoutWidget1);
        label_6->setObjectName(QString::fromUtf8("label_6"));

        horizontalLayout_32->addWidget(label_6);

        pushButton_6 = new QPushButton(layoutWidget1);
        pushButton_6->setObjectName(QString::fromUtf8("pushButton_6"));

        horizontalLayout_32->addWidget(pushButton_6);

        layoutWidget2 = new QWidget(page);
        layoutWidget2->setObjectName(QString::fromUtf8("layoutWidget2"));
        layoutWidget2->setGeometry(QRect(70, 110, 144, 55));
        verticalLayout_29 = new QVBoxLayout(layoutWidget2);
        verticalLayout_29->setObjectName(QString::fromUtf8("verticalLayout_29"));
        verticalLayout_29->setContentsMargins(0, 0, 0, 0);
        label_7 = new QLabel(layoutWidget2);
        label_7->setObjectName(QString::fromUtf8("label_7"));

        verticalLayout_29->addWidget(label_7);

        lineEdit = new QLineEdit(layoutWidget2);
        lineEdit->setObjectName(QString::fromUtf8("lineEdit"));

        verticalLayout_29->addWidget(lineEdit);

        label_8 = new QLabel(page);
        label_8->setObjectName(QString::fromUtf8("label_8"));
        label_8->setGeometry(QRect(80, 180, 67, 17));
        pushButton_7 = new QPushButton(page);
        pushButton_7->setObjectName(QString::fromUtf8("pushButton_7"));
        pushButton_7->setGeometry(QRect(70, 220, 89, 25));
        stackedWidget->addWidget(page);

        verticalLayout_11->addWidget(stackedWidget);

        verticalSpacer = new QSpacerItem(20, 20, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_11->addItem(verticalSpacer);

        verticalLayout_10 = new QVBoxLayout();
        verticalLayout_10->setObjectName(QString::fromUtf8("verticalLayout_10"));
        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        p_1 = new QPushButton(widget_2);
        p_1->setObjectName(QString::fromUtf8("p_1"));
        sizePolicy2.setHeightForWidth(p_1->sizePolicy().hasHeightForWidth());
        p_1->setSizePolicy(sizePolicy2);
        p_1->setMinimumSize(QSize(40, 30));
        p_1->setMaximumSize(QSize(40, 30));
        p_1->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}"));
        p_1->setCheckable(true);
        p_1->setAutoExclusive(true);

        horizontalLayout_3->addWidget(p_1);

        p_2 = new QPushButton(widget_2);
        p_2->setObjectName(QString::fromUtf8("p_2"));
        sizePolicy2.setHeightForWidth(p_2->sizePolicy().hasHeightForWidth());
        p_2->setSizePolicy(sizePolicy2);
        p_2->setMinimumSize(QSize(40, 30));
        p_2->setMaximumSize(QSize(40, 30));
        p_2->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}"));
        p_2->setCheckable(true);
        p_2->setAutoExclusive(true);

        horizontalLayout_3->addWidget(p_2);

        p_3 = new QPushButton(widget_2);
        p_3->setObjectName(QString::fromUtf8("p_3"));
        sizePolicy2.setHeightForWidth(p_3->sizePolicy().hasHeightForWidth());
        p_3->setSizePolicy(sizePolicy2);
        p_3->setMinimumSize(QSize(40, 30));
        p_3->setMaximumSize(QSize(40, 30));
        p_3->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}"));
        p_3->setCheckable(true);
        p_3->setAutoExclusive(true);

        horizontalLayout_3->addWidget(p_3);

        p_4 = new QPushButton(widget_2);
        p_4->setObjectName(QString::fromUtf8("p_4"));
        sizePolicy2.setHeightForWidth(p_4->sizePolicy().hasHeightForWidth());
        p_4->setSizePolicy(sizePolicy2);
        p_4->setMinimumSize(QSize(40, 30));
        p_4->setMaximumSize(QSize(40, 30));
        p_4->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}"));
        p_4->setCheckable(true);
        p_4->setAutoExclusive(true);

        horizontalLayout_3->addWidget(p_4);


        verticalLayout_10->addLayout(horizontalLayout_3);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        p1_2 = new QPushButton(widget_2);
        p1_2->setObjectName(QString::fromUtf8("p1_2"));
        sizePolicy2.setHeightForWidth(p1_2->sizePolicy().hasHeightForWidth());
        p1_2->setSizePolicy(sizePolicy2);
        p1_2->setMinimumSize(QSize(40, 30));
        p1_2->setMaximumSize(QSize(40, 30));
        p1_2->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}"));
        p1_2->setCheckable(true);
        p1_2->setAutoExclusive(true);

        horizontalLayout_4->addWidget(p1_2);

        p3_4 = new QPushButton(widget_2);
        p3_4->setObjectName(QString::fromUtf8("p3_4"));
        sizePolicy2.setHeightForWidth(p3_4->sizePolicy().hasHeightForWidth());
        p3_4->setSizePolicy(sizePolicy2);
        p3_4->setMinimumSize(QSize(40, 30));
        p3_4->setMaximumSize(QSize(40, 30));
        p3_4->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"\n"
"border:none;\n"
"}\n"
"QPushButton:hover{\n"
"	\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgb(255, 255, 255);\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgb(90, 90, 90);\n"
"border: 1px solid rgb(0, 170, 255);\n"
"}"));
        p3_4->setCheckable(true);
        p3_4->setAutoExclusive(true);

        horizontalLayout_4->addWidget(p3_4);

        p1234 = new QPushButton(widget_2);
        p1234->setObjectName(QString::fromUtf8("p1234"));
        sizePolicy2.setHeightForWidth(p1234->sizePolicy().hasHeightForWidth());
        p1234->setSizePolicy(sizePolicy2);
        p1234->setMinimumSize(QSize(35, 35));
        p1234->setMaximumSize(QSize(35, 35));
        p1234->setStyleSheet(QString::fromUtf8("QPushButton{\n"
"	image: url(:/image/icons/4\350\267\257.png);\n"
"border-style:none;\n"
"border: 2px solid rgb(0, 170, 255);\n"
"padding:-5px;\n"
"background:transparent;\n"
"\n"
"}\n"
"QPushButton::pressed,QPushButton::checked{\n"
"	image: url(:/image/icons/4\350\267\257(default).png);\n"
" padding: -5px;\n"
"background:transparent;\n"
"}\n"
"QPushButton:hover{\n"
"	image: url(:/image/icons/4\350\267\257(default).png);\n"
"}"));
        p1234->setCheckable(true);
        p1234->setAutoExclusive(true);

        horizontalLayout_4->addWidget(p1234);


        verticalLayout_10->addLayout(horizontalLayout_4);


        verticalLayout_11->addLayout(verticalLayout_10);

        verticalSpacer_2 = new QSpacerItem(20, 50, QSizePolicy::Minimum, QSizePolicy::Fixed);

        verticalLayout_11->addItem(verticalSpacer_2);


        gridLayout_3->addLayout(verticalLayout_11, 0, 0, 1, 1);


        gridLayout_4->addWidget(widget_2, 1, 0, 1, 1);

        video = new QWidget(centralwidget);
        video->setObjectName(QString::fromUtf8("video"));
        video->setStyleSheet(QString::fromUtf8(""));
        gridLayout = new QGridLayout(video);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        gridLayout->setContentsMargins(0, 0, 0, 0);
        stackedWidget_2 = new QStackedWidget(video);
        stackedWidget_2->setObjectName(QString::fromUtf8("stackedWidget_2"));
        page_2 = new QWidget();
        page_2->setObjectName(QString::fromUtf8("page_2"));
        gridLayout_2 = new QGridLayout(page_2);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        gridLayout_2->setContentsMargins(0, 0, 0, 0);
        v1 = new QWidget(page_2);
        v1->setObjectName(QString::fromUtf8("v1"));
        v1->setStyleSheet(QString::fromUtf8(""));

        gridLayout_2->addWidget(v1, 0, 0, 1, 1);

        stackedWidget_2->addWidget(page_2);
        page_3 = new QWidget();
        page_3->setObjectName(QString::fromUtf8("page_3"));
        gridLayout_7 = new QGridLayout(page_3);
        gridLayout_7->setObjectName(QString::fromUtf8("gridLayout_7"));
        gridLayout_7->setContentsMargins(0, 0, 0, 0);
        v2 = new QWidget(page_3);
        v2->setObjectName(QString::fromUtf8("v2"));

        gridLayout_7->addWidget(v2, 0, 0, 1, 1);

        stackedWidget_2->addWidget(page_3);
        page_4 = new QWidget();
        page_4->setObjectName(QString::fromUtf8("page_4"));
        gridLayout_8 = new QGridLayout(page_4);
        gridLayout_8->setObjectName(QString::fromUtf8("gridLayout_8"));
        gridLayout_8->setContentsMargins(0, 0, 0, 0);
        v3 = new QWidget(page_4);
        v3->setObjectName(QString::fromUtf8("v3"));

        gridLayout_8->addWidget(v3, 0, 0, 1, 1);

        stackedWidget_2->addWidget(page_4);
        page_5 = new QWidget();
        page_5->setObjectName(QString::fromUtf8("page_5"));
        gridLayout_9 = new QGridLayout(page_5);
        gridLayout_9->setObjectName(QString::fromUtf8("gridLayout_9"));
        gridLayout_9->setContentsMargins(0, 0, 0, 0);
        v4 = new QWidget(page_5);
        v4->setObjectName(QString::fromUtf8("v4"));

        gridLayout_9->addWidget(v4, 0, 0, 1, 1);

        stackedWidget_2->addWidget(page_5);
        page_6 = new QWidget();
        page_6->setObjectName(QString::fromUtf8("page_6"));
        gridLayout_10 = new QGridLayout(page_6);
        gridLayout_10->setSpacing(0);
        gridLayout_10->setObjectName(QString::fromUtf8("gridLayout_10"));
        gridLayout_10->setContentsMargins(0, 0, 0, 0);
        v5_2 = new QWidget(page_6);
        v5_2->setObjectName(QString::fromUtf8("v5_2"));

        gridLayout_10->addWidget(v5_2, 0, 1, 1, 1);

        v5_1 = new QWidget(page_6);
        v5_1->setObjectName(QString::fromUtf8("v5_1"));

        gridLayout_10->addWidget(v5_1, 0, 0, 1, 1);

        stackedWidget_2->addWidget(page_6);
        page_7 = new QWidget();
        page_7->setObjectName(QString::fromUtf8("page_7"));
        gridLayout_11 = new QGridLayout(page_7);
        gridLayout_11->setSpacing(0);
        gridLayout_11->setObjectName(QString::fromUtf8("gridLayout_11"));
        gridLayout_11->setContentsMargins(0, 0, 0, 0);
        v6_1 = new QWidget(page_7);
        v6_1->setObjectName(QString::fromUtf8("v6_1"));

        gridLayout_11->addWidget(v6_1, 0, 0, 1, 1);

        v6_2 = new QWidget(page_7);
        v6_2->setObjectName(QString::fromUtf8("v6_2"));

        gridLayout_11->addWidget(v6_2, 0, 1, 1, 1);

        stackedWidget_2->addWidget(page_7);
        page_8 = new QWidget();
        page_8->setObjectName(QString::fromUtf8("page_8"));
        gridLayout_12 = new QGridLayout(page_8);
        gridLayout_12->setSpacing(0);
        gridLayout_12->setObjectName(QString::fromUtf8("gridLayout_12"));
        gridLayout_12->setContentsMargins(0, 0, 0, 0);
        v7_1 = new QWidget(page_8);
        v7_1->setObjectName(QString::fromUtf8("v7_1"));

        gridLayout_12->addWidget(v7_1, 0, 0, 1, 1);

        v7_2 = new QWidget(page_8);
        v7_2->setObjectName(QString::fromUtf8("v7_2"));

        gridLayout_12->addWidget(v7_2, 0, 1, 1, 1);

        v7_3 = new QWidget(page_8);
        v7_3->setObjectName(QString::fromUtf8("v7_3"));

        gridLayout_12->addWidget(v7_3, 1, 0, 1, 1);

        v7_4 = new QWidget(page_8);
        v7_4->setObjectName(QString::fromUtf8("v7_4"));

        gridLayout_12->addWidget(v7_4, 1, 1, 1, 1);

        stackedWidget_2->addWidget(page_8);

        gridLayout->addWidget(stackedWidget_2, 0, 0, 1, 1);


        gridLayout_4->addWidget(video, 1, 1, 1, 1);

        MainWindow->setCentralWidget(centralwidget);

        retranslateUi(MainWindow);
        QObject::connect(pushButton_fullscreen, SIGNAL(clicked()), MainWindow, SLOT(toggleVideoFullscreen()));
        QObject::connect(comboBox_cameradev, SIGNAL(activated(int)), MainWindow, SLOT(select_video(int)));
        QObject::connect(comboBox_videoformat, SIGNAL(activated(int)), MainWindow, SLOT(select_format(int)));
        QObject::connect(comboBox_fbl, SIGNAL(activated(int)), MainWindow, SLOT(select_resolution(int)));
        QObject::connect(videoSet, SIGNAL(clicked()), MainWindow, SLOT(VideoSetPage()));
        QObject::connect(pictureSet, SIGNAL(clicked()), MainWindow, SLOT(PictureSetPage()));
        QObject::connect(cameraControl, SIGNAL(clicked()), MainWindow, SLOT(CameraControlPage()));
        QObject::connect(textSet, SIGNAL(clicked()), MainWindow, SLOT(TextSetPage()));
        QObject::connect(horizontalSlider_brightness, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_brightness()));
        QObject::connect(spinBox_brightness, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_brightness()));
        QObject::connect(horizontalSlider_contrast, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_contrast()));
        QObject::connect(spinBox_contrast, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_contrast()));
        QObject::connect(horizontalSlider_saturation, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_saturation()));
        QObject::connect(spinBox_saturation, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_saturation()));
        QObject::connect(horizontalSlider_hue, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_hue()));
        QObject::connect(spinBox_hue, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_hue()));
        QObject::connect(horizontalSlider_sharpness, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_sharpness()));
        QObject::connect(spinBox_sharpness, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_sharpness()));
        QObject::connect(horizontalSlider_gamma, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_gamma()));
        QObject::connect(spinBox_gamma, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_gamma()));
        QObject::connect(horizontalSlider_backlightcompensation, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_backlightcompensation()));
        QObject::connect(spinBox_backlightcompensation, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_backlightcompensation()));
        QObject::connect(horizontalSlider_gain, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_gain()));
        QObject::connect(spinBox_gain, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_gain()));
        QObject::connect(horizontalSlider_whitebalance, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_whitebalance()));
        QObject::connect(spinBox_whitebalance, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_whitebalance()));
        QObject::connect(horizontalSlider_exposure, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_exposure()));
        QObject::connect(spinBox_exposure, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_exposure()));
        QObject::connect(checkBox_exposure, SIGNAL(clicked()), MainWindow, SLOT(select_exposure_auto()));
        QObject::connect(checkBox_whitebalance, SIGNAL(clicked()), MainWindow, SLOT(select_whitebalance_auto()));
        QObject::connect(horizontalSlider_focus, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_focus()));
        QObject::connect(spinBox_focus, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_focus()));
        QObject::connect(checkBox_focus, SIGNAL(clicked()), MainWindow, SLOT(select_focus_auto()));
        QObject::connect(horizontalSlider_zoom, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_zoom()));
        QObject::connect(spinBox_zoom, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_zoom()));
        QObject::connect(horizontalSlider_pan, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_pan()));
        QObject::connect(spinBox_pan, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_pan()));
        QObject::connect(horizontalSlider_tilt, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_tilt()));
        QObject::connect(spinBox_tilt, SIGNAL(valueChanged(int)), MainWindow, SLOT(select_tilt()));
        QObject::connect(pushButton_default1, SIGNAL(clicked()), MainWindow, SLOT(set_default1()));
        QObject::connect(pushButton_default2, SIGNAL(clicked()), MainWindow, SLOT(set_default2()));
        QObject::connect(p1, SIGNAL(clicked()), MainWindow, SLOT(Signals1()));
        QObject::connect(p2, SIGNAL(clicked()), MainWindow, SLOT(Signals2()));
        QObject::connect(p3, SIGNAL(clicked()), MainWindow, SLOT(Signals3()));
        QObject::connect(p4, SIGNAL(clicked()), MainWindow, SLOT(Signals4()));
        QObject::connect(p_1, SIGNAL(clicked()), MainWindow, SLOT(Signals1()));
        QObject::connect(p_2, SIGNAL(clicked()), MainWindow, SLOT(Signals2()));
        QObject::connect(p_3, SIGNAL(clicked()), MainWindow, SLOT(Signals3()));
        QObject::connect(p_4, SIGNAL(clicked()), MainWindow, SLOT(Signals4()));
        QObject::connect(p1_2, SIGNAL(clicked()), MainWindow, SLOT(Signals5()));
        QObject::connect(p3_4, SIGNAL(clicked()), MainWindow, SLOT(Signals6()));
        QObject::connect(p1234, SIGNAL(clicked()), MainWindow, SLOT(Signals7()));
        QObject::connect(recordingSet, SIGNAL(clicked()), MainWindow, SLOT(videoset()));
        QObject::connect(fileManage, SIGNAL(clicked()), MainWindow, SLOT(filemanage()));
        QObject::connect(closeButton, SIGNAL(clicked()), MainWindow, SLOT(onCloseButtonClicked()));
        QObject::connect(pushButton_max, SIGNAL(clicked()), MainWindow, SLOT(onMaximizeClicked()));
        QObject::connect(pushButton_min, SIGNAL(clicked()), MainWindow, SLOT(onMinimizeClicked()));
        QObject::connect(systemSet, SIGNAL(clicked()), MainWindow, SLOT(systemset()));
        QObject::connect(checkBox_audio, SIGNAL(toggled(bool)), MainWindow, SLOT(isRecordAudio(bool)));
        QObject::connect(comboBox_audiodev, SIGNAL(activated(int)), MainWindow, SLOT(select_audio(int)));
        QObject::connect(timewmark, SIGNAL(clicked()), MainWindow, SLOT(TimeWatermark()));
        QObject::connect(pushButton, SIGNAL(clicked()), MainWindow, SLOT(aboutus()));

        stackedWidget->setCurrentIndex(0);
        stackedWidget_2->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "MainWindow", nullptr));
        label_mainname->setText(QCoreApplication::translate("MainWindow", "<html><head/><body><p align=\"justify\"><span style=\" font-weight:700; font-style:italic;\">Video Capture</span></p></body></html>", nullptr));
        pushButton->setText(QCoreApplication::translate("MainWindow", "\345\205\263\344\272\216\346\210\221\344\273\254", nullptr));
#if QT_CONFIG(tooltip)
        recordingSet->setToolTip(QCoreApplication::translate("MainWindow", "\345\275\225\345\203\217\350\256\276\347\275\256", nullptr));
#endif // QT_CONFIG(tooltip)
        recordingSet->setText(QString());
#if QT_CONFIG(tooltip)
        fileManage->setToolTip(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266\347\256\241\347\220\206", nullptr));
#endif // QT_CONFIG(tooltip)
        fileManage->setText(QString());
#if QT_CONFIG(tooltip)
        systemSet->setToolTip(QCoreApplication::translate("MainWindow", "\347\263\273\347\273\237\350\256\276\347\275\256", nullptr));
#endif // QT_CONFIG(tooltip)
        systemSet->setText(QString());
        pushButton_fullscreen->setText(QString());
        pushButton_min->setText(QString());
        pushButton_max->setText(QString());
        closeButton->setText(QString());
        label_cpu->setText(QString());
        label_gpu->setText(QString());
        label_memory->setText(QString());
        label_time->setText(QString());
#if QT_CONFIG(tooltip)
        videoSet->setToolTip(QCoreApplication::translate("MainWindow", "\350\247\206\351\242\221\350\256\276\347\275\256", nullptr));
#endif // QT_CONFIG(tooltip)
        videoSet->setText(QString());
#if QT_CONFIG(tooltip)
        pictureSet->setToolTip(QCoreApplication::translate("MainWindow", "\345\233\276\345\203\217\350\256\276\347\275\256", nullptr));
#endif // QT_CONFIG(tooltip)
        pictureSet->setText(QString());
#if QT_CONFIG(tooltip)
        cameraControl->setToolTip(QCoreApplication::translate("MainWindow", "\347\233\270\346\234\272\346\216\247\345\210\266", nullptr));
#endif // QT_CONFIG(tooltip)
        cameraControl->setText(QString());
#if QT_CONFIG(tooltip)
        textSet->setToolTip(QCoreApplication::translate("MainWindow", "\346\226\207\345\255\227\350\256\276\347\275\256", nullptr));
#endif // QT_CONFIG(tooltip)
        textSet->setText(QString());
        label_xinhaoy->setText(QCoreApplication::translate("MainWindow", "\344\277\241\345\217\267\346\272\220", nullptr));
        p1->setText(QCoreApplication::translate("MainWindow", "1", nullptr));
        p2->setText(QCoreApplication::translate("MainWindow", "2", nullptr));
        p3->setText(QCoreApplication::translate("MainWindow", "3", nullptr));
        p4->setText(QCoreApplication::translate("MainWindow", "4", nullptr));
        label_cameradev->setText(QCoreApplication::translate("MainWindow", "\347\233\270\346\234\272\350\256\276\345\244\207", nullptr));
        label_videoformat->setText(QCoreApplication::translate("MainWindow", "\350\247\206\351\242\221\346\240\274\345\274\217", nullptr));
        label_fbl->setText(QCoreApplication::translate("MainWindow", "\345\210\206\350\276\250\347\216\207", nullptr));
        label_audiodev->setText(QCoreApplication::translate("MainWindow", "\351\237\263\351\242\221\350\256\276\345\244\207", nullptr));
        checkBox_audio->setText(QCoreApplication::translate("MainWindow", "\345\274\200\345\220\257\345\275\225\351\237\263", nullptr));
        label_brightness->setText(QCoreApplication::translate("MainWindow", "\344\272\256\345\272\246", nullptr));
        label_contrast->setText(QCoreApplication::translate("MainWindow", "\345\257\271\346\257\224\345\272\246", nullptr));
        label_saturation->setText(QCoreApplication::translate("MainWindow", "\351\245\261\345\222\214\345\272\246", nullptr));
        label_hue->setText(QCoreApplication::translate("MainWindow", "\350\211\262\350\260\203", nullptr));
        label_sharpness->setText(QCoreApplication::translate("MainWindow", "\346\270\205\346\231\260\345\272\246", nullptr));
        label_gamma->setText(QCoreApplication::translate("MainWindow", "\344\274\275\347\216\233", nullptr));
        label_backlightcompensation->setText(QCoreApplication::translate("MainWindow", "\351\200\206\345\205\211\345\257\271\346\257\224", nullptr));
        label_gain->setText(QCoreApplication::translate("MainWindow", "\345\242\236\347\233\212", nullptr));
        label_whitebalance->setText(QCoreApplication::translate("MainWindow", "\347\231\275\345\271\263\350\241\241", nullptr));
        checkBox_whitebalance->setText(QCoreApplication::translate("MainWindow", "\350\207\252\345\212\250", nullptr));
        pushButton_default1->setText(QCoreApplication::translate("MainWindow", "\351\273\230\350\256\244\345\200\274", nullptr));
        label_exposure->setText(QCoreApplication::translate("MainWindow", "\346\233\235\345\205\211", nullptr));
        checkBox_exposure->setText(QCoreApplication::translate("MainWindow", "\350\207\252\345\212\250", nullptr));
        label_focus->setText(QCoreApplication::translate("MainWindow", "\347\204\246\347\202\271", nullptr));
        checkBox_focus->setText(QCoreApplication::translate("MainWindow", "\350\207\252\345\212\250", nullptr));
        label_zoom->setText(QCoreApplication::translate("MainWindow", "\347\274\251\346\224\276", nullptr));
        label_pan->setText(QCoreApplication::translate("MainWindow", "\345\205\250\346\231\257", nullptr));
        label_tilt->setText(QCoreApplication::translate("MainWindow", "\345\200\276\346\226\234", nullptr));
        pushButton_default2->setText(QCoreApplication::translate("MainWindow", "\351\273\230\350\256\244\345\200\274", nullptr));
        label_5->setText(QCoreApplication::translate("MainWindow", "\346\227\266\351\227\264\345\217\240\345\212\240", nullptr));
        timewmark->setText(QString());
        label_6->setText(QCoreApplication::translate("MainWindow", "\346\226\207\345\255\227\345\217\240\345\212\240", nullptr));
        pushButton_6->setText(QCoreApplication::translate("MainWindow", "PushButton", nullptr));
        label_7->setText(QCoreApplication::translate("MainWindow", "\346\226\207\345\255\227\345\206\205\345\256\271", nullptr));
        label_8->setText(QCoreApplication::translate("MainWindow", "\346\226\207\345\255\227\344\275\215\347\275\256", nullptr));
        pushButton_7->setText(QCoreApplication::translate("MainWindow", "\347\241\256\350\256\244\345\217\240\345\212\240", nullptr));
        p_1->setText(QCoreApplication::translate("MainWindow", "1", nullptr));
        p_2->setText(QCoreApplication::translate("MainWindow", "2", nullptr));
        p_3->setText(QCoreApplication::translate("MainWindow", "3", nullptr));
        p_4->setText(QCoreApplication::translate("MainWindow", "4", nullptr));
        p1_2->setText(QCoreApplication::translate("MainWindow", "1|2", nullptr));
        p3_4->setText(QCoreApplication::translate("MainWindow", "3|4", nullptr));
        p1234->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
