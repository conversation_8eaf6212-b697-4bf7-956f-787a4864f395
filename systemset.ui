<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Systemset</class>
 <widget class="QDialog" name="Systemset">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>303</width>
    <height>404</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QGridLayout" name="gridLayout_3">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QWidget" name="widget" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>244</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>24</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(120, 120, 120);</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QLabel" name="label">
          <property name="font">
           <font>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>系统设置</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton">
          <property name="minimumSize">
           <size>
            <width>25</width>
            <height>24</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>25</width>
            <height>24</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
	image: url(:/image/关闭.png);
border-style:none;
padding:5px;

background:transparent;
}
QPushButton:hover{
	background-color: rgb(239, 41, 41);
}</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QWidget" name="widget_2" native="true">
     <property name="styleSheet">
      <string notr="true">background-color: rgb(90, 90, 90);</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <property name="leftMargin">
       <number>3</number>
      </property>
      <property name="topMargin">
       <number>10</number>
      </property>
      <property name="rightMargin">
       <number>3</number>
      </property>
      <property name="bottomMargin">
       <number>3</number>
      </property>
      <property name="verticalSpacing">
       <number>12</number>
      </property>
      <item row="0" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="sizeConstraint">
         <enum>QLayout::SetFixedSize</enum>
        </property>
        <item>
         <widget class="QLabel" name="language_label">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>25</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>25</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>11</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>语言设置：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="comboBox">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>26</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>130</width>
            <height>25</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QComboBox {
    font: 75 9pt 'Arial';
    color: rgb(0, 0, 0);
    background-color: rgb(255, 255, 255);
    padding: 1px 15px 1px 10px;
    border: none;
    border-radius: 5px 5px 0px 0px;
}
QComboBox::drop-down {
	image: url(:/image/icons/combox_ico_drop.png);
    padding: 3px 6px 1px 3px;
    width: 18px;
}
QComboBox QAbstractItemView {
    outline: none;
    border: 1px solid white;
    color: rgb(0, 0, 0);
    background-color: rgb(255, 255, 255);
    selection-background-color: rgb(90, 90, 90);
}
QAbstractItemView::item {
    height: 30px;
}
QComboBox QAbstractItemView::item:hover {
    color: #FFFFFF;
    background-color: rgb(90, 90, 90);
}
QComboBox:disabled {
    background-color: rgb(188, 188, 188);
    color: rgb(122, 122, 122);
}</string>
          </property>
          <item>
           <property name="text">
            <string>中文</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>English</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>العربية</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>繁体中文</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Français</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Deutsch</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Italiano</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>日本語</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>한국어</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Português</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Русский язык</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Español</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Türkçe</string>
           </property>
          </item>
         </widget>
        </item>
       </layout>
      </item>
      <item row="1" column="0">
       <layout class="QVBoxLayout" name="verticalLayout">
        <property name="spacing">
         <number>12</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_12">
          <item>
           <widget class="QLabel" name="label_9">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>25</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>25</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>11</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(255, 255, 255);</string>
            </property>
            <property name="text">
             <string>时间设置：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_time">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <pointsize>11</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(255, 255, 255);</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QGridLayout" name="gridLayout">
          <item row="0" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_5">
            <item>
             <widget class="QSpinBox" name="spinBox_year">
              <property name="minimumSize">
               <size>
                <width>56</width>
                <height>30</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>30</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(255, 255, 255);</string>
              </property>
              <property name="minimum">
               <number>2025</number>
              </property>
              <property name="maximum">
               <number>3000</number>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_2">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>年</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="0" column="1">
           <layout class="QHBoxLayout" name="horizontalLayout_7">
            <item>
             <widget class="QComboBox" name="comboBox_month">
              <property name="minimumSize">
               <size>
                <width>56</width>
                <height>30</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>30</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(255, 255, 255);</string>
              </property>
              <item>
               <property name="text">
                <string>01</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>02</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>03</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>04</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>05</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>06</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>07</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>08</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>09</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>10</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>11</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>12</string>
               </property>
              </item>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_5">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>月</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="0" column="2">
           <layout class="QHBoxLayout" name="horizontalLayout_6">
            <item>
             <widget class="QComboBox" name="comboBox_day">
              <property name="minimumSize">
               <size>
                <width>56</width>
                <height>30</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>30</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(255, 255, 255);</string>
              </property>
              <item>
               <property name="text">
                <string>01</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>02</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>03</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>04</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>05</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>06</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>07</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>08</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>09</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>10</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>11</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>12</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>13</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>14</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>15</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>16</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>17</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>18</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>19</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>20</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>21</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>22</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>23</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>24</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>25</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>26</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>27</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>28</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>29</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>30</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>31</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string/>
               </property>
              </item>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_4">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>日</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="1" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_8">
            <item>
             <widget class="QSpinBox" name="spinBox_hour">
              <property name="minimumSize">
               <size>
                <width>56</width>
                <height>30</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>30</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(255, 255, 255);</string>
              </property>
              <property name="maximum">
               <number>23</number>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_6">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>时</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="1" column="1">
           <layout class="QHBoxLayout" name="horizontalLayout_9">
            <item>
             <widget class="QSpinBox" name="spinBox_min">
              <property name="minimumSize">
               <size>
                <width>56</width>
                <height>30</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>30</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(255, 255, 255);</string>
              </property>
              <property name="maximum">
               <number>59</number>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_7">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>分</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="1" column="2">
           <layout class="QHBoxLayout" name="horizontalLayout_10">
            <item>
             <widget class="QSpinBox" name="spinBox_second">
              <property name="minimumSize">
               <size>
                <width>56</width>
                <height>30</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>56</width>
                <height>30</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(255, 255, 255);</string>
              </property>
              <property name="maximum">
               <number>59</number>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_8">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>秒</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_11">
          <item>
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_2">
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>25</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>80</width>
              <height>25</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(255, 255, 255);</string>
            </property>
            <property name="text">
             <string>Change</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButton_OK">
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>25</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>80</width>
              <height>25</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">
background-color: rgb(255, 255, 255);</string>
            </property>
            <property name="text">
             <string>OK</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item row="2" column="0">
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="3" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label_3">
          <property name="styleSheet">
           <string notr="true">color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>版本信息：</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>pushButton</sender>
   <signal>clicked()</signal>
   <receiver>Systemset</receiver>
   <slot>closewindow()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>267</x>
     <y>9</y>
    </hint>
    <hint type="destinationlabel">
     <x>232</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>comboBox</sender>
   <signal>activated(int)</signal>
   <receiver>Systemset</receiver>
   <slot>combo_language(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>189</x>
     <y>49</y>
    </hint>
    <hint type="destinationlabel">
     <x>268</x>
     <y>41</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton_OK</sender>
   <signal>clicked()</signal>
   <receiver>Systemset</receiver>
   <slot>set_time()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>263</x>
     <y>223</y>
    </hint>
    <hint type="destinationlabel">
     <x>315</x>
     <y>183</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton_2</sender>
   <signal>clicked()</signal>
   <receiver>Systemset</receiver>
   <slot>changetime()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>149</x>
     <y>217</y>
    </hint>
    <hint type="destinationlabel">
     <x>-42</x>
     <y>207</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>closewindow()</slot>
  <slot>combo_language(int)</slot>
  <slot>set_time()</slot>
  <slot>changetime()</slot>
 </slots>
</ui>
