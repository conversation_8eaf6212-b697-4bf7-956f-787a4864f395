#ifndef CAMERASTREAM_H
#define CAMERASTREAM_H

#include <QObject>
#include <QImage>
#include <QString>
#include <QVariantMap>
#include <QSize>
#include <QTimer>
#include <QDateTime>
#include <QDir>
#include <gst/gst.h>
#include <QMutex>
#include <gst/app/gstappsink.h>

// 前向声明
class MainWindow;

class CameraStream : public QObject
{
    Q_OBJECT

public:
    // 构造函数和析构函数
    explicit CameraStream(QObject *parent = nullptr);
    ~CameraStream();

    // 实例成员变量 - 替代原来的静态成员变量
    char m_recording_base_path[512]; // 录制基本路径
    bool m_path_initialized;        // 路径是否已初始化


    // 修改为正确的format-location信号回调函数签名
    static gchar* global_format_location_callback(GstElement *splitmux, guint fragment_id, gpointer user_data);

    // 初始化GStreamer管道
    bool initializeGStreamer(const QString &devicePath,
                             const QVariantMap &format,
                             const QVariantMap &resolution);


    // 清理GStreamer资源
    void cleanupGStreamer();

    // 动态更新时间水印状态
    // bool updateTimeWatermark();

    // 获取当前设备路径
    QString getCurrentDevicePath() const { return currentDevicePath; }

    // 获取和设置缓存的photo_sink
    GstElement* getCachedPhotoSink() const { return m_cached_photo_sink; }
    void setCachedPhotoSink(GstElement* sink) { m_cached_photo_sink = sink; }

    // 获取和设置缓存的录像分支
    GstElement* getCachedRecordBranch() const { return m_cached_record_branch; }
    void setCachedRecordBranch(GstElement* sink) { m_cached_record_branch = sink; }

    int GetRecordBitRate(int width, int height, int fps, int quality);

    int m_checkFileCounter;

    int channelstream;
    int Quality; //录像质量
    QString recordingpath; //录像路径
    QString recordingfilename; //录像文件名
    QString currentSegmentFilename; //当前分段录像文件名

    int segmentedSize;//录像文件大小
    bool isRecording = false; // 添加变量标记是否正在录像
    QString recordingfilepat;
    bool m_currentWatermarkState = false; // 当前时间水印状态

    // 移到public区域，使回调函数可以访问
    unsigned int m_recordingSegmentCounter = 1;// 分段计数器

    QDateTime recordingStartTime; // 录像开始时间
    QDateTime lastSegmentTime;    // 上一个分段的时间，用于时间跟踪
    qint64 totalRecordedTime = 0; // 总录制时间（毫秒）
    qint64 expectedSegmentDuration = 0; // 期望的分段时长（毫秒）
    QDateTime storageLimitStartTime; // 存储限制检查开始时间

    void pausePreview();  // 暂停预览（替换为 fakesink）
    void resumePreview(); // 恢复预览（重新启用 waylandsink）
    void setPreviewSize(int channel);
    void setfullPreviewSize(int channel);

    // 动态控制时间水印
    void updateTimeWatermark(); // 根据timewatermark状态动态更新时间水印
    void restoreTimeWatermarkOnInit(); // 初始化后恢复时间水印状态

    // 预览状态管理
    bool isPreviewPaused() const { return m_isPreviewPaused; }
    void setPreviewPaused(bool paused) { m_isPreviewPaused = paused; }

private:
    MainWindow* m_mainWindow;  // 保存MainWindow指针

    // 移除了m_recordingSegmentCounter
    QMutex m_recordingMutex;


    bool m_isPreviewPaused = false;    // 跟踪预览是否暂停

    bool m_photo_sink_destroying = false;      // 标记photo_sink是否正在销毁中



signals:
    // 发送新帧信号
    void newFrameReady(const QImage &frame);

    // 发送GStreamer错误信号
    void gstreamerErrorOccurred(const QString &errorMessage, const QString &errorDetails, int channelId);

private:
    // 录像相关元素
    GstElement *tee = nullptr;        // 流分流器
    GstElement *queue_record = nullptr;
    GstElement *encoder = nullptr;
    GstElement *h264parse = nullptr;
    GstElement *muxer = nullptr;
    GstElement *filesink = nullptr;
    GstElement *appsink = nullptr;

    // 缓存的录像分支
    GstElement *m_cached_record_branch = nullptr;
    GstPad *m_record_tee_pad = nullptr;
    GstPad *m_record_queue_pad = nullptr;

    // 音频相关元素
    GstElement *m_audio_source = nullptr;      // alsasrc
    GstElement *m_audio_convert = nullptr;     // audioconvert
    GstElement *m_audio_encoder = nullptr;     // voaacenc
    GstElement *m_audio_parser = nullptr;      // aacparse
    GstElement *m_audio_queue = nullptr;       // queue for audio
    QString m_audio_device_path;               // 音频设备路径

    // 预览分支元素（用于水印隔离）
    GstElement *m_preview_convert = nullptr;      // 预览分支的videoconvert
    GstElement *m_preview_capsfilter = nullptr;   // 预览分支的capsfilter

    // 时间水印元素
    GstElement *m_timeoverlay = nullptr;          // 全局时间水印元素
    GstElement *m_convert = nullptr;              // videoconvert元素（用于时间水印插入点）

    // 拍照相关
    GstElement *m_cached_photo_sink = nullptr;  // 缓存的photo_sink
    QDateTime m_last_photo_time;               // 上次拍照时间
    QTimer *m_photo_sink_timer = nullptr;      // 延迟销毁photo_sink的定时器
    GstElement* m_photo_sink = nullptr;   // 保存拍照分支的 appsink，拍完再统一销毁

    bool m_photoBranchCreated = false;

private slots:
    // 延迟销毁photo_sink的槽函数
    void destroyPhotoSinkDelayed();

public slots:
    // 处理新帧
    // void onNewFrame(const QImage &frame);
    void startRecording();
    void stopRecording();

    //录像定时器

    void checkFileSize_time();



public:
    // 当前设备信息
    QString currentDevicePath;
    QVariantMap currentFormat;
    QVariantMap currentResolution;
    //定时器
    QTimer *recordingTimer;
    QTimer *byFileSizeTimer;
    QTimer *checkFileSizeTimer;

    void rotateCamera(); //旋转
    void takePhoto(); //拍照


private:
    // GStreamer管道
    GstElement *pipeline = nullptr;
    GstElement *flip=nullptr;

    // Parser元素管理
    GstElement *parser = nullptr;  // parser元素引用
    GstElement *decoder = nullptr; // decoder元素引用
    bool parser_removed = false;   // 标记parser是否被移除

    // 拍照相关
    QMutex photoMutex;  // 保护拍照操作的互斥锁

    // 视频控件
    QMutex mutex;
    QImage lastFrame;  // 保存最后一帧
    //Q_INVOKABLE void requestImmediateFrame();

    // GStreamer回调函数
    static gboolean busCallback(GstBus *bus, GstMessage *msg, gpointer data);
    //static GstFlowReturn newSampleCallback(GstElement *sink, gpointer data);

    // 动态创建和销毁appsink
    GstElement* createPhotoSink();
    void destroyPhotoSink(GstElement* photoSink);

    // 动态创建和销毁录像分支
    GstElement* createRecordBranch(const QString &filePath, int quality, int maxSizeTime = 0, qint64 maxSizeBytes = 0);
    void destroyRecordBranch();
    bool activateRecordBranch();
    bool deactivateRecordBranch();

    // 音频分支管理
    bool createAudioBranch(const QString &audioDevice);
    void destroyAudioBranch();
    bool activateAudioBranch();

    // 动态管理预览分支
    bool restorePreviewParser();
    bool insertWatermarkIsolationElements();  // 启用水印隔离（设置capsfilter为I420）
    bool removeWatermarkIsolationElements();  // 禁用水印隔离（恢复capsfilter为不限制格式）

    // 动态管理时间水印
    bool insertTimeWatermark();              // 动态插入时间水印元素
    bool removeTimeWatermark();              // 动态移除时间水印元素

    int rotate_direction=0;

    void ensurePipelineStability();          // 确保管道状态稳定，防止PTS问题


    // static GstFlowReturn newSampleCallback2(GstElement *sink, gpointer data);
    // static GstFlowReturn newSampleCallback3(GstElement *sink, gpointer data);
    // static GstFlowReturn newSampleCallback4(GstElement *sink, gpointer data);
};

#endif // CAMERASTREAM_H
