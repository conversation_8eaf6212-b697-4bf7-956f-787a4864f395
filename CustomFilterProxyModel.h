﻿#ifndef CUSTOMFILTERPROXYMODEL_H
#define CUSTOMFILTERPROXYMODEL_H

#include <QSortFilterProxyModel>
#include <QDate>
#include <QString>

class CustomFilterProxyModel : public QSortFilterProxyModel
{
	Q_OBJECT

public:
	explicit CustomFilterProxyModel(QObject* parent = nullptr);

	void setUseCustomFilter(bool use); //控制是否启用自定义过滤逻辑

	// 设置过滤日期
	void setFilterDate(const QDate& date);

	// 设置过滤关键字
	void setFilterKeyword(const QString& keyword);

	// 启用或禁用日期过滤
	void enableDateFilter(bool enable);

	// 启用或禁用关键字过滤
	void enableKeywordFilter(bool enable);

	// 公共接口：重新应用过滤器
	void reapplyFilters();

	// 添加一个公共函数来刷新过滤器
	//void refreshFilter() {
	//	invalidateFilter(); // 调用 protected 的 invalidateFilter()
	//}

protected:
	// 重写过滤逻辑
	bool filterAcceptsRow(int sourceRow, const QModelIndex& sourceParent) const override;

private:
	QDate m_filterDate;          // 存储过滤日期
	QString m_filterKeyword;     // 存储过滤关键字
	bool m_enableDateFilter;     // 是否启用日期过滤
	bool m_enableKeywordFilter;  // 是否启用关键字过滤
	bool m_useCustomFilter; // 是否启用自定义过滤逻辑

};

#endif // CUSTOMFILTERPROXYMODEL_H