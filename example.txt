#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QMouseEvent>
#include <gst/gst.h>
#include <gst/video/videooverlay.h>
#include <linux/videodev2.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void closeEvent(QCloseEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void showEvent(QShowEvent *event) override;  // 新增

private slots:
    void onCloseButtonClicked();
    void updateVideoOverlay();

private:
    Ui::MainWindow *ui;
    GstElement *pipeline = nullptr;
    GstElement *videoSink = nullptr;
    QPoint m_dragPosition;
    int v4l2_fd = -1;

    bool setupV4L2Device();
    void initializeGStreamer();
    void cleanupGStreamer();

    static gboolean busCallback(GstBus *bus, GstMessage *msg, gpointer data);
};
#endif // MAINWINDOW_H




#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QDebug>
#include <QTimer>
#include <QResizeEvent>
#include <QShowEvent>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);
    setWindowFlags(Qt::FramelessWindowHint);

    // 强制设置 Wayland 环境（关键！）
    qputenv("QT_QPA_PLATFORM", "wayland");


    // 强制创建原生窗口
    ui->video1->setAttribute(Qt::WA_NativeWindow, true);
    ui->video1->setAttribute(Qt::WA_DontCreateNativeAncestors, true);
    ui->video1->setAutoFillBackground(false);
    ui->video1->createWinId();

    qDebug() << "[DEBUG] Video Widget WinId:" << ui->video1->winId();

    // 配置V4L2设备
    if(!setupV4L2Device()) {
        qCritical("Failed to setup V4L2 device");
        close();
        return;
    }
    // 注意：GStreamer 初始化移到 showEvent 中
}

void MainWindow::showEvent(QShowEvent *event)
{
    QMainWindow::showEvent(event);
    // 延迟初始化确保控件完全显示
    QTimer::singleShot(100, this, &MainWindow::initializeGStreamer);
}

bool MainWindow::setupV4L2Device()
{
    v4l2_fd = open("/dev/video9", O_RDWR | O_NONBLOCK);
    if (v4l2_fd < 0) {
        qWarning("Cannot open device /dev/video9: %s", strerror(errno));
        return false;
    }

    struct v4l2_capability cap;
    if (ioctl(v4l2_fd, VIDIOC_QUERYCAP, &cap) < 0) {
        qWarning("Failed to query device capabilities: %s", strerror(errno));
        ::close(v4l2_fd);
        v4l2_fd = -1;
        return false;
    }

    struct v4l2_format fmt = {};
    fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    fmt.fmt.pix.width = 1920;
    fmt.fmt.pix.height = 1080;
    fmt.fmt.pix.pixelformat = V4L2_PIX_FMT_MJPEG;
    fmt.fmt.pix.field = V4L2_FIELD_NONE;

    if (ioctl(v4l2_fd, VIDIOC_S_FMT, &fmt) < 0) {
        qWarning("Failed to set video format: %s", strerror(errno));
        ::close(v4l2_fd);
        v4l2_fd = -1;
        return false;
    }
    ::close(v4l2_fd);
    return true;
}

void MainWindow::initializeGStreamer()
{
    // 确保GStreamer已初始化
    if (!gst_is_initialized()) {
        gst_init(nullptr, nullptr);
    }

    // 清理现有管道（如果有）
    if (pipeline) {
        cleanupGStreamer();
    }

    // 确保控件已准备好
    ui->video1->show();
    QCoreApplication::processEvents(QEventLoop::AllEvents, 100);

    // 创建管道元素
    pipeline = gst_pipeline_new("camera-pipeline");
    GstElement *source = gst_element_factory_make("v4l2src", "source");
    GstElement *filter = gst_element_factory_make("capsfilter", "filter");
    GstElement *parser = gst_element_factory_make("jpegparse", "parser");
    GstElement *decoder = gst_element_factory_make("mppjpegdec", "decoder");
    GstElement *convert = gst_element_factory_make("videoconvert", "convert");
    videoSink = gst_element_factory_make("waylandsink", "preview_waylandsink");


    // 检查元素创建是否成功
    if (!pipeline || !source || !filter || !parser || !decoder || !convert || !videoSink) {
        qWarning("Failed to create GStreamer elements:");
        if (!pipeline) qWarning("- pipeline");
        if (!source) qWarning("- source");
        if (!filter) qWarning("- filter");
        if (!parser) qWarning("- parser");
        if (!decoder) qWarning("- decoder");
        if (!convert) qWarning("- convert");
        if (!videoSink) qWarning("- videoSink");
        cleanupGStreamer();
        return;
    }

    // 配置视频源
    g_object_set(source,
                 "device", "/dev/video9",
                 "io-mode", 4,  // DMA buffer mode
                 nullptr);

    // 设置视频格式
    GstCaps *caps = gst_caps_new_simple("image/jpeg",
                                        "width", G_TYPE_INT, 1920,
                                        "height", G_TYPE_INT, 1080,
                                        "framerate", GST_TYPE_FRACTION, 30, 1,
                                        nullptr);
    g_object_set(filter, "caps", caps, nullptr);
    gst_caps_unref(caps);

    // 配置视频接收器
    g_object_set(videoSink,
                 "sync", FALSE,
                 "async", FALSE,
                 nullptr);

    // 设置窗口句柄（关键步骤）
    // 设置窗口句柄（增加有效性检查）
    WId winId = ui->video1->winId();
    if (winId == 0) {
        qWarning("Failed to get valid window ID!");
        cleanupGStreamer();
        return;
    }

    qDebug() << "Setting video overlay on window ID:" << winId;
    if (GST_IS_VIDEO_OVERLAY(videoSink)) {
        gst_video_overlay_set_window_handle(
            GST_VIDEO_OVERLAY(videoSink),
            (guintptr)winId
            );
    }


    // 构建管道
    gst_bin_add_many(GST_BIN(pipeline),
                     source, filter, parser, decoder, convert, videoSink,
                     nullptr);

    // 链接元素
    if (!gst_element_link_many(source, filter, parser, decoder, convert, videoSink, nullptr)) {
        qWarning("Failed to link GStreamer elements");
        cleanupGStreamer();
        return;
    }

    // 设置总线消息监听
    GstBus *bus = gst_pipeline_get_bus(GST_PIPELINE(pipeline));
    gst_bus_add_watch(bus, busCallback, this);
    gst_object_unref(bus);

    // 启动管道
    GstStateChangeReturn ret = gst_element_set_state(pipeline, GST_STATE_PLAYING);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        qWarning("Failed to start pipeline");
        cleanupGStreamer();
    } else {
        // 初始更新视频覆盖区域
        QTimer::singleShot(100, this, &MainWindow::updateVideoOverlay);


        // 创建高频刷新定时器
        QTimer *overlayTimer = new QTimer(this);
        overlayTimer->setInterval(50);
        connect(overlayTimer, &QTimer::timeout, this, [this]() {
            updateVideoOverlay();
            if (GST_IS_VIDEO_OVERLAY(videoSink)) {
                gst_video_overlay_expose(GST_VIDEO_OVERLAY(videoSink));
            }
        });
        overlayTimer->start();

    }
}

void MainWindow::updateVideoOverlay()
{
    if (!pipeline || !videoSink || !ui->video1->isVisible()) return;

    if (GST_IS_VIDEO_OVERLAY(videoSink)) {
        // 获取控件在窗口中的相对坐标
        QRect rect = ui->video1->geometry();
        QPoint windowPos = ui->video1->mapTo(this, rect.topLeft());

        //qDebug() << "[DEBUG] Overlay Region:" << windowPos << rect.size();
        gst_video_overlay_set_render_rectangle(
            GST_VIDEO_OVERLAY(videoSink),
            windowPos.x(), windowPos.y(),
            rect.width(), rect.height()
            );
    }
}

gboolean MainWindow::busCallback(GstBus *bus, GstMessage *msg, gpointer data)
{
    Q_UNUSED(bus);
    Q_UNUSED(data); // 标记data参数未使用

    switch (GST_MESSAGE_TYPE(msg)) {
    case GST_MESSAGE_ERROR: {
        GError *err = nullptr;
        gchar *debug = nullptr;
        gst_message_parse_error(msg, &err, &debug);
        qCritical("GStreamer Error: %s", err->message);
        if (debug) qCritical("Debug details: %s", debug);
        g_error_free(err);
        g_free(debug);
        break;
    }
    case GST_MESSAGE_EOS:
        qDebug("End of stream");
        break;
    case GST_MESSAGE_STATE_CHANGED: {
        GstState old_state, new_state, pending_state;
        gst_message_parse_state_changed(msg, &old_state, &new_state, &pending_state);
        qDebug("State changed from %s to %s",
               gst_element_state_get_name(old_state),
               gst_element_state_get_name(new_state));
        break;
    }
    default:
        break;
    }
    return TRUE;
}

void MainWindow::onCloseButtonClicked()
{
    cleanupGStreamer();
    close();
}

void MainWindow::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragPosition = event->globalPos() - frameGeometry().topLeft();
        event->accept();
    }
}

void MainWindow::mouseMoveEvent(QMouseEvent *event)
{
    if (event->buttons() & Qt::LeftButton) {
        move(event->globalPos() - m_dragPosition);
        event->accept();
    }
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);
    // 延迟更新避免频繁调用
    QTimer::singleShot(50, this, &MainWindow::updateVideoOverlay);
}

// 清理函数
void MainWindow::cleanupGStreamer()
{
    if (pipeline) {
        gst_element_set_state(pipeline, GST_STATE_NULL);
        gst_object_unref(pipeline);
        pipeline = nullptr;
    }
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    cleanupGStreamer();
    event->accept();
}

MainWindow::~MainWindow()
{
    cleanupGStreamer();
    delete ui;
}

