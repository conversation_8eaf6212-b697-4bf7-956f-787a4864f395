﻿#include "CustomFileSystemModel.h"
#include <QDebug>
#include <QLocale>
#include <QDir>
#include <QApplication>
#include <QFile>


CustomFileSystemModel::CustomFileSystemModel(QObject* parent)
	: QFileSystemModel(parent)
{

}

QVariant CustomFileSystemModel::headerData(int section, Qt::Orientation orientation, int role) const
{
	// 获取 CustomFileSystemModel 的父对象 (FileManage)
	QObject* parentObject = parent();
	//qDebug() << "Direct parent object (FileManage):" << parentObject;

	// 获取 FileManage 的父对象 (RerWS)
	QObject* grandparentObject = nullptr;
	if (parentObject) {
		grandparentObject = parentObject->parent();
		//qDebug() << "Grandparent object (RerWS):" << grandparentObject;
	}

    MainWindow* mainWindow = qobject_cast<MainWindow*>(grandparentObject); // 获取父窗口指针

    int languageindex = mainWindow->currentLanguageIndex; // 获取当前语言索引
	QString languagetext;
	if (orientation == Qt::Horizontal && role == Qt::DisplayRole) {
		switch (section) {
		case 0:
            languagetext = mainWindow->systemmanager->getTranslatedText("name",languageindex,"filesystemmodel");       // 名称
			return languagetext;       // 名称
		case 1:
            languagetext = mainWindow->systemmanager->getTranslatedText("modifydate",languageindex,"filesystemmodel");   // 修改日期
			return languagetext;   // 修改日期
		case 2:
            languagetext = mainWindow->systemmanager->getTranslatedText("type",languageindex,"filesystemmodel");       // 类型
			return languagetext;       // 类型
		case 3:
            languagetext = mainWindow->systemmanager->getTranslatedText("size",languageindex,"filesystemmodel");        // 大小
			return languagetext;        // 大小
		default: return QFileSystemModel::headerData(section, orientation, role);
		}
	}
	return QFileSystemModel::headerData(section, orientation, role);
}

QVariant CustomFileSystemModel::data(const QModelIndex& index, int role) const
{
	if (role == Qt::DisplayRole) {
		int column = index.column();
		switch (column) {
		case 0: // 名称
			return QFileSystemModel::data(index, Qt::DisplayRole);
		case 1: // 修改日期
			return QFileSystemModel::data(this->index(index.row(), 3, index.parent()), Qt::DisplayRole);
		case 2: // 类型
			return getFileType(index); // 调用自定义的 getFileType 方法
		case 3: // 大小
			return formatFileSize(index); // 调用自定义的 formatFileSize 方法
		default:
			return QFileSystemModel::data(index, Qt::DisplayRole);
		}
	}

	// 设置文本对齐方式
	if (role == Qt::TextAlignmentRole) {
		return QVariant(static_cast<int>(Qt::AlignLeft | Qt::AlignVCenter)); // 文件大小靠左对齐
	}

	return QFileSystemModel::data(index, role);
}

QString CustomFileSystemModel::getFileType(const QModelIndex& index) const
{
    QFileInfo fileInfo = this->fileInfo(index);
    QString fileType = fileInfo.suffix().toLower(); // 获取文件扩展名


    // 获取 CustomFileSystemModel 的父对象 (FileManage)
    QObject* parentObject = parent();
    //qDebug() << "Direct parent object (FileManage):" << parentObject;

    // 获取 FileManage 的父对象 (RerWS)
    QObject* grandparentObject = nullptr;
    if (parentObject) {
        grandparentObject = parentObject->parent();
        //qDebug() << "Grandparent object (RerWS):" << grandparentObject;
    }

    MainWindow* mainWindow = qobject_cast<MainWindow*>(grandparentObject); // 获取父窗口指针

    int languageindex = mainWindow->currentLanguageIndex; // 获取当前语言索引
    QString languagetext;


    // 调试输出
    //qDebug() << "文件扩展名: " << fileType;
    //qDebug() << "文件类型: " << (fileInfo.isDir() ? "文件夹" : "未知类型");

    // 根据文件扩展名返回中文文件类型
    if (fileType == "txt") {
        return "文本文件";
    }
    else if (fileType == "jpg" || fileType == "jpeg" || fileType == "png" || fileType == "gif") {
        languagetext = Languagetext(languageindex, "Image files");
        return languagetext;
    }
    else if (fileType == "mp4" || fileType == "avi" || fileType == "mkv") {
        languagetext = Languagetext(languageindex, "Video files");
        return languagetext;
    }
    else if (fileType == "mp3" || fileType == "wav" || fileType == "flac") {
        return "音频文件";
    }
    else if (fileType == "doc" || fileType == "docx") {
        return "Word 文档";
    }
    else if (fileType == "xls" || fileType == "xlsx") {
        return "Excel 表格";
    }
    else if (fileType == "ppt" || fileType == "pptx") {
        return "PowerPoint 演示文稿";
    }
    else if (fileType == "pdf") {
        return "PDF 文件";
    }
    else if (fileType == "zip" || fileType == "rar" || fileType == "7z") {
        return "压缩文件";
    }
    else if (fileInfo.isDir()) {
        languagetext = Languagetext(languageindex, "folder");
        return languagetext;
    }
    else {
        return "未知类型";
    }
}

QString CustomFileSystemModel::formatFileSize(const QModelIndex& index) const
{
	QFileInfo fileInfo = this->fileInfo(index);

	// 如果是文件夹，返回空字符串或特定的字符串（例如 "文件夹"）
	if (fileInfo.isDir()) {
		return "";
	}

	qint64 sizeInBytes = fileInfo.size();

	// 将字节转换为 MB 或 MiB
	double sizeInMB = sizeInBytes / (1024.0 * 1024.0); // 转换为 MB

	// 使用 QLocale 格式化数字
	QLocale locale;
	QString formattedSize;

	if (sizeInMB < 1.0) {
		// 如果文件大小小于 1 MB，显示为 KB
		double sizeInKB = sizeInBytes / 1024.0;
		formattedSize = locale.toString(sizeInKB, 'f', 2) + " KB";
	}
	else {
		// 显示为 MB
		formattedSize = locale.toString(sizeInMB, 'f', 2) + " MB";
	}

	return formattedSize;
}

QString CustomFileSystemModel::Languagetext(int index, QString text)const
{
    QString toptext;
    if (index == 0) {
        QString iniFilePath = QDir::toNativeSeparators(QApplication::applicationDirPath()) + "/data/Chinese.ini";

        // 检查INI文件是否存在
        if (!QFile::exists(iniFilePath)) {
            // 如果文件不存在，创建默认的中文配置
            createDefaultChineseIni(iniFilePath);
        }

        // 读取INI文件
        QSettings settings(iniFilePath, QSettings::IniFormat);
        settings.setIniCodec("UTF-8");

        // 设置默认值，如果文件存在但没有对应的键，使用默认中文
        QString defaultValue = getDefaultChineseText(text);
        toptext = settings.value(text, defaultValue).toString();

        //qDebug() << "读取的文本：" << toptext;
        return toptext;
    }
    return text; // 如果index不是0，返回原始文本
}

// 创建默认的中文INI文件
void CustomFileSystemModel::createDefaultChineseIni(const QString& filePath) const
{
    // 确保目录存在
    QDir dir = QFileInfo(filePath).absoluteDir();
    if (!dir.exists()) {
        dir.mkpath(".");
    }

    QSettings settings(filePath, QSettings::IniFormat);
    settings.setIniCodec("UTF-8");

    // 设置默认的中文配置
    settings.setValue("name", "名称");
    settings.setValue("modifydate", "修改日期");
    settings.setValue("type", "类型");
    settings.setValue("size", "大小");
    settings.setValue("Image files", "图像文件");
    settings.setValue("Video files", "视频文件");
    settings.setValue("folder", "文件夹");

    // 强制写入文件
    settings.sync();

    qDebug() << "已创建默认中文配置文件：" << filePath;
}

// 获取默认中文文本
QString CustomFileSystemModel::getDefaultChineseText(const QString& key) const
{
    // 定义默认的中文映射
    static QMap<QString, QString> defaultTexts = {
        {"name", "名称"},
        {"modifydate", "修改日期"},
        {"type", "类型"},
        {"size", "大小"},
        {"Image files", "图像文件"},
        {"Video files", "视频文件"},
        {"folder", "文件夹"}
    };

    return defaultTexts.value(key, key); // 如果找不到对应的键，返回原始键值
}
