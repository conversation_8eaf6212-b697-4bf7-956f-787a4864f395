#include "systemmanager.h"
#include <QDir>
#include <QFile>
#include <QSettings>
#include <QDebug>
SystemManager::SystemManager(QObject *parent)
    : QObject{parent}
{

}
QString SystemManager::getTranslatedText(const QString& key, int languageIndex, QString group)
{
    // 检查languageIndex是否在有效范围内
    if (languageIndex < 0 || languageIndex >= TranslatedPaths.size()) {
        qWarning() << "Invalid languageIndex:" << languageIndex << "Valid range: 0 to" << (TranslatedPaths.size() - 1);
        return "Error: Invalid language index";
    }

    QString iniFilePath = QDir::toNativeSeparators(TranslatedPaths[languageIndex]);

    // 检查INI文件是否存在
    if (!QFile::exists(iniFilePath)) {
        qWarning() << "INI file does not exist:" << iniFilePath;
        return "Error: Translation file missing";
    }

    // 读取INI文件
    QSettings settings(iniFilePath, QSettings::IniFormat);
    settings.setIniCodec("UTF-8");
    QString value = settings.value(group + "/" + key, "").toString();

    // 如果键不存在，返回错误信息
    if (value.isEmpty()) {
        qWarning() << "Key not found:" << key << "in" << iniFilePath;
        return "Error: Translation missing";
    }

    return value;
}
