#include "systemset.h"
#include "ui_systemset.h"
#include "mainwindow.h"
#include <SysComboBoxStyle.h>
#include <QDebug>
#include <cstdlib>

#include "ui_mainwindow.h"

Systemset::Systemset(QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::Systemset)
    , timeTimer(new QTimer(this))
{
    ui->setupUi(this);
    setWindowFlags(Qt::FramelessWindowHint);
    show();

    ui->comboBox->setView(new QListView);
    ui->comboBox->setStyleSheet(SysComboBoxStyle::getStyleSheet());

    MainWindow* mainWindow = qobject_cast<MainWindow*>(parent); // 获取父窗口指针
    if(mainWindow){
        ui->comboBox->setCurrentIndex(mainWindow->currentLanguageIndex);
    }

    // 创建并启动定时器来更新时间显示
    connect(timeTimer, &QTimer::timeout, this, &Systemset::updateTimeDisplay);
    timeTimer->start(1000); // 每秒更新一次

    // 立即更新一次时间显示
    updateTimeDisplay();

    // 获取当前系统时间
    QDateTime currentTime = QDateTime::currentDateTime();
    // 格式化时间字符串为 "yyyy-MM-dd hh:mm:ss" 格式
    QString year = currentTime.toString("yyyy");
    QString month = currentTime.toString("MM");
    QString day = currentTime.toString("dd");
    QString hour = currentTime.toString("hh");
    QString min = currentTime.toString("mm");
    QString second = currentTime.toString("ss");
    ui->spinBox_year->setValue(year.toInt());
    ui->comboBox_month->setCurrentText(month);
    ui->comboBox_day->setCurrentText(day);
    ui->spinBox_hour->setValue(hour.toInt());
    ui->spinBox_min->setValue(min.toInt());
    ui->spinBox_second->setValue(second.toInt());

    ui->comboBox_month->setView(new QListView);
    ui->comboBox_day->setView(new QListView);

    // 初始化虚拟键盘
    m_virtualKeyboard = new VirtualKeyboard(this);
    setupSpinBoxKeyboard(ui->spinBox_hour);
    setupSpinBoxKeyboard(ui->spinBox_year);
    setupSpinBoxKeyboard(ui->spinBox_min);
    setupSpinBoxKeyboard(ui->spinBox_second);

    ui->spinBox_second->setEnabled(false);
    ui->spinBox_hour->setEnabled(false);
    ui->spinBox_min->setEnabled(false);
    ui->spinBox_year->setEnabled(false);
    ui->comboBox_month->setEnabled(false);
    ui->comboBox_day->setEnabled(false);

    ui->pushButton_OK->setEnabled(false);
}

Systemset::~Systemset()
{
    if (timeTimer) {
        timeTimer->stop();
    }
    // 安全释放键盘资源
    if (m_virtualKeyboard) {
        // 先停止键盘可能正在进行的操作
        m_virtualKeyboard->hide();
        m_virtualKeyboard->disconnect(); // 断开所有信号连接
        delete m_virtualKeyboard;
    }
    delete ui;
}



void Systemset::combo_language(int index) {
    MainWindow* mainWindow = qobject_cast<MainWindow*>(parent()); // 获取父窗口指针
    if (!mainWindow) {
        return; // 如果父窗口无效，直接返回
    }

    // 根据索引加载对应的翻译文件
    QString translationFile;
    switch (index) {
    case 0: translationFile = QDir::toNativeSeparators ("/data/Language/RWS_zh_CN.qm");break;
    case 1: translationFile = QDir::toNativeSeparators ("/data/Language/RWS_en_AS.qm"); break;
    case 2: translationFile = QDir::toNativeSeparators ("/data/Language/RWS_ar_DZ.qm"); break;
    case 3: translationFile = QDir::toNativeSeparators ("/data/Language/RWS_zh_HK.qm"); break;
    case 4: translationFile = QDir::toNativeSeparators ("/data/Language/RWS_fr_DZ.qm"); break;
    case 5: translationFile = QDir::toNativeSeparators ("/data/Language/RWS_de_AT.qm"); break;
    case 6: translationFile = QDir::toNativeSeparators ("/data/Language/RWS_it_IT.qm"); break;
    case 7: translationFile = QDir::toNativeSeparators ("/data/Language/RWS_ja_JP.qm"); break;
    case 8: translationFile = QDir::toNativeSeparators ("/data/Language/RWS_ko_CN.qm"); break;
    case 9: translationFile = QDir::toNativeSeparators ("/data/Language/RWS_pt_AO.qm"); break;
    case 10: translationFile = QDir::toNativeSeparators("/data/Language/RWS_ru_BY.qm"); break;
    case 11: translationFile = QDir::toNativeSeparators("/data/Language/RWS_es_AR.qm"); break;
    case 12: translationFile = QDir::toNativeSeparators("/data/Language/RWS_tr_CY.qm"); break;
    default: return; // 如果索引无效，直接返回
    }
    //qDebug()<<"seleect language"<<QApplication::applicationDirPath()<<translationFile;
    // 加载翻译文件
    if (mainWindow->translator.load(translationFile)) {
        qApp->installTranslator(&mainWindow->translator);
        mainWindow->currentLanguageIndex = index; // 保存当前选择的语言索引

        //qDebug()<<"system shauxingjiemian";
        // 更新界面文本
        ui->retranslateUi(this);


        // 强制刷新界面
        qApp->processEvents();

        // 调整窗口大小以适应新内容
        adjustSize();

        // 保持高度不变
        //setFixedHeight(height());

        // 发射语言切换信号
        emit Languagechanged();

    }
    for(int i=0;i<4;i++)
    {
        mainWindow->updateDeviceLabels(i);
    }
    mainWindow->updateCompositePageLabels(0,1,mainWindow->channelSettings[0].isOpen,mainWindow->channelSettings[1].isOpen);
    mainWindow->updateCompositePageLabels(2,3,mainWindow->channelSettings[2].isOpen,mainWindow->channelSettings[3].isOpen);
    mainWindow->updateGridPageLabels(0,1,2,3,mainWindow->channelSettings[0].isOpen,mainWindow->channelSettings[1].isOpen,mainWindow->channelSettings[2].isOpen,mainWindow->channelSettings[3].isOpen);

    //video设备combox
    if (mainWindow->ui->comboBox_cameradev->count() > 0) {
        int lastIndex = mainWindow->ui->comboBox_cameradev->count() - 1;
        mainWindow->ui->comboBox_cameradev->removeItem(lastIndex); // 删除最后一项
    }

    //重新添加翻译后的 "关闭设备"
    QString closeDeviceText = mainWindow->systemmanager->getTranslatedText(
        "close_device",
        mainWindow->currentLanguageIndex,
        "mainwindow"
        );
    mainWindow->ui->comboBox_cameradev->addItem(closeDeviceText, ""); // 添加到末尾

    //音频设备combox
    int count = mainWindow->ui->comboBox_audiodev->count();
    QString microphoneText = mainWindow->systemmanager->getTranslatedText(
        "microphone",
        mainWindow->currentLanguageIndex,
        "mainwindow"
        );
    mainWindow->ui->comboBox_audiodev->addItem(microphoneText, "hw:1");

    // 添加"关闭"选项（最后一个位置）
    QString audioOffText = mainWindow->systemmanager->getTranslatedText(
        "off",
        mainWindow->currentLanguageIndex,
        "mainwindow"
        );
    mainWindow->ui->comboBox_audiodev->addItem(audioOffText, "-1");

    // 删除最后一项
    mainWindow->ui->comboBox_audiodev->removeItem(count - 1);
    // 删除倒数第二项（现在是新的最后一项）
    mainWindow->ui->comboBox_audiodev->removeItem(count - 2);

    mainWindow->ui->comboBox_audiodev->setCurrentIndex(mainWindow->channelSettings[mainWindow->curchannel].audioDeviceIndex);

    // 更新父窗口的其他文本内容
    // mainWindow->updateDevText();//更新当前实际视频设备文本，没有这句就不显示设备
    // mainWindow->updateFBLText();//更新当前实际分辨率文本
    // mainWindow->Label2and4(mainWindow->mCurChannel);
    // mainWindow->ChooseAudioRecord();//更新是否录音文本
    // mainWindow->ui.comboBox->removeItem(mainWindow->mDevCount);//添加视频设备关闭文本
    // mainWindow->ui.comboBox_4->removeItem(mainWindow->audio_count);//添加音频设备关闭文本
    // QString ltext;
    // ltext = mainWindow->Languagetext(mainWindow->currentLanguageIndex, "0");
    // mainWindow->ui.comboBox->insertItem(mainWindow->mDevCount, ltext);
    // mainWindow->ui.comboBox_4->insertItem(mainWindow->audio_count, ltext);
}


// ================== SpinBox键盘设置 ==================
void Systemset::setupSpinBoxKeyboard(QSpinBox* spinBox)
{
    if (!spinBox) {
        qWarning() << "Invalid QSpinBox pointer!";
        return;
    }

    // 获取内置的LineEdit
    QLineEdit* lineEdit = spinBox->findChild<QLineEdit*>();
    if (!lineEdit) {
        qWarning() << "Cannot find LineEdit in QSpinBox!";
        return;
    }

    // 安装事件过滤器
    spinBox->installEventFilter(this);
    lineEdit->installEventFilter(this);

    // 设置数值范围验证
    lineEdit->setValidator(new QIntValidator(spinBox->minimum(), spinBox->maximum(), this));

    // 调试样式（正式发布时可移除）
    lineEdit->setStyleSheet("border: 1px solid #2ecc71; background: #f0fff0;");
}

// ================== 事件过滤器核心逻辑 ==================
bool Systemset::eventFilter(QObject* obj, QEvent* event)
{
    // 处理SpinBox点击
    QSpinBox* spinBox = qobject_cast<QSpinBox*>(obj);
    QLineEdit* spinBoxLineEdit = nullptr;

    if (spinBox) {
        spinBoxLineEdit = spinBox->findChild<QLineEdit*>();
    } else if (QLineEdit* le = qobject_cast<QLineEdit*>(obj)) {
        if ((spinBox = qobject_cast<QSpinBox*>(le->parent()))) {
            spinBoxLineEdit = le;
        }
    }

    if (spinBoxLineEdit && event->type() == QEvent::MouseButtonRelease) {
        // 关键修改：添加启用状态检查
        if (!spinBox->isEnabled()) {
            return QDialog::eventFilter(obj, event);
        }

        QMouseEvent* mouseEvent = static_cast<QMouseEvent*>(event);

        // 获取SpinBox的上下按钮区域
        QStyleOptionSpinBox opt;
        opt.initFrom(spinBox);
        QRect upRect = spinBox->style()->subControlRect(
            QStyle::CC_SpinBox, &opt, QStyle::SC_SpinBoxUp, spinBox);
        QRect downRect = spinBox->style()->subControlRect(
            QStyle::CC_SpinBox, &opt, QStyle::SC_SpinBoxDown, spinBox);

        // 如果点击位置不在按钮区域内，才触发键盘
        if (!upRect.contains(mouseEvent->pos()) &&
            !downRect.contains(mouseEvent->pos())) {
            spinBoxLineEdit->setFocus();
            m_virtualKeyboard->attachTo(spinBoxLineEdit);
            m_virtualKeyboard->showAtOptimalPosition(spinBox);
            return true;
        }
    }

    return QDialog::eventFilter(obj, event);
}

// 显示键盘的辅助函数
void Systemset::showKeyboardForSpinBox(QSpinBox* spinBox)
{
    if (!spinBox || !m_virtualKeyboard) return;

    QLineEdit* lineEdit = spinBox->findChild<QLineEdit*>();
    if (lineEdit) {
        m_virtualKeyboard->attachTo(lineEdit);
        m_virtualKeyboard->showAtOptimalPosition(spinBox);
    }
}



void Systemset::set_time()
{
    ui->spinBox_second->setEnabled(false);
    ui->spinBox_hour->setEnabled(false);
    ui->spinBox_min->setEnabled(false);
    ui->spinBox_year->setEnabled(false);
    ui->comboBox_month->setEnabled(false);
    ui->comboBox_day->setEnabled(false);

    // 获取各个控件的值
    int year = ui->spinBox_year->value();
    int month = ui->comboBox_month->currentText().toInt();
    int day = ui->comboBox_day->currentText().toInt();
    int hour = ui->spinBox_hour->value();
    int minute = ui->spinBox_min->value();
    int second = ui->spinBox_second->value();

    // 格式化时间字符串 "YYYY-MM-DD HH:MM:SS"
    QString timeString = QString("%1-%2-%3 %4:%5:%6")
                             .arg(year, 4, 10, QChar('0'))           // 年份，4位，不足补0
                             .arg(month, 2, 10, QChar('0'))          // 月份，2位，不足补0
                             .arg(day, 2, 10, QChar('0'))            // 日期，2位，不足补0
                             .arg(hour, 2, 10, QChar('0'))           // 小时，2位，不足补0
                             .arg(minute, 2, 10, QChar('0'))         // 分钟，2位，不足补0
                             .arg(second, 2, 10, QChar('0'));        // 秒数，2位，不足补0

    // 构建date命令
    QString dateCommand = QString("date -s \"%1\"").arg(timeString);

    // 执行date命令设置系统时间
    int dateResult = system(dateCommand.toLocal8Bit().data());

    // 执行hwclock命令将系统时间写入硬件时钟
    int hwclockResult = system("hwclock -w");

}

// 更新时间显示的槽函数
void Systemset::updateTimeDisplay()
{
    // 获取当前系统时间
    QDateTime currentTime = QDateTime::currentDateTime();

    // 格式化时间字符串为 "yyyy-MM-dd hh:mm:ss" 格式
    QString timeString = currentTime.toString("yyyy-MM-dd hh:mm:ss");

    // 更新label_time的显示
    ui->label_time->setText(timeString);
}

void Systemset::changetime(){
    ui->spinBox_second->setEnabled(true);
    ui->spinBox_hour->setEnabled(true);
    ui->spinBox_min->setEnabled(true);
    ui->spinBox_year->setEnabled(true);
    ui->comboBox_month->setEnabled(true);
    ui->comboBox_day->setEnabled(true);
    ui->pushButton_OK->setEnabled(true);
}
