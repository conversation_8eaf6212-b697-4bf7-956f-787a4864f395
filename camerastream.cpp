#include "camerastream.h"
#include <QDebug>
#include<QTime>
#include <linux/videodev2.h>
#include "mainwindow.h"
#include <QStorageInfo>
#include <QProcess>
#include <QThread>
#include <QRegExp>
// 添加C标准库头文件
#include <time.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <sys/stat.h>  // 对应mkdir
#include <sys/types.h> // 对应mkdir
#include <errno.h>     // 对应errno
#include <functional>
#include <QComboBox>
#include <QtConcurrent>

gchar* CameraStream::global_format_location_callback(GstElement *splitmux, guint fragment_id, gpointer user_data)
{
    // 使用纯C风格打印
    fprintf(stdout, "全局回调函数被调用: fragment_id=%u\n", fragment_id);
    fprintf(stdout, "user_data指针: %p\n", user_data);
    fflush(stdout);

    // 默认路径
    const char* backup_path = "/mnt/nvme";
    const char* effective_path = backup_path;

    // 修复：使用高精度时间戳，确保文件名的唯一性和时间准确性
    QDateTime currentTime = QDateTime::currentDateTime();
    QString fileName = currentTime.toString("yyyy-MM-dd_hh-mm-ss-zzz");
    QString datePath = currentTime.toString("yyyy_MM_dd");

    // 记录分段时间信息用于调试
    fprintf(stdout, "分段时间: %s, fragment_id=%u\n",
            currentTime.toString("yyyy-MM-dd hh:mm:ss.zzz").toUtf8().constData(), fragment_id);

    // 从user_data获取CameraStream实例
    if (user_data) {
        CameraStream* stream = static_cast<CameraStream*>(user_data);
        fprintf(stdout, "成功获取CameraStream实例\n");

        // 添加录像前缀名
        if (stream && stream->m_mainWindow) {
            int currentChannel = stream->channelstream;
            QString recordingPrefix = stream->m_mainWindow->RecordingSettings[currentChannel].name;
            if (!recordingPrefix.isEmpty()) {
                fileName = recordingPrefix + fileName;
                fprintf(stdout, "添加录像前缀: %s，完整文件名: %s\n",
                        recordingPrefix.toUtf8().constData(), fileName.toUtf8().constData());
            }
        }

        // 时间跟踪和验证
        if (stream && stream->lastSegmentTime.isValid()) {
            QDateTime now = currentTime;
            qint64 actualDuration = stream->lastSegmentTime.msecsTo(now);
            stream->totalRecordedTime += actualDuration;

            fprintf(stdout, "分段时间统计: 实际时长=%lld毫秒, 期望时长=%lld毫秒, 差异=%lld毫秒\n",
                    actualDuration, stream->expectedSegmentDuration,
                    actualDuration - stream->expectedSegmentDuration);
            fprintf(stdout, "总录制时间: %lld毫秒 (%.2f分钟)\n",
                    stream->totalRecordedTime, stream->totalRecordedTime / 60000.0);

            // 更新上次分段时间
            stream->lastSegmentTime = now;
        }

        // 检查recordingpath是否有效
        if (stream && !stream->recordingpath.isEmpty()) {
            effective_path = stream->recordingpath.toUtf8().constData();
            fprintf(stdout, "使用实例路径: %s\n", effective_path);
        } else {
            fprintf(stdout, "recordingpath为空或无效，使用默认路径\n");
        }
    } else {
        fprintf(stdout, "user_data为NULL，使用默认路径\n");
    }
    fflush(stdout);

    // 生成完整的日期目录路径
    QString fullDirPath;
    if (QString(effective_path).endsWith("/")) {
        fullDirPath = QString(effective_path) + datePath;
    } else {
        fullDirPath = QString(effective_path) + "/" + datePath;
    }

    // 检查并创建日期目录
    QDir dateDir(fullDirPath);
    if (!dateDir.exists()) {
        if (dateDir.mkpath(".")) {
            fprintf(stdout, "成功创建新日期目录: %s\n", fullDirPath.toUtf8().constData());
        } else {
            fprintf(stdout, "警告：无法创建日期目录: %s\n", fullDirPath.toUtf8().constData());
        }
    }
    qDebug()<<"fullDirPath:"<<fullDirPath;
    // 生成文件名，避免双斜杠
    char filename[512];
    snprintf(filename, sizeof(filename), "%s/%s_%u.mp4", fullDirPath.toUtf8().constData(), fileName.toUtf8().constData(), fragment_id + 1);

    // 更新CameraStream实例中的当前分段文件名
    if (user_data) {
        CameraStream* stream = static_cast<CameraStream*>(user_data);
        if (stream) {
            stream->currentSegmentFilename = QString::fromUtf8(filename);
            fprintf(stdout, "更新当前分段文件名: %s\n", qPrintable(stream->currentSegmentFilename));
        }
    }
    fflush(stdout);
    return g_strdup(filename);
}


CameraStream::CameraStream(QObject *parent) : QObject(parent), pipeline(nullptr)
{
    // 初始化channelstream为0，防止未定义行为
    channelstream = 0;

    // 初始化GStreamer（如果尚未初始化）
    if (!gst_is_initialized()) {
        gst_init(nullptr, nullptr);
    }

    // 确保实例成员变量有默认值
    strcpy(m_recording_base_path, "./");
    m_path_initialized = true;

    // 初始化预览状态
    m_isPreviewPaused = false;

    // 初始化m_mainWindow指针
    m_mainWindow = qobject_cast<MainWindow*>(parent);

    // recordingTimer = new QTimer(this);
    // connect(recordingTimer, &QTimer::timeout, this, &CameraStream::recording_time);
    // byFileSizeTimer = new QTimer(this);
    // connect(byFileSizeTimer, &QTimer::timeout, this, &CameraStream::byFileSize_time);

    checkFileSizeTimer = new QTimer(this);
    connect(checkFileSizeTimer, &QTimer::timeout, this, &CameraStream::checkFileSize_time);

    // 初始化photo_sink延迟销毁定时器
    m_photo_sink_timer = new QTimer(this);
    m_photo_sink_timer->setSingleShot(true); // 单次触发
    connect(m_photo_sink_timer, &QTimer::timeout, this, &CameraStream::destroyPhotoSinkDelayed);

    // 初始化拍照相关标志
    m_photoRequested = false;
    m_photoPipelineActive = false;

    // 初始化录像相关成员变量
    m_cached_record_branch = nullptr;
    m_record_tee_pad = nullptr;
    m_record_queue_pad = nullptr;

    // 初始化音频相关成员变量
    m_audio_source = nullptr;
    m_audio_convert = nullptr;
    m_audio_encoder = nullptr;
    m_audio_parser = nullptr;
    m_audio_queue = nullptr;
    m_audio_device_path.clear();

    // 初始化parser相关成员变量
    parser = nullptr;
    decoder = nullptr;
    parser_removed = true; // 不再使用jpegparse，标记为已移除

    // 初始化时间水印相关成员变量
    m_timeoverlay = nullptr;
    m_convert = nullptr;
}

CameraStream::~CameraStream()
{
    // 清理GStreamer资源
    cleanupGStreamer();
}

bool CameraStream::initializeGStreamer(const QString &devicePath,
                                       const QVariantMap &format,
                                       const QVariantMap &resolution)
{
    // 检查参数有效性
    if (devicePath.isEmpty()) {
        qWarning("devicePathIsEmpty,CannotInitializeGStreamer");
        return false;
    }

    // 获取MainWindow指针
    // MainWindow* m_mainWindow = qobject_cast<MainWindow*>(parent());
    // int streamchannel=m_mainWindow->get_curchannel();
    // qDebug()<<"curchannel:"<<streamchannel;
    // 清理现有的管道（如果有）
    cleanupGStreamer();

    // 保存当前设备信息
    currentDevicePath = devicePath;
    currentFormat = format;
    currentResolution = resolution;

    // 设置默认参数
    int width = 3840;
    int height = 2160;
    int framerate_num = 30;
    int framerate_den = 1;
    QString formatStr = "image/jpeg"; // 默认格式
    uint32_t pixelformat = V4L2_PIX_FMT_MJPEG; // 默认像素格式

    // 从格式中提取信息
    if (!format.isEmpty()) {
        pixelformat = format["pixelformat"].toUInt();

        // 设置GStreamer格式字符串
        QString formatName = format["formatName"].toString();
        if (formatName.toUpper() == "MJPG" || formatName.toUpper() == "JPEG") {
            formatStr = "image/jpeg";
        } else if (formatName.toUpper() == "YUYV") {
            formatStr = "video/x-raw,format=YUY2";
        } else if (formatName.toUpper() == "UYVY") {
            formatStr = "video/x-raw,format=UYVY";
        } else {
            // 其他格式使用默认
            qWarning("unsupportedFormats:%s,UseDefaultJPEG", qPrintable(formatName));
        }
    }

    // 从分辨率中提取信息
    if (!resolution.isEmpty()) {
        width = resolution["width"].toInt();
        height = resolution["height"].toInt();

        // 如果有帧率信息，则使用
        if (resolution.contains("framerate_num") && resolution.contains("framerate_den")) {
            framerate_num = resolution["framerate_num"].toInt();
            framerate_den = resolution["framerate_den"].toInt();
            qDebug() << "setFrameRate: " << framerate_num << "/" << framerate_den << " fps";
        }
    }

    // 创建GStreamer管道元素
    pipeline = gst_pipeline_new("camera-pipeline");
    GstElement *source = gst_element_factory_make("v4l2src", "source");
    GstElement *filter = gst_element_factory_make("capsfilter", "filter");
    // parser和decoder现在使用成员变量
    m_convert = gst_element_factory_make("videoconvert", "convert");

    // 添加tee元素，用于分流视频流 (保留tee以便后续动态添加拍照分支)
    tee = gst_element_factory_make("tee", "tee");

    // 主视频流分支（简化，去掉强制格式转换）
    GstElement *queue_main = gst_element_factory_make("queue", "queue_main");
    flip = gst_element_factory_make("videoflip", "flip");
    GstElement *preview_convert = gst_element_factory_make("videoconvert", "preview_convert");
    GstElement *sink = gst_element_factory_make("waylandsink", "preview_waylandsink");

    // 水印隔离元素不再需要
    m_preview_convert = nullptr;
    m_preview_capsfilter = nullptr;

    // 根据格式创建相应的parser和decoder (保存为成员变量)
    // 不再使用jpegparse，直接使用identity作为parser
    if (formatStr == "image/jpeg") {
        // 使用identity代替jpegparse，避免录像时的PTS问题
        parser = gst_element_factory_make("identity", "parser");
        decoder = gst_element_factory_make("mppjpegdec", "decoder");
    } else {
        // 对于其他格式，使用identity作为parser和decoder
        parser = gst_element_factory_make("identity", "parser");
        decoder = gst_element_factory_make("identity", "decoder");
    }

    // 初始化parser状态 - 不再使用jpegparse，所以标记为已移除
    parser_removed = true;

    // 检查元素是否创建成功
    if (!pipeline || !source || !filter || !parser || !decoder || !m_convert ||
        !tee || !flip || !preview_convert || !sink || !queue_main) {
        qWarning("unableToCreateGStreamerElement");
        cleanupGStreamer();
        return false;
    }

    // 配置视频源
    g_object_set(source,
                 "device", qPrintable(devicePath),
                 "io-mode", 4, // DMA buffer mode
                 nullptr);

    // 设置视频格式
    GstCaps *caps = nullptr;
    if (formatStr == "image/jpeg") {
        caps = gst_caps_new_simple(formatStr.toUtf8().constData(),
                                   "width", G_TYPE_INT, width,
                                   "height", G_TYPE_INT, height,
                                   "framerate", GST_TYPE_FRACTION, framerate_num, framerate_den,
                                   nullptr);
    } else if(formatStr == "video/x-raw,format=YUY2")
    {
        // 对于YUY2等原始格式
        caps = gst_caps_new_simple("video/x-raw",
                                   "format", G_TYPE_STRING, "YUY2",
                                   "width", G_TYPE_INT, width,
                                   "height", G_TYPE_INT, height,
                                   "framerate", GST_TYPE_FRACTION, framerate_num, framerate_den,
                                   nullptr);
    }else if(formatStr == "video/x-raw,format=UYVY")
    {
        caps = gst_caps_new_simple("video/x-raw",
                                   //   "format", G_TYPE_STRING, "UYVY",
                                   "width", G_TYPE_INT, width,
                                   "height", G_TYPE_INT, height,
                                   "framerate", GST_TYPE_FRACTION, framerate_num, framerate_den,
                                   nullptr);
    }

    g_object_set(filter, "caps", caps, nullptr);
    gst_caps_unref(caps);



    // 配置sink
    MainWindow* m_mainWindow = qobject_cast<MainWindow*>(parent());
    // 获取窗口ID
    QWidget* showwidget = m_mainWindow->findChild<QWidget*>("video");
    WId winId = showwidget->winId();
    qDebug()<<showwidget;
    // 获取目标控件在屏幕上的全局坐标
    QPoint globalPos = showwidget->mapToGlobal(QPoint(0, 0));
    int x = globalPos.x();
    int y = globalPos.y();
    int w = showwidget->width();
    int h = showwidget->height()+1;


    // 设置渲染区域
    g_object_set(sink,
                 "sync", FALSE,  // 禁用同步（减少延迟）
                 "enable-last-sample", FALSE,  // 不保留最后一帧
                 "layer",2,
                 nullptr);

    g_object_set(flip, "video-direction", 0, nullptr);

    // 配置队列
    g_object_set(queue_main,
                 "max-size-buffers", 3,
                 "max-size-time", 0,
                 "max-size-bytes", 0,
                 "leaky", 2, // 下游丢帧
                 NULL);

    // 定义矩形坐标和尺寸
    gint rect[4] = {x, y, w, h};  // x, y, width, height

    // 初始化GValue为GstValueArray类型
    GValue val = G_VALUE_INIT;
    g_value_init(&val, GST_TYPE_ARRAY);

    // 添加四个整数值到数组中
    for (int i = 0; i < 4; i++) {
        GValue elem = G_VALUE_INIT;
        g_value_init(&elem, G_TYPE_INT);
        g_value_set_int(&elem, rect[i]);
        gst_value_array_append_value(&val, &elem);
        g_value_unset(&elem);
    }

    // 设置属性
    g_object_set_property(G_OBJECT(sink), "render-rectangle", &val);

    // 清理
    g_value_unset(&val);

    // 构建管道 - 直接添加parser，因为初始化时必定是预览状态
    gst_bin_add_many(GST_BIN(pipeline),
                     source, filter, parser, decoder, m_convert, tee,
                     queue_main, flip, preview_convert, sink,
                     nullptr);

    // 初始化时不添加时间水印，通过动态插入/移除来控制
    // 默认链接：source -> filter -> parser -> decoder -> m_convert -> tee
    if (!gst_element_link_many(source, filter, parser, decoder, m_convert, tee, nullptr)) {
        qWarning("无法链接GStreamer元素到tee");
        cleanupGStreamer();
        return false;
    }

    // 链接主视频流分支：queue_main -> flip -> preview_convert -> sink
    if (!gst_element_link_many(queue_main, flip, preview_convert, sink, nullptr)) {
        qWarning("无法链接主视频流分支");
        cleanupGStreamer();
        return false;
    }

    // 链接tee到主视频流队列
    GstPadTemplate *tee_src_pad_template = gst_element_class_get_pad_template(GST_ELEMENT_GET_CLASS(tee), "src_%u");

    // 主视频流pad
    GstPad *tee_main_pad = gst_element_request_pad(tee, tee_src_pad_template, NULL, NULL);
    GstPad *queue_main_pad = gst_element_get_static_pad(queue_main, "sink");

    // 链接pad
    if (gst_pad_link(tee_main_pad, queue_main_pad) != GST_PAD_LINK_OK) {
        gst_object_unref(tee_main_pad);
        gst_object_unref(queue_main_pad);
        qWarning("无法链接tee pad到主视频流");
        cleanupGStreamer();
        return false;
    }

    // 解引用不再需要的pad
    gst_object_unref(queue_main_pad);
    gst_object_unref(tee_main_pad);

    // 设置总线消息监听
    GstBus *bus = gst_pipeline_get_bus(GST_PIPELINE(pipeline));
    gst_bus_add_watch(bus, busCallback, this);
    gst_object_unref(bus);

    // 启动管道
    GstStateChangeReturn ret = gst_element_set_state(pipeline, GST_STATE_PLAYING);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        qWarning("unableToStartPipeline");
        cleanupGStreamer();
        return false;
    }

    qDebug() << "GStreamer is open" << devicePath << ", resolution ratio:" << width << "x" << height;

    // 初始化完成后，使用专门的恢复函数来恢复时间水印状态
    // 这个函数会检查管道状态并在合适的时机恢复时间水印
    QTimer::singleShot(200, this, &CameraStream::restoreTimeWatermarkOnInit);

    return true;
}



void CameraStream::cleanupGStreamer()
{
    if (pipeline) {
        if (isRecording) {

            // 直接向整个管道发送EOS信号，让GStreamer自己处理所有分支
            printf("向整个管道发送EOS信号...\n");
            bool eos_sent = gst_element_send_event(pipeline, gst_event_new_eos());

            if (eos_sent) {
                printf("EOS信号发送成功，等待处理完成...\n");

                // 等待EOS信号处理完成
                GstBus* bus = gst_element_get_bus(pipeline);
                GstMessage* msg = gst_bus_timed_pop_filtered(bus, 1 * GST_SECOND,
                                                             (GstMessageType)(GST_MESSAGE_EOS | GST_MESSAGE_ERROR));

                if (msg != nullptr) {
                    switch (GST_MESSAGE_TYPE(msg)) {
                    case GST_MESSAGE_EOS:
                        printf("EOS处理完成，录像已正常结束\n");
                        break;
                    case GST_MESSAGE_ERROR:
                        printf("等待EOS时发生错误，但继续清理\n");
                        break;
                    default:
                        break;
                    }
                    gst_message_unref(msg);
                } else {
                    printf("等待EOS超时，可能设备已断开，继续清理\n");
                }

                gst_object_unref(bus);

                // 给一些时间让所有元素完成数据写入
                printf("等待所有数据写入完成...\n");
                g_usleep(300000); // 等待300ms
            } else {
                printf("EOS信号发送失败，可能设备已断开\n");

                // 即使EOS发送失败，也要尝试强制刷新录像分支
                if (m_cached_record_branch) {
                    printf("强制刷新录像分支数据...\n");
                    gst_element_set_state(m_cached_record_branch, GST_STATE_PAUSED);
                    gst_element_get_state(m_cached_record_branch, NULL, NULL, 500 * GST_MSECOND);
                    g_usleep(200000); // 等待200ms
                }
            }

            isRecording = false; // 重置录像状态
            currentSegmentFilename.clear(); // 清空当前分段文件名
        } else {
            printf("非录像状态，直接关闭管道\n");
        }

        // 停止定时器
        if (m_photo_sink_timer && m_photo_sink_timer->isActive()) {
            m_photo_sink_timer->stop();
        }

        // 清理takePhoto中可能缓存的photo_sink
        if (m_cached_photo_sink) {
            printf("清理缓存的photo_sink\n");
            destroyPhotoSink(m_cached_photo_sink);
            m_cached_photo_sink = nullptr;
        }

        // 清理可能缓存的音频分支
        if (m_audio_source || m_audio_queue || m_audio_convert || m_audio_encoder || m_audio_parser) {
            printf("清理缓存的音频分支\n");
            destroyAudioBranch();
        }

        // 清理可能缓存的录像分支
        if (m_cached_record_branch) {
            printf("清理缓存的录像分支\n");
            destroyRecordBranch();
        }

        // 强制清理v4l2src设备资源
        printf("开始强制清理v4l2src设备资源...\n");

        // 首先尝试将pipeline设置为READY状态，这会释放设备资源但保持元素
        GstStateChangeReturn ret = gst_element_set_state(pipeline, GST_STATE_READY);
        if (ret != GST_STATE_CHANGE_FAILURE) {
            // 等待状态变化完成，确保设备资源被释放
            gst_element_get_state(pipeline, NULL, NULL, 1 * GST_SECOND);
            printf("Pipeline已设置为READY状态，设备资源应已释放\n");
        } else {
            printf("警告：无法将pipeline设置为READY状态\n");
        }

        // 再设置为NULL状态并等待完成
        ret = gst_element_set_state(pipeline, GST_STATE_NULL);
        if (ret != GST_STATE_CHANGE_FAILURE) {
            // 等待状态变化完成
            gst_element_get_state(pipeline, NULL, NULL, 1 * GST_SECOND);
            printf("Pipeline已设置为NULL状态\n");
        } else {
            printf("警告：无法将pipeline设置为NULL状态\n");
        }

        // 额外等待一段时间确保设备完全释放
        g_usleep(100000); // 等待100ms

        gst_object_unref(pipeline);
        pipeline = nullptr;

        // 清理parser相关成员变量
        parser = nullptr;
        decoder = nullptr;
        parser_removed = true; // 保持一致，不使用jpegparse

        // 清理预览分支的水印隔离元素
        m_preview_convert = nullptr;
        m_preview_capsfilter = nullptr;

        // 清理时间水印元素
        m_timeoverlay = nullptr;
        m_convert = nullptr;

        // 清理可能保存的数据
        if (pipeline) {
            g_object_set_data(G_OBJECT(pipeline), "old_parser", NULL);
        }
    }
}

gboolean CameraStream::busCallback(GstBus *bus, GstMessage *msg, gpointer data)
{
    Q_UNUSED(bus);
    CameraStream *stream = static_cast<CameraStream *>(data);

    switch (GST_MESSAGE_TYPE(msg)) {
    case GST_MESSAGE_ERROR: {
        GError *err = nullptr;
        gchar *debug = nullptr;
        gst_message_parse_error(msg, &err, &debug);
        qCritical("GStreamer erro: %s", err->message);
        if (debug)
            qCritical("debuggingDetails: %s", debug);

        // 检查特定的错误类型并发送信号
        QString errorMessage = QString::fromUtf8(err->message);
        QString errorDetails = debug ? QString::fromUtf8(debug) : QString();

        // 检查是否是摄像头运行时断开的错误（不需要弹窗）
        bool isRuntimeDisconnection = (errorMessage.contains("Could not read from resource", Qt::CaseInsensitive) &&
                                       errorDetails.contains("poll error", Qt::CaseInsensitive)) ||
                                      (errorMessage.contains("Failed to allocate a buffer", Qt::CaseInsensitive) &&
                                       errorDetails.contains("gst_v4l2src_create", Qt::CaseInsensitive));

        // 检查是否是摄像头打开时带宽不足的错误（需要弹窗）
        bool isBandwidthError = errorMessage.contains("Failed to allocate required memory", Qt::CaseInsensitive) &&
                                errorDetails.contains("Buffer pool activation failed", Qt::CaseInsensitive);

        // 检查其他内存分配错误或数据流错误
        bool isOtherMemoryError = errorMessage.contains("memory", Qt::CaseInsensitive) ||
                                  errorMessage.contains("Failed to allocate", Qt::CaseInsensitive);
        bool isStreamError = errorMessage.contains("data stream error", Qt::CaseInsensitive) ||
                             (errorDetails.contains("not-negotiated", Qt::CaseInsensitive) &&
                              errorDetails.contains("streaming stopped", Qt::CaseInsensitive));

        // 如果是我们关注的错误类型，则发送信号
        if (isRuntimeDisconnection || isBandwidthError || isOtherMemoryError || isStreamError) {
            if (stream) {
                // 发送错误信号，包含错误信息、详细信息和通道ID
                // 同时传递错误类型信息，用于决定是否显示弹窗
                QString errorType;
                if (isRuntimeDisconnection) {
                    errorType = "RUNTIME_DISCONNECTION";  // 运行时断开，不显示弹窗
                } else if (isBandwidthError) {
                    errorType = "BANDWIDTH_ERROR";        // 带宽不足，显示弹窗
                } else {
                    errorType = "OTHER_ERROR";            // 其他错误，显示弹窗
                }

                QMetaObject::invokeMethod(stream, "gstreamerErrorOccurred",
                                          Qt::QueuedConnection,
                                          Q_ARG(QString, errorMessage),
                                          Q_ARG(QString, errorDetails + "|TYPE:" + errorType),
                                          Q_ARG(int, stream->channelstream));

                // 出现严重错误后尝试清理资源
                stream->cleanupGStreamer();
            }
        }

        g_error_free(err);
        g_free(debug);
        break;
    }
    case GST_MESSAGE_EOS:
        qDebug() << "stream ended";
        break;
    case GST_MESSAGE_STATE_CHANGED: {
        GstState old_state, new_state, pending_state;
        gst_message_parse_state_changed(msg, &old_state, &new_state, &pending_state);
        // qDebug() << "stateFrom " << gst_element_state_get_name(old_state)
        //          << " becomes " << gst_element_state_get_name(new_state);
        break;
    }
    default:
        break;
    }
    return TRUE;
}


int CameraStream::GetRecordBitRate(int width, int height, int fps, int quality)
{
    int base_bitrate[] = { 8000000, 5000000, 3000000 };
    float scale = static_cast<float>(width * height * fps) / (1920 * 1080 * 30);
    return static_cast<int>(base_bitrate[quality] * scale);
}

// 动态创建录像分支
GstElement* CameraStream::createRecordBranch(const QString &filePath, int quality, int maxSizeTime, qint64 maxSizeBytes)
{
    if (!pipeline || !tee) {
        printf("无法创建录像分支：pipeline或tee未初始化\n");
        return nullptr;
    }

    printf("正在创建录像分支...\n");

    // 获取MainWindow引用
    MainWindow* m_mmainWindow = qobject_cast<MainWindow*>(parent());
    int currentChannel = channelstream;

    // 创建录像分支的元素（简化，不包含时间水印）
    GstElement *queue_rec = gst_element_factory_make("queue", "queue_rec");
    GstElement *encoder = gst_element_factory_make("mpph264enc", "encoder");
    GstElement *h264parse = gst_element_factory_make("h264parse", "h264parse");
    GstElement *splitmuxsink = gst_element_factory_make("splitmuxsink", "splitmuxsink");

    if (!queue_rec || !encoder || !h264parse || !splitmuxsink) {
        printf("无法创建录像分支的元素\n");
        if (queue_rec) gst_object_unref(queue_rec);
        if (encoder) gst_object_unref(encoder);
        if (h264parse) gst_object_unref(h264parse);
        if (splitmuxsink) gst_object_unref(splitmuxsink);
        return nullptr;
    }

    // 配置队列 - 修复：优化队列设置以减少延迟
    g_object_set(queue_rec,
                 "max-size-buffers", 60,  // 减少缓冲区大小，降低延迟
                 "max-size-time", 1 * GST_SECOND,  // 减少时间缓冲，降低延迟
                 "max-size-bytes", 0,     // 不限制字节大小
                 "leaky", 2,              // 下游丢帧
                 "flush-on-eos", TRUE,    // EOS时刷新队列
                 NULL);



    // 配置编码器 - 修复：优化编码器设置以减少延迟
    g_object_set(encoder,
                 "bps", quality,
                 "gop", 30,  // 保持GOP为30帧，确保1秒一个关键帧
                 "profile", 66, // Baseline profile
                 "rc-mode", 1,   // CBR模式，确保码率稳定
                 "qp-init", 25,  // 初始QP值
                 "qp-min", 10,   // 最小QP值
                 "qp-max", 40,   // 最大QP值
                 NULL);

    // 配置splitmuxsink

    if (m_mmainWindow && m_mmainWindow->RecordingSettings[currentChannel].segmentedVideo) {
        if (m_mmainWindow->RecordingSettings[currentChannel].recordingByTime && maxSizeTime > 0) {
            // 按时间分段
            guint64 adjustedTime = maxSizeTime * GST_SECOND;

            g_object_set(splitmuxsink,
                         "max-size-time", adjustedTime,
                         "muxer", gst_element_factory_make("mp4mux", "muxer"),
                         "send-keyframe-requests", TRUE,  // 请求关键帧，确保切换点准确
                         NULL);
            g_signal_connect(splitmuxsink, "format-location", G_CALLBACK(global_format_location_callback), this);
            printf("设置时间分段: %d秒 (调整后: %lu秒)\n", maxSizeTime, adjustedTime / GST_SECOND);
        }
        else if (m_mmainWindow->RecordingSettings[currentChannel].byFileSize && maxSizeBytes > 0) {
            // 按大小分段
            g_object_set(splitmuxsink,
                         "max-size-bytes", maxSizeBytes,
                         "muxer", gst_element_factory_make("mp4mux", "muxer"),
                         NULL);
            g_signal_connect(splitmuxsink, "format-location", G_CALLBACK(global_format_location_callback), this);
            printf("设置大小分段: %lld字节\n", maxSizeBytes);
        }
        else {
            // 不分段
            g_object_set(splitmuxsink,
                         "location", filePath.toUtf8().constData(),
                         "muxer", gst_element_factory_make("mp4mux", "muxer"),
                         NULL);
        }
    } else {
        // 不分段
        g_object_set(splitmuxsink,
                     "location", filePath.toUtf8().constData(),
                     "muxer", gst_element_factory_make("mp4mux", "muxer"),
                     NULL);
    }

    // 将元素添加到管道
    gst_bin_add_many(GST_BIN(pipeline), queue_rec, encoder, h264parse, splitmuxsink, NULL);

    // 链接元素：queue_rec -> encoder -> h264parse -> splitmuxsink
    bool linkSuccess = gst_element_link_many(queue_rec, encoder, h264parse, splitmuxsink, NULL);
    if (!linkSuccess) {
        printf("无法链接录像分支元素\n");
        gst_bin_remove_many(GST_BIN(pipeline), queue_rec, encoder, h264parse, splitmuxsink, NULL);
        return nullptr;
    }

    if (!linkSuccess) {
        return nullptr;
    }

    // 从tee获取pad
    GstPadTemplate *tee_src_pad_template = gst_element_class_get_pad_template(GST_ELEMENT_GET_CLASS(tee), "src_%u");
    m_record_tee_pad = gst_element_request_pad(tee, tee_src_pad_template, NULL, NULL);
    m_record_queue_pad = gst_element_get_static_pad(queue_rec, "sink");


    // 存储引用到sink的私有数据中，以便后续清理
    g_object_set_data(G_OBJECT(splitmuxsink), "queue", queue_rec);
    g_object_set_data(G_OBJECT(splitmuxsink), "encoder", encoder);
    g_object_set_data(G_OBJECT(splitmuxsink), "h264parse", h264parse);

    // 不立即链接tee_pad和queue_pad，通过activateRecordBranch控制

    // 同步元素状态
    gst_element_sync_state_with_parent(queue_rec);
    gst_element_sync_state_with_parent(encoder);
    gst_element_sync_state_with_parent(h264parse);
    gst_element_sync_state_with_parent(splitmuxsink);
    GstIterator *it = gst_element_iterate_src_pads(tee);
    GValue val = G_VALUE_INIT;
    while (gst_iterator_next(it, &val) == GST_ITERATOR_OK) {
        GstPad *pad = (GstPad *)g_value_get_object(&val);
        gchar *name = gst_pad_get_name(pad);
        printf("tee src pad: %s\n", name);
        g_free(name);
        g_value_unset(&val);
    }
    gst_iterator_free(it);

    printf("录像分支创建成功\n");
    return splitmuxsink;
}

// 销毁录像分支
void CameraStream::destroyRecordBranch()
{
    if (!m_cached_record_branch) {
        printf("录像分支已为空，无需销毁\n");
        return;
    }

    if (!pipeline) {
        printf("管道已为空，直接清理录像分支引用\n");
        m_cached_record_branch = nullptr;
        m_record_tee_pad = nullptr;
        m_record_queue_pad = nullptr;
        return;
    }

    printf("正在销毁录像分支...\n");

    // 先停用录像分支
    deactivateRecordBranch();

    // 获取之前存储的元素引用
    GstElement *queue_rec = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "queue");
    GstElement *timeoverlay = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "clockoverlay");
    GstElement *encoder = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "encoder");
    GstElement *h264parse = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "h264parse");

    if (!queue_rec || !encoder || !h264parse) {
        printf("无法获取录像分支的元素引用，可能已被释放\n");
    } else {
        // 将元素状态设置为NULL，并等待状态变化完成
        printf("设置录像分支元素状态为NULL...\n");
        gst_element_set_state(m_cached_record_branch, GST_STATE_NULL);
        gst_element_set_state(h264parse, GST_STATE_NULL);
        gst_element_set_state(encoder, GST_STATE_NULL);
        if (timeoverlay) {
            gst_element_set_state(timeoverlay, GST_STATE_NULL);
        }
        gst_element_set_state(queue_rec, GST_STATE_NULL);

        // 等待状态变化完成
        gst_element_get_state(m_cached_record_branch, NULL, NULL, 1 * GST_SECOND);
        gst_element_get_state(h264parse, NULL, NULL, 500 * GST_MSECOND);
        gst_element_get_state(encoder, NULL, NULL, 500 * GST_MSECOND);
        if (timeoverlay) {
            gst_element_get_state(timeoverlay, NULL, NULL, 500 * GST_MSECOND);
        }
        gst_element_get_state(queue_rec, NULL, NULL, 500 * GST_MSECOND);
    }

    // 安全地释放pad引用
    if (m_record_tee_pad && tee) {
        printf("释放tee的请求pad...\n");
        gst_element_release_request_pad(tee, m_record_tee_pad);
        gst_object_unref(m_record_tee_pad);
        m_record_tee_pad = nullptr;
    }

    if (m_record_queue_pad) {
        gst_object_unref(m_record_queue_pad);
        m_record_queue_pad = nullptr;
    }

    // 从管道中移除元素
    if (queue_rec && encoder && h264parse) {
        printf("从管道中移除录像分支元素...\n");
        if (timeoverlay) {
            gst_bin_remove_many(GST_BIN(pipeline), queue_rec, timeoverlay, encoder, h264parse, m_cached_record_branch, NULL);
        } else {
            gst_bin_remove_many(GST_BIN(pipeline), queue_rec, encoder, h264parse, m_cached_record_branch, NULL);
        }
    } else {
        printf("部分元素引用丢失，尝试单独移除splitmuxsink...\n");
        gst_bin_remove(GST_BIN(pipeline), m_cached_record_branch);
    }

    m_cached_record_branch = nullptr;

    // 录像结束后，移除水印隔离元素，恢复原始预览链路
    printf("录像结束，移除水印隔离元素\n");
    removeWatermarkIsolationElements();

    printf("录像分支已销毁\n");

    // 检查原始文件名
    if (!recordingfilename.isEmpty()) {
        printf("检查原始文件: %s\n", qPrintable(recordingfilename));
        if (QFile::exists(recordingfilename)) {
            QFileInfo fileInfo(recordingfilename);
            printf("原始文件存在，大小: %lld 字节\n", fileInfo.size());
        } else {
            printf("原始文件不存在\n");
        }
    }

    // 检查分段文件名（如果有）
    if (!currentSegmentFilename.isEmpty()) {
        printf("检查分段文件: %s\n", qPrintable(currentSegmentFilename));
        if (QFile::exists(currentSegmentFilename)) {
            QFileInfo fileInfo(currentSegmentFilename);
            printf("分段文件存在，大小: %lld 字节\n", fileInfo.size());
        } else {
            printf("分段文件不存在\n");
        }
    }

    // 检查目录中的所有mp4文件
    QString dirPath = QFileInfo(recordingfilename).absolutePath();

    if (!recordingfilename.isEmpty() && QFile::exists(recordingfilename)) {
        QFileInfo fileInfo(recordingfilename);
        if (fileInfo.size() > 0) {
            printf("录像文件已保存: %s (大小: %lld 字节)\n",
                   qPrintable(recordingfilename), fileInfo.size());
        } else {
            printf("警告: 录像文件大小为0，可能未正确保存\n");
        }
    } else {
        printf("警告: 录像文件不存在或路径为空\n");
    }
}


// 激活录像分支（链接tee_pad和queue_pad）
bool CameraStream::activateRecordBranch()
{
    printf("开始激活录像分支...\n");

    if (!m_record_tee_pad || !m_record_queue_pad) {
        printf("无法激活录像分支：pad未初始化 (tee_pad=%p, queue_pad=%p)\n",
               m_record_tee_pad, m_record_queue_pad);
        return false;
    }

    if (!tee || !m_cached_record_branch) {
        printf("无法激活录像分支：tee或录像分支未初始化 (tee=%p, record_branch=%p)\n",
               tee, m_cached_record_branch);
        return false;
    }

    // 检查pad是否已经连接
    if (gst_pad_is_linked(m_record_tee_pad)) {
        printf("录像分支pad已连接，无需重复激活\n");
        isRecording = true;
        return true;
    }

    printf("准备链接tee pad到录像分支...\n");

    // 激活pad
    gst_pad_set_active(m_record_tee_pad, TRUE);

    // 链接pad
    GstPadLinkReturn link_result = gst_pad_link(m_record_tee_pad, m_record_queue_pad);
    if (link_result != GST_PAD_LINK_OK) {
        printf("无法链接tee到录像分支，错误代码: %d\n", link_result);

        // 打印详细错误信息
        switch (link_result) {
        case GST_PAD_LINK_WRONG_HIERARCHY:
            printf("错误：pad不在同一管道中\n");
            break;
        case GST_PAD_LINK_WAS_LINKED:
            printf("错误：pad已经连接\n");
            break;
        case GST_PAD_LINK_WRONG_DIRECTION:
            printf("错误：pad方向错误\n");
            break;
        case GST_PAD_LINK_NOFORMAT:
            printf("错误：pad格式不兼容\n");
            break;
        case GST_PAD_LINK_NOSCHED:
            printf("错误：调度器问题\n");
            break;
        case GST_PAD_LINK_REFUSED:
            printf("错误：连接被拒绝\n");
            break;
        default:
            printf("错误：未知连接错误\n");
            break;
        }
        return false;
    }

    printf("录像分支pad连接成功\n");

    // 检查splitmuxsink状态
    GstState state, pending;
    GstStateChangeReturn ret = gst_element_get_state(m_cached_record_branch, &state, &pending, 0);
    printf("splitmuxsink状态: %s (pending: %s)\n",
           gst_element_state_get_name(state),
           gst_element_state_get_name(pending));

    printf("录像分支已激活\n");
    isRecording = true;

    return true;
}

// 停用录像分支（断开tee_pad和queue_pad）
bool CameraStream::deactivateRecordBranch()
{
    if (!m_record_tee_pad || !m_record_queue_pad) {
        printf("录像分支pad未初始化或已释放\n");
        isRecording = false;
        return true; // 不是错误，可能已经停用了
    }

    // 检查连接状态并断开
    if (gst_pad_is_linked(m_record_tee_pad)) {
        printf("断开录像分支pad连接...\n");
        if (gst_pad_unlink(m_record_tee_pad, m_record_queue_pad)) {
            printf("录像分支pad连接已断开\n");
        } else {
            printf("警告：断开录像分支pad连接失败\n");
        }
    } else {
        printf("录像分支pad已断开或未连接\n");
    }

    printf("录像分支已停用\n");
    isRecording = false;

    return true;
}

void CameraStream::startRecording(){
    // 防止重复调用
    if (isRecording) {
        printf("录像已在进行中，忽略重复调用\n");
        return;
    }

    int width=0, height=0, framerate_num=0, framerate_den=0;
    MainWindow* m_mmainWindow = qobject_cast<MainWindow*>(parent());
    if (!m_mmainWindow) {
        printf("无法获取MainWindow引用\n");
        return;
    }

    int currentChannel = channelstream;

    // 检查通道索引有效性
    if (currentChannel < 0 || currentChannel >= 4) {
        printf("无效的通道索引: %d\n", currentChannel);
        return;
    }

    // 详细调试RecordingSettings
    printf("=== 录像设置调试信息 ===\n");
    printf("当前通道: %d\n", currentChannel);
    printf("RecordingSettings[%d].path = '%s'\n", currentChannel,
           qPrintable(m_mmainWindow->RecordingSettings[currentChannel].path));
    printf("RecordingSettings[%d].name = '%s'\n", currentChannel,
           qPrintable(m_mmainWindow->RecordingSettings[currentChannel].name));
    printf("RecordingSettings[%d].segmentedVideo = %s\n", currentChannel,
           m_mmainWindow->RecordingSettings[currentChannel].segmentedVideo ? "true" : "false");
    printf("RecordingSettings[%d].recordingQuality = %d\n", currentChannel,
           m_mmainWindow->RecordingSettings[currentChannel].recordingQuality);

    int recordingByTime = m_mmainWindow->RecordingSettings[currentChannel].segmentedTime;
    recordingByTime = recordingByTime * 60;
    int recordingQuality = m_mmainWindow->RecordingSettings[currentChannel].recordingQuality;

    // 获取基础路径并构建录制路径
    QString basePath = m_mmainWindow->RecordingSettings[currentChannel].basePath;
    if (basePath.isEmpty()) {
        // 如果basePath为空，尝试从path字段获取或使用默认值
        QString fullPath = m_mmainWindow->RecordingSettings[currentChannel].path;
        if (!fullPath.isEmpty()) {
            // 从完整路径中提取基础路径（向后兼容）
            if (fullPath.contains("/Recording/CH")) {
                basePath = fullPath.left(fullPath.indexOf("/Recording/CH"));
            } else {
                basePath = fullPath;
            }
        } else {
            basePath = "/mnt/nvme";
        }
        // 更新basePath字段
        m_mmainWindow->RecordingSettings[currentChannel].basePath = basePath;
    }

    // 构建完整的录制路径：basePath/Recording/CHx
    recordingpath = QString("%1/Recording/CH%2").arg(basePath).arg(currentChannel + 1);
    segmentedSize = m_mmainWindow->RecordingSettings[currentChannel].segmentedSize * 1024 * 1024;

    printf("基础路径: '%s'\n", qPrintable(basePath));
    printf("最终录像路径: '%s'\n", qPrintable(recordingpath));
    qDebug() << "basePath=" << basePath << "recordingpath=" << recordingpath;

    // 自动创建录制目录结构
    QDir baseDir(basePath);
    if (!baseDir.exists()) {
        if (!baseDir.mkpath(".")) {
            printf("错误：无法创建基础目录 %s\n", qPrintable(basePath));
            return;
        }
        printf("成功创建基础目录: %s\n", qPrintable(basePath));
    }

    QString recordingDir = basePath + "/Recording";
    QDir recDir(recordingDir);
    if (!recDir.exists()) {
        if (!recDir.mkpath(".")) {
            printf("错误：无法创建Recording目录 %s\n", qPrintable(recordingDir));
            return;
        }
        printf("成功创建Recording目录: %s\n", qPrintable(recordingDir));
    }

    QDir channelDir(recordingpath);
    if (!channelDir.exists()) {
        if (!channelDir.mkpath(".")) {
            printf("错误：无法创建通道目录 %s\n", qPrintable(recordingpath));
            return;
        }
        printf("成功创建通道目录: %s\n", qPrintable(recordingpath));
    }
    // 检查路径是否存在，不存在则创建
    QDir dir(recordingpath);
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            printf("无法创建录像目录: %s\n", qPrintable(recordingpath));
            return;
        }
    }

    if (!currentResolution.isEmpty()) {
        width = currentResolution["width"].toInt();
        height = currentResolution["height"].toInt();

        // 如果有帧率信息，则使用
        if (currentResolution.contains("framerate_num") && currentResolution.contains("framerate_den")) {
            framerate_num = currentResolution["framerate_num"].toInt();
            framerate_den = currentResolution["framerate_den"].toInt();
            qDebug() << "setFrameRate: " << framerate_num << "/" << framerate_den << " fps";
        }
    }

    // 计算合适的码率
    Quality = GetRecordBitRate(width, height, framerate_num, recordingQuality);

    // 确保摄像头已经初始化并处于预览状态
    if (!pipeline) {
        printf("管道未初始化，先初始化摄像头\n");
        // 如果管道不存在，需要先初始化
        initializeGStreamer(currentDevicePath, currentFormat, currentResolution);
        if (!pipeline) {
            printf("无法初始化摄像头管道\n");
            return;
        }
    }

    // 检查tee元素是否存在
    if (!tee) {
        printf("tee元素未初始化，无法开始录像\n");
        return;
    }

    // 记录录像开始时间和初始化时间跟踪
    recordingStartTime = QDateTime::currentDateTime();
    lastSegmentTime = recordingStartTime;
    totalRecordedTime = 0;

    // 设置期望的分段时长
    if (m_mmainWindow->RecordingSettings[currentChannel].segmentedVideo &&
        m_mmainWindow->RecordingSettings[currentChannel].recordingByTime) {
        expectedSegmentDuration = recordingByTime * 1000; // 转换为毫秒
        printf("设置期望分段时长: %lld毫秒 (%d秒)\n", expectedSegmentDuration, recordingByTime);
    }

    // 使用Qt方式获取当前日期和时间
    QString datePath = QDateTime::currentDateTime().toString("yyyy_MM_dd"); // 日期子目录
    QString fileName = QDateTime::currentDateTime().toString("yyyy-MM-dd_hh-mm-ss-zzz"); // 文件名时间戳

    // 检查并添加录像前缀名
    QString recordingPrefix = m_mmainWindow->RecordingSettings[currentChannel].name;
    if (!recordingPrefix.isEmpty()) {
        fileName = recordingPrefix + fileName;
        printf("添加录像前缀: %s，完整文件名: %s\n",
               recordingPrefix.toUtf8().constData(), fileName.toUtf8().constData());
    }

    // 拼接完整的日期目录
    QString fullPath;
    if (recordingpath.endsWith("/")) {
        fullPath = recordingpath + datePath;
    } else {
        fullPath = recordingpath + "/" + datePath;
    }

    // 创建目录
    QDir dateDir(fullPath);
    if (!dateDir.exists()) {
        dateDir.mkpath(".");
    }

    // 用Qt方式生成文件名
    QString filePath = fullPath + "/" + fileName + ".mp4";
    recordingfilename = filePath;
    currentSegmentFilename.clear(); // 清空之前的分段文件名

    // 打印关键信息
    printf("录制初始化 - 文件: %s\n", qPrintable(recordingfilename));

    // 检查是否已经有录像分支
    if (m_cached_record_branch) {
        // 如果有，先销毁它
        destroyRecordBranch();
    }

    // 检查是否需要录制音频
    bool enableAudio = m_mmainWindow->curaudiostate[currentChannel];
    QString audioDevice;

    if (enableAudio) {
        // 获取当前选择的音频设备
        QComboBox* audioComboBox = m_mmainWindow->findChild<QComboBox*>("comboBox_audiodev");
        if (audioComboBox && audioComboBox->currentIndex() >= 0) {
            audioDevice = audioComboBox->currentData().toString();
            printf("音频录制已启用，设备: %s\n", qPrintable(audioDevice));
        } else {
            printf("音频录制已启用但未找到有效音频设备\n");
            enableAudio = false;
        }
    } else {
        printf("音频录制未启用\n");
    }

    // 先创建音频分支（如果需要），但不激活
    bool audioSuccess = false;
    if (enableAudio && !audioDevice.isEmpty()) {
        printf("尝试创建音频分支...\n");
        if (createAudioBranch(audioDevice)) {
            printf("音频分支创建成功\n");
            audioSuccess = true;
        } else {
            printf("无法创建音频分支，继续进行视频录制\n");
        }
    }

    // 创建新的录像分支
    m_cached_record_branch = createRecordBranch(
        recordingfilename,
        Quality,
        m_mmainWindow->RecordingSettings[currentChannel].recordingByTime ? recordingByTime : 0,
        m_mmainWindow->RecordingSettings[currentChannel].byFileSize ? segmentedSize : 0
        );

    if (!m_cached_record_branch) {
        printf("无法创建录像分支\n");
        if (audioSuccess) {
            destroyAudioBranch();
        }
        return;
    }

    // 激活录像分支
    if (!activateRecordBranch()) {
        printf("无法激活录像分支\n");
        destroyRecordBranch();
        if (audioSuccess) {
            destroyAudioBranch();
        }
        return;
    }

    // 验证录像分支是否真正激活
    if (!isRecording) {
        printf("错误：录像分支激活后isRecording仍为false\n");
        destroyRecordBranch();
        if (audioSuccess) {
            destroyAudioBranch();
        }
        return;
    }

    printf("录像分支已成功激活，isRecording = %s\n", isRecording ? "true" : "false");

    // 激活音频分支（如果已创建）
    if (audioSuccess) {
        printf("尝试激活音频分支...\n");
        if (activateAudioBranch()) {
            printf("音频分支已成功激活\n");
        } else {
            printf("无法激活音频分支，继续进行视频录制\n");
            destroyAudioBranch();
            audioSuccess = false;
        }
    }

    if (enableAudio && !audioSuccess) {
        printf("音频录制失败，但视频录制将继续\n");
    }

    // 在开始录像前将旋转状态重置为正常
    if (rotate_direction != 0) {
        printf("录像前重置旋转状态为正常\n");
        rotate_direction = 0;
        if (flip) {
            g_object_set(flip, "video-direction", rotate_direction, nullptr);
        }
    }

    // 不再需要移除jpegparse，因为初始化时就没有添加jpegparse
    printf("Channel %d: 使用identity parser，无需移除jpegparse\n", channelstream);

    // 通过MainWindow的公有接口禁用所有相机控制
    if (m_mmainWindow) {
        m_mmainWindow->disableCameraControls();
    }

    // 启动检查文件大小的定时器
    if (checkFileSizeTimer) {
        if (checkFileSizeTimer->isActive()) {
            checkFileSizeTimer->stop();
        }
        checkFileSizeTimer->start(1000); // 每秒检查一次，内部会控制实际检查频率
    }
}

void CameraStream::stopRecording(){
    // 防止重复调用
    if (!isRecording) {
        printf("录像未在进行中，忽略停止调用\n");
        return;
    }

    printf("开始停止录像...\n");

    // 停止定时器
    if (checkFileSizeTimer && checkFileSizeTimer->isActive()) {
        checkFileSizeTimer->stop();
    }

    // 清除录像开始时间并输出最终统计
    if (recordingStartTime.isValid()) {
        QDateTime stopTime = QDateTime::currentDateTime();
        qint64 totalActualTime = recordingStartTime.msecsTo(stopTime);

        printf("录像结束统计:\n");
        printf("  开始时间: %s\n", recordingStartTime.toString("yyyy-MM-dd hh:mm:ss.zzz").toUtf8().constData());
        printf("  结束时间: %s\n", stopTime.toString("yyyy-MM-dd hh:mm:ss.zzz").toUtf8().constData());
        printf("  实际总时长: %lld毫秒 (%.2f分钟)\n", totalActualTime, totalActualTime / 60000.0);
        printf("  分段累计时长: %lld毫秒 (%.2f分钟)\n", totalRecordedTime, totalRecordedTime / 60000.0);
        printf("  时间差异: %lld毫秒\n", totalActualTime - totalRecordedTime);
    }

    recordingStartTime = QDateTime();
    lastSegmentTime = QDateTime();
    totalRecordedTime = 0;
    expectedSegmentDuration = 0;

    // 停用录像分支
    if (m_cached_record_branch) {
        printf("正在结束录像，确保正确关闭文件...\n");

        // 获取录像分支的所有元素
        GstElement *queue_rec = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "queue");
        GstElement *encoder = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "encoder");
        GstElement *h264parse = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "h264parse");

        if (!queue_rec || !encoder || !h264parse) {
            printf("无法获取录像分支的元素引用，可能已被释放\n");
        } else {
            // 确保录像分支与管道连接，但停止新数据流入
            // 停用pad连接但不断开（暂时保持连接但阻止新数据）
            if (m_record_tee_pad && m_record_queue_pad && gst_pad_is_linked(m_record_tee_pad)) {
                printf("开始优雅地停止录像分支...\n");

                // 设置probe阻止新数据通过
                gulong probe_id = gst_pad_add_probe(m_record_tee_pad,
                                                    GST_PAD_PROBE_TYPE_BLOCK_DOWNSTREAM,
                                                    (GstPadProbeCallback)NULL, NULL, NULL);
                printf("已阻止新数据进入录像分支\n");

                // 让已有数据有时间流入分支
                g_usleep(100000); // 等待100ms

                // 发送EOS信号到视频和音频分支
                bool video_eos_sent = false;
                bool audio_eos_sent = false;

                // 向音频分支发送EOS（如果存在）
                if (m_audio_source) {
                    printf("向音频分支发送EOS...\n");
                    if (gst_element_send_event(m_audio_source, gst_event_new_eos())) {
                        printf("向音频源发送EOS成功\n");
                        audio_eos_sent = true;
                    } else if (m_audio_queue && gst_element_send_event(m_audio_queue, gst_event_new_eos())) {
                        printf("向音频队列发送EOS成功\n");
                        audio_eos_sent = true;
                    } else if (m_audio_convert && gst_element_send_event(m_audio_convert, gst_event_new_eos())) {
                        printf("向音频转换器发送EOS成功\n");
                        audio_eos_sent = true;
                    } else {
                        printf("向音频分支发送EOS失败\n");
                    }
                }

                // 向视频分支发送EOS
                if (gst_element_send_event(queue_rec, gst_event_new_eos())) {
                    printf("向queue_rec发送EOS成功\n");
                    video_eos_sent = true;
                } else {
                    printf("向queue_rec发送EOS失败，尝试其他元素\n");

                    // 如果失败，尝试向encoder发送
                    if (gst_element_send_event(encoder, gst_event_new_eos())) {
                        printf("向encoder发送EOS成功\n");
                        video_eos_sent = true;
                    } else {
                        printf("向encoder发送EOS失败，尝试h264parse\n");

                        // 再尝试向h264parse发送
                        if (gst_element_send_event(h264parse, gst_event_new_eos())) {
                            printf("向h264parse发送EOS成功\n");
                            video_eos_sent = true;
                        } else {
                            printf("向h264parse发送EOS失败，尝试muxsink\n");

                            // 最后尝试向splitmuxsink发送
                            if (gst_element_send_event(m_cached_record_branch, gst_event_new_eos())) {
                                printf("向splitmuxsink发送EOS成功\n");
                                video_eos_sent = true;
                            } else {
                                printf("所有视频EOS发送尝试均失败\n");
                            }
                        }
                    }
                }

                // 检查EOS发送结果
                bool eos_sent = video_eos_sent || audio_eos_sent;
                printf("EOS发送结果: 视频=%s, 音频=%s\n",
                       video_eos_sent ? "成功" : "失败",
                       audio_eos_sent ? "成功" : "失败");

                if (eos_sent) {
                    // 等待EOS信号处理完成
                    printf("等待EOS处理完成...\n");

                    // 先等待同步状态变化，确保元素开始处理EOS
                    gst_element_get_state(m_cached_record_branch, NULL, NULL,500 * GST_MSECOND);

                    // 如果有音频分支，也等待音频元素状态变化
                    if (m_audio_source) {
                        gst_element_get_state(m_audio_source, NULL, NULL, 500 * GST_MSECOND);
                    }

                    // 然后等待EOS或错误消息，增加等待时间以确保音频处理完成
                    GstBus* bus = gst_element_get_bus(pipeline);
                    GstMessage* msg = gst_bus_timed_pop_filtered(bus, 100 * GST_MSECOND,
                                                                 (GstMessageType)(GST_MESSAGE_EOS | GST_MESSAGE_ERROR));

                    if (msg) {
                        switch (GST_MESSAGE_TYPE(msg)) {
                        case GST_MESSAGE_EOS:
                            printf("EOS处理完成，录像已正常结束\n");
                            break;
                        case GST_MESSAGE_ERROR:
                            printf("处理EOS时发生错误\n");
                            {
                                GError *err = nullptr;
                                gchar *debug = nullptr;
                                gst_message_parse_error(msg, &err, &debug);
                                printf("错误: %s\n", err->message);
                                if (debug) printf("调试信息: %s\n", debug);
                                g_error_free(err);
                                g_free(debug);
                            }
                            break;
                        default:
                            break;
                        }
                        gst_message_unref(msg);
                    } else {
                        printf("等待EOS超时，但可能已成功处理\n");
                    }

                    gst_object_unref(bus);
                }

                // 移除probe
                if (m_record_tee_pad) {
                    gst_pad_remove_probe(m_record_tee_pad, probe_id);
                }

                // 额外等待500ms，确保所有音频和视频数据都被处理
                g_usleep(300000);

                // 确保所有元素进入暂停状态，这样会处理所有缓冲的数据
                printf("设置录像分支元素为暂停状态...\n");
                gst_element_set_state(m_cached_record_branch, GST_STATE_PAUSED);
                gst_element_set_state(h264parse, GST_STATE_PAUSED);
                gst_element_set_state(encoder, GST_STATE_PAUSED);
                gst_element_set_state(queue_rec, GST_STATE_PAUSED);

                // 同时设置音频元素为暂停状态
                if (m_audio_source) {
                    printf("设置音频分支元素为暂停状态...\n");
                    gst_element_set_state(m_audio_source, GST_STATE_PAUSED);
                    gst_element_set_state(m_audio_queue, GST_STATE_PAUSED);
                    gst_element_set_state(m_audio_convert, GST_STATE_PAUSED);
                    gst_element_set_state(m_audio_encoder, GST_STATE_PAUSED);
                    gst_element_set_state(m_audio_parser, GST_STATE_PAUSED);
                }

                // 等待状态变化完成
                gst_element_get_state(m_cached_record_branch, NULL, NULL, 1 * GST_SECOND);
                if (m_audio_source) {
                    gst_element_get_state(m_audio_source, NULL, NULL, 500 * GST_MSECOND);
                }

                // 最后断开tee和queue的连接
                if (m_record_tee_pad && m_record_queue_pad && gst_pad_is_linked(m_record_tee_pad)) {
                    gst_pad_unlink(m_record_tee_pad, m_record_queue_pad);
                    printf("断开tee连接\n");
                }
            } else {
                printf("录像分支pad未连接或已断开\n");
            }
        }

        // 确保文件已经关闭
        printf("设置所有元素为NULL状态以确保资源释放...\n");

        // 先设置音频元素为NULL状态
        if (m_audio_source) {
            printf("设置音频元素为NULL状态...\n");
            gst_element_set_state(m_audio_source, GST_STATE_NULL);
            gst_element_set_state(m_audio_queue, GST_STATE_NULL);
            gst_element_set_state(m_audio_convert, GST_STATE_NULL);
            gst_element_set_state(m_audio_encoder, GST_STATE_NULL);
            gst_element_set_state(m_audio_parser, GST_STATE_NULL);

            // 等待音频元素状态变化完成
            gst_element_get_state(m_audio_source, NULL, NULL, 500 * GST_MSECOND);
        }

        // 再设置视频元素为NULL状态
        if (m_cached_record_branch) {
            printf("设置视频元素为NULL状态...\n");
            gst_element_set_state(m_cached_record_branch, GST_STATE_NULL);
            if (queue_rec) gst_element_set_state(queue_rec, GST_STATE_NULL);
            if (encoder) gst_element_set_state(encoder, GST_STATE_NULL);
            if (h264parse) gst_element_set_state(h264parse, GST_STATE_NULL);

            // 等待状态变化完成
            gst_element_get_state(m_cached_record_branch, NULL, NULL, 500 * GST_MSECOND);
        }

        // 销毁音频分支（如果存在）
        if (m_audio_source || m_audio_queue || m_audio_convert || m_audio_encoder || m_audio_parser) {
            printf("销毁音频分支...\n");
            destroyAudioBranch();
        }

        // 销毁录像分支
        destroyRecordBranch();
        printf("录像分支已销毁，文件应已正确关闭\n");

        if (QFile::exists(recordingfilename)) {
            printf("录像文件已成功保存: %s\n", qPrintable(recordingfilename));
        } else {
            printf("警告：未找到录像文件，可能未成功保存\n");

            // 检查分段文件
            if (!currentSegmentFilename.isEmpty() && QFile::exists(currentSegmentFilename)) {
                printf("但找到分段文件: %s\n", qPrintable(currentSegmentFilename));
            }
        }
    }
    // 清空分段文件名
    currentSegmentFilename.clear();

    // 重新启用UI控件，通过MainWindow的公有接口
    MainWindow* m_mmainWindow = qobject_cast<MainWindow*>(parent());
    if (m_mmainWindow) {
        m_mmainWindow->enableCameraControls();
    }
}

void CameraStream::checkFileSize_time()
{
    // 调用 du 命令
    MainWindow* m_mmainWindow = qobject_cast<MainWindow*>(parent()); // 将当前对象的父对象强制转换为 MainWindow 类型
    if (!m_mmainWindow) return;

    // 使用channelstream变量代替get_curchannel()
    int currentChannel = channelstream;

    // 获取基础路径并构建录制路径
    QString basePath = m_mmainWindow->RecordingSettings[currentChannel].basePath;
    if (basePath.isEmpty()) {
        // 如果basePath为空，尝试从path字段获取或使用默认值
        QString fullPath = m_mmainWindow->RecordingSettings[currentChannel].path;
        if (!fullPath.isEmpty()) {
            // 从完整路径中提取基础路径（向后兼容）
            if (fullPath.contains("/Recording/CH")) {
                basePath = fullPath.left(fullPath.indexOf("/Recording/CH"));
            } else {
                basePath = fullPath;
            }
        } else {
            basePath = "/mnt/nvme";
        }
    }

    // 构建完整的录制路径：basePath/Recording/CHx
    QString folderPath = QString("%1/Recording/CH%2").arg(basePath).arg(currentChannel + 1);

    double storageSize = m_mmainWindow->RecordingSettings[currentChannel].storageSize;
    bool videoStorage = m_mmainWindow->RecordingSettings[currentChannel].videoStorage;


    // 如果未开启存储限制，检查磁盘剩余空间
    if (!videoStorage || storageSize <= 0) {
        // 使用df命令检查磁盘剩余空间
        QProcess dfProcess;
        QString command = QString("df %1").arg(basePath);
        dfProcess.start("sh", QStringList() << "-c" << command);
        dfProcess.waitForFinished(3000); // 等待3秒

        QString output = dfProcess.readAllStandardOutput();
        QStringList lines = output.split('\n');

        if (lines.size() >= 2) {
            // 解析df输出的第二行（第一行是标题）
            QStringList fields = lines[1].split(QRegExp("\\s+"), Qt::SkipEmptyParts);
            if (fields.size() >= 4) {
                bool ok;
                qint64 availableKB = fields[3].toLongLong(&ok); // Available字段（KB）
                if (ok) {
                    double availableGB = availableKB / (1024.0 * 1024.0); // 转换为GB
                    // 如果剩余空间少于10GB，清理最早的文件
                    if (availableGB >= 10.0) {
                        return; // 空间充足返回
                    }
                } else {
                    //无法解析df输出中的可用空间信息;
                    return;
                }
            }
        }
    }

    // 检查录制目录是否存在
    QDir recordingDir(folderPath);
    if (!recordingDir.exists()) {
        return;
    }

    // 计算文件夹大小的函数（使用std::function解决递归问题）
    std::function<qint64(const QString&)> calculateDirSize = [&calculateDirSize](const QString &path) -> qint64 {
        QDir dir(path);
        qint64 size = 0;

        // 获取所有文件的大小
        QFileInfoList fileList = dir.entryInfoList(QDir::Files | QDir::NoDotAndDotDot);
        for (const QFileInfo &fi : fileList) {
            size += fi.size();
        }

        // 递归获取所有子目录的大小
        QFileInfoList dirList = dir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot);
        for (const QFileInfo &fi : dirList) {
            size += calculateDirSize(fi.absoluteFilePath());
        }

        return size;
    };

    // 计算文件夹当前大小
    qint64 totalSize = calculateDirSize(folderPath);
    double sizeInGB = totalSize / (1024.0 * 1024.0 * 1024.0);


    // 检查是否需要清理文件（存储限制超限或磁盘空间不足）
    bool needCleanup = false;
    QString cleanupReason;

    if (videoStorage && storageSize > 0 && sizeInGB >= storageSize) {
        needCleanup = true;
        cleanupReason = QString("文件夹大小超过存储限制: %1GB >= %2GB").arg(sizeInGB).arg(storageSize);
    } else if (!videoStorage || storageSize <= 0) {
        // 对于未开启存储限制的情况，在前面已经检查过磁盘空间
        // 如果执行到这里，说明磁盘空间不足，需要清理
        needCleanup = true;
        cleanupReason = "磁盘剩余空间不足10GB";
    }

    if (needCleanup) {
        printf("%s，开始清理旧文件...\n", qPrintable(cleanupReason));

        // 查找最旧的MP4文件进行删除
        QDir baseDir(folderPath);
        QStringList dateFolders = baseDir.entryList(QDir::Dirs | QDir::NoDotAndDotDot);

        if (!dateFolders.isEmpty()) {
            // 按日期排序
            std::sort(dateFolders.begin(), dateFolders.end());

            // 从最早的日期文件夹开始查找
            for (const QString& dateFolder : dateFolders) {
                QString dateFolderPath = folderPath + "/" + dateFolder;
                QDir dateDir(dateFolderPath);

                // 获取该目录下所有MP4文件并按时间排序
                QFileInfoList mp4Files = dateDir.entryInfoList(QStringList("*.mp4"), QDir::Files, QDir::Time | QDir::Reversed);

                if (!mp4Files.isEmpty()) {
                    // 删除最旧的文件
                    QFileInfo oldestFile = mp4Files.first();
                    if (QFile::remove(oldestFile.absoluteFilePath())) {
                        qDebug() << "已删除最早的文件:" << oldestFile.absoluteFilePath();
                        printf("成功删除文件: %s\n", qPrintable(oldestFile.absoluteFilePath()));
                        break; // 每次只删除一个文件，下次检查再继续
                    } else {
                        printf("删除文件失败: %s\n", qPrintable(oldestFile.absoluteFilePath()));
                    }
                }
            }
        }
    }
}

void CameraStream::pausePreview() {
    if (!pipeline) {
        // 如果pipeline为空，只更新状态标记
        m_isPreviewPaused = true;
        return;
    }

    // 直接查找特定命名的waylandsink元素
    GstElement *waylandsink = gst_bin_get_by_name(GST_BIN(pipeline), "preview_waylandsink");
    if (waylandsink) {
        // 设置透明度为0
        g_object_set(waylandsink, "alpha", 0, nullptr);
        gst_object_unref(waylandsink);
    }

    // 更新预览状态
    m_isPreviewPaused = true;
}


void CameraStream::resumePreview() {
    if (!pipeline) {
        // 如果pipeline为空，只更新状态标记
        m_isPreviewPaused = false;
        return;
    }

    // 直接查找特定命名的waylandsink元素
    GstElement *waylandsink = gst_bin_get_by_name(GST_BIN(pipeline), "preview_waylandsink");
    if (waylandsink) {
        // 恢复显示，设置透明度为1.0
        g_object_set(waylandsink, "alpha", 1.0, nullptr);
        gst_object_unref(waylandsink);
    }

    // 更新预览状态
    m_isPreviewPaused = false;
}

void CameraStream::setPreviewSize(int channel)
{
    if (!pipeline) return;  // 添加对pipeline为NULL的检查

    // 直接查找特定命名的waylandsink元素
    GstElement *waylandsink = gst_bin_get_by_name(GST_BIN(pipeline), "preview_waylandsink");
    if (!waylandsink) {
        qDebug() << "无法找到preview_waylandsink元素，无法设置预览区域";
        return;
    }

    // 获取窗口ID
    QWidget* showwidget = m_mainWindow->findChild<QWidget*>("video");
    WId winId = showwidget->winId();
    // 获取目标控件在屏幕上的全局坐标
    QPoint globalPos = showwidget->mapToGlobal(QPoint(0, 0));


    int x=0,y=0,w=0,h=0;
    if(channel < 4)
    {
        x = globalPos.x();
        y = globalPos.y();
        w = showwidget->width();
        h = showwidget->height();
    }
    else if(channel >= 4 && channel < 8)
    {
        printf("channel=%d\n",channel);
        if(channel ==5 || channel == 7)
        {
            w = showwidget->width()/2;
            h = showwidget->height();
            x = globalPos.x()+w;
            y = globalPos.y();
        }
        else
        {
            w = showwidget->width()/2;
            h = showwidget->height();
            x = globalPos.x();
            y = globalPos.y();
        }
    }
    else
    {
        if(channel == 8)
        {
            w = showwidget->width()/2;
            h = showwidget->height()/2;
            x = globalPos.x();
            y = globalPos.y();
        }
        else if(channel == 9)
        {
            w = showwidget->width()/2;
            h = showwidget->height()/2;
            x = globalPos.x()+w;
            y = globalPos.y();
        }
        else if(channel == 10)
        {
            w = showwidget->width()/2;
            h = showwidget->height()/2;
            x = globalPos.x();
            y = globalPos.y()+h;
        }
        else if(channel == 11)
        {
            w = showwidget->width()/2;
            h = showwidget->height()/2;
            x = globalPos.x()+w;
            y = globalPos.y()+h;
        }
    }
    // 定义矩形坐标和尺寸
    gint rect[4] = {x, y, w ,h};  // x, y, width, height

    // 初始化GValue为GstValueArray类型
    GValue val = G_VALUE_INIT;
    g_value_init(&val, GST_TYPE_ARRAY);

    // 添加四个整数值到数组中
    for (int i = 0; i < 4; i++) {
        GValue elem = G_VALUE_INIT;
        g_value_init(&elem, G_TYPE_INT);
        g_value_set_int(&elem, rect[i]);
        gst_value_array_append_value(&val, &elem);
        g_value_unset(&elem);
    }

    // 设置属性
    //printf("x:%d,y:%d,w:%d,h:%d\n",x,y,w,h);
    g_object_set_property(G_OBJECT(waylandsink), "render-rectangle", &val);

    // 清理
    g_value_unset(&val);
    gst_object_unref(waylandsink);
}

void CameraStream::setfullPreviewSize(int channel)
{
    if (!pipeline) return;  // 添加对pipeline为NULL的检查

    // 直接查找特定命名的waylandsink元素
    GstElement *waylandsink = gst_bin_get_by_name(GST_BIN(pipeline), "preview_waylandsink");
    if (!waylandsink) {
        qDebug() << "无法找到preview_waylandsink元素，无法设置预览区域";
        return;
    }
    if (waylandsink) {
        // 恢复显示，设置透明度为1.0
        g_object_set(waylandsink, "layer", 2, nullptr);
        //gst_object_unref(waylandsink);
    }
    // 获取窗口ID
    QWidget* showwidget = m_mainWindow->findChild<QWidget*>("centralwidget");
    WId winId = showwidget->winId();
    // 获取目标控件在屏幕上的全局坐标
    QPoint globalPos = showwidget->mapToGlobal(QPoint(0, 0));
    int x=0,y=0,w=0,h=0;
    if(channel < 4)
    {
        x = 0;
        y = 0;
        w = showwidget->width();
        h = showwidget->height();
        qDebug()<<w<<h;
    }
    else if(channel >= 4 && channel < 8)
    {
        printf("channel=%d\n",channel);
        if(channel ==5 || channel == 7)
        {
            w = showwidget->width()/2;
            h = showwidget->height();
            x = globalPos.x()+w;
            y = globalPos.y();
        }
        else
        {
            w = showwidget->width()/2;
            h = showwidget->height();
            x = globalPos.x();
            y = globalPos.y();
        }
    }
    else
    {
        if(channel == 8)
        {
            w = showwidget->width()/2;
            h = showwidget->height()/2;
            x = globalPos.x();
            y = globalPos.y();
        }
        else if(channel == 9)
        {
            w = showwidget->width()/2;
            h = showwidget->height()/2;
            x = globalPos.x()+w;
            y = globalPos.y();
        }
        else if(channel == 10)
        {
            w = showwidget->width()/2;
            h = showwidget->height()/2;
            x = globalPos.x();
            y = globalPos.y()+h;
        }
        else if(channel == 11)
        {
            w = showwidget->width()/2;
            h = showwidget->height()/2;
            x = globalPos.x()+w;
            y = globalPos.y()+h;
        }
    }
    // 定义矩形坐标和尺寸
    gint rect[4] = {x, y, w ,h};  // x, y, width, height

    // 初始化GValue为GstValueArray类型
    GValue val = G_VALUE_INIT;
    g_value_init(&val, GST_TYPE_ARRAY);

    // 添加四个整数值到数组中
    for (int i = 0; i < 4; i++) {
        GValue elem = G_VALUE_INIT;
        g_value_init(&elem, G_TYPE_INT);
        g_value_set_int(&elem, rect[i]);
        gst_value_array_append_value(&val, &elem);
        g_value_unset(&elem);
    }

    // 设置属性
    printf("x:%d,y:%d,w:%d,h:%d\n",x,y,w,h);
    g_object_set_property(G_OBJECT(waylandsink), "render-rectangle", &val);

    // 清理
    g_value_unset(&val);
    gst_object_unref(waylandsink);
}

void CameraStream::rotateCamera()
{
    if (!pipeline || !flip) {
        printf("无法旋转：管道或flip元素未初始化\n");
        return;
    }

    // 检查管道状态
    GstState current_state, pending_state;
    GstStateChangeReturn ret = gst_element_get_state(pipeline, &current_state, &pending_state, 0);

    if (ret == GST_STATE_CHANGE_FAILURE || current_state != GST_STATE_PLAYING) {
        printf("管道状态不正确，无法旋转\n");
        return;
    }

    // 检查当前是否正在录像，录像时不允许旋转
    if (isRecording) {
        printf("录像进行中，暂时不允许旋转\n");
        return;
    }

    rotate_direction++;
    if(rotate_direction == 4)
    {
        rotate_direction = 0;
    }

    printf("设置旋转方向: %d\n", rotate_direction);

    // 更安全的旋转方法：使用probe来确保在合适的时机设置属性
    GstPad *flip_sink_pad = gst_element_get_static_pad(flip, "sink");
    if (flip_sink_pad) {
        // 添加一个probe来在下一个buffer到达时设置旋转
        gst_pad_add_probe(flip_sink_pad, GST_PAD_PROBE_TYPE_BUFFER,
                          [](GstPad *pad, GstPadProbeInfo *info, gpointer user_data) -> GstPadProbeReturn {
                              CameraStream *stream = static_cast<CameraStream*>(user_data);
                              if (stream && stream->flip) {
                                  g_object_set(stream->flip, "video-direction", stream->rotate_direction, nullptr);
                                  printf("旋转设置完成: %d\n", stream->rotate_direction);
                              }
                              return GST_PAD_PROBE_REMOVE; // 移除probe，只执行一次
                          }, this, nullptr);
        gst_object_unref(flip_sink_pad);
    } else {
        // 如果无法获取pad，则直接设置（可能有风险）
        printf("警告：无法获取flip sink pad，直接设置旋转属性\n");
        g_object_set(flip, "video-direction", rotate_direction, nullptr);
    }
}

// 创建拍照用的分支和appsink
GstElement* CameraStream::createPhotoSink()
{
    if (!pipeline || !tee) return nullptr;

    if (m_photoBranchCreated) {
        return gst_bin_get_by_name(GST_BIN(pipeline), "photo_sink");
    }

    GstElement* valve = gst_element_factory_make("valve", "photo_valve");
    GstElement* queue = gst_element_factory_make("queue", "queue_photo");
    GstElement* convert = gst_element_factory_make("videoconvert", "convert_photo");
    GstElement* appsink = gst_element_factory_make("appsink", "photo_sink");

    if (!valve || !queue || !convert || !appsink) {
        qWarning() << "拍照元素创建失败";
        return nullptr;
    }

    GstCaps* caps = gst_caps_new_simple("video/x-raw",
                                        "format", G_TYPE_STRING, "RGB",
                                        NULL);
    g_object_set(appsink,
                 "emit-signals", TRUE,   // 启用信号
                 "sync", FALSE,
                 "drop", TRUE,
                 "max-buffers", 1,
                 "caps", caps,
                 NULL);
    gst_caps_unref(caps);

    // 连接new-sample信号到回调函数
    g_signal_connect(appsink, "new-sample", G_CALLBACK(photoSampleCallback), this);

    g_object_set(valve, "drop", TRUE, NULL); // 默认关闭

    gst_bin_add_many(GST_BIN(pipeline), valve, queue, convert, appsink, NULL);
    gst_element_link_many(valve, queue, convert, appsink, NULL);

    GstPadTemplate* templ = gst_element_class_get_pad_template(GST_ELEMENT_GET_CLASS(tee), "src_%u");
    GstPad* tee_pad = gst_element_request_pad(tee, templ, NULL, NULL);
    GstPad* valve_sink_pad = gst_element_get_static_pad(valve, "sink");
    gst_pad_link(tee_pad, valve_sink_pad);
    gst_object_unref(tee_pad);
    gst_object_unref(valve_sink_pad);

    gst_element_sync_state_with_parent(valve);
    gst_element_sync_state_with_parent(queue);
    gst_element_sync_state_with_parent(convert);
    gst_element_sync_state_with_parent(appsink);

    m_photoBranchCreated = true;
    return appsink;
}

// 销毁拍照分支
void CameraStream::destroyPhotoSink(GstElement* photo_sink)
{
    if (!pipeline || !photo_sink) {
        return;
    }

    printf("正在销毁拍照分支...\n");

    // 获取之前存储的pad和元素引用
    GstPad *tee_photo_pad = (GstPad*)g_object_get_data(G_OBJECT(photo_sink), "tee-pad");
    GstElement *queue_photo = (GstElement*)g_object_get_data(G_OBJECT(photo_sink), "queue");
    GstElement *convert_photo = (GstElement*)g_object_get_data(G_OBJECT(photo_sink), "convert");

    if (!tee_photo_pad || !queue_photo || !convert_photo) {
        printf("无法获取拍照分支的引用\n");
        return;
    }

    // 将元素状态设置为NULL
    gst_element_set_state(photo_sink, GST_STATE_NULL);
    gst_element_set_state(convert_photo, GST_STATE_NULL);
    gst_element_set_state(queue_photo, GST_STATE_NULL);

    // 断开pad连接
    GstPad *queue_sink_pad = gst_element_get_static_pad(queue_photo, "sink");
    if (queue_sink_pad) {
        gst_pad_unlink(tee_photo_pad, queue_sink_pad);
        gst_object_unref(queue_sink_pad);
    }

    // 释放tee的请求pad
    gst_element_release_request_pad(tee, tee_photo_pad);
    gst_object_unref(tee_photo_pad);

    // 从管道中移除元素
    gst_bin_remove_many(GST_BIN(pipeline), queue_photo, convert_photo, photo_sink, NULL);

    printf("拍照分支已销毁\n");
}

// 定时器触发后销毁photo_sink的槽函数
void CameraStream::destroyPhotoSinkDelayed()
{
    printf("定时器触发，销毁拍照分支\n");
    if (m_cached_photo_sink) {
        destroyPhotoSink(m_cached_photo_sink);
        m_cached_photo_sink = nullptr;
    }
}

// 拍照回调函数 - 当appsink有新帧时自动调用
GstFlowReturn CameraStream::photoSampleCallback(GstElement *sink, gpointer data)
{
    CameraStream* stream = static_cast<CameraStream*>(data);
    if (!stream || !stream->m_photoRequested) {
        return GST_FLOW_OK; // 如果没有请求拍照，直接返回
    }

    // 获取sample
    GstSample* sample = gst_app_sink_pull_sample(GST_APP_SINK(sink));
    if (!sample) {
        qWarning() << "无法获取拍照sample";
        return GST_FLOW_OK;
    }

    GstBuffer* buffer = gst_sample_get_buffer(sample);
    GstCaps* caps = gst_sample_get_caps(sample);

    if (buffer && caps) {
        GstMapInfo map;
        if (gst_buffer_map(buffer, &map, GST_MAP_READ)) {
            GstStructure* s = gst_caps_get_structure(caps, 0);
            int width = 0, height = 0;
            gst_structure_get_int(s, "width", &width);
            gst_structure_get_int(s, "height", &height);

            // 获取MainWindow引用
            MainWindow* mw = qobject_cast<MainWindow*>(stream->parent());
            if (mw) {
                QString basePath = mw->basePath.isEmpty() ? "/mnt/nvme" : mw->basePath;
                QString dirPath = QString("%1/Picture/CH%2/%3")
                                      .arg(basePath)
                                      .arg(stream->channelstream + 1)
                                      .arg(QDateTime::currentDateTime().toString("yyyy_MM_dd"));
                QDir().mkpath(dirPath);

                QString filePath = dirPath + "/" +
                                   QDateTime::currentDateTime().toString("yyyy-MM-dd_hh-mm-ss-zzz") + ".jpg";

                QImage img(map.data, width, height, QImage::Format_RGB888);
                if (img.save(filePath, "JPG", 85)) {
                    qDebug() << "Photo OK:" << filePath;
                } else {
                    qWarning() << "save Photo error:" << filePath;
                }
            }

            gst_buffer_unmap(buffer, &map);
        }
    }

    gst_sample_unref(sample);

    // 拍照完成，重置标志并停用管道
    stream->m_photoRequested = false;
    stream->deactivatePhotoPipeline();

    return GST_FLOW_OK;
}

// 激活拍照管道
void CameraStream::activatePhotoPipeline()
{
    if (m_photoPipelineActive) {
        return; // 已经激活
    }

    GstElement* valve = gst_bin_get_by_name(GST_BIN(pipeline), "photo_valve");
    if (valve) {
        g_object_set(valve, "drop", FALSE, NULL); // 打开阀门
        gst_object_unref(valve);
        m_photoPipelineActive = true;
        printf("拍照管道已激活\n");
    }
}

// 停用拍照管道
void CameraStream::deactivatePhotoPipeline()
{
    if (!m_photoPipelineActive) {
        return; // 已经停用
    }

    GstElement* valve = gst_bin_get_by_name(GST_BIN(pipeline), "photo_valve");
    if (valve) {
        g_object_set(valve, "drop", TRUE, NULL); // 关闭阀门
        gst_object_unref(valve);
        m_photoPipelineActive = false;
        qDebug() << "拍照管道已停用";
    }
}

void CameraStream::takePhoto()
{
    MainWindow* mw = qobject_cast<MainWindow*>(parent());
    if (!mw || !pipeline || !tee) {
        qWarning() << "拍照失败: 必要元素未初始化";
        return;
    }

    // 如果已经有拍照请求在处理中，忽略新请求
    if (m_photoRequested) {
        qDebug() << "拍照请求已在处理中，忽略新请求";
        return;
    }

    // 创建拍照分支（只一次）
    GstElement* appsink = createPhotoSink();
    if (!appsink) {
        qWarning() << "无法创建拍照分支";
        return;
    }

    // 设置拍照请求标志
    m_photoRequested = true;

    // 激活拍照管道，回调函数会自动处理拍照
    activatePhotoPipeline();

    printf("拍照请求已提交，等待回调处理...\n");
}

// 创建音频分支
bool CameraStream::createAudioBranch(const QString &audioDevice)
{
    if (!pipeline) {
        printf("无法创建音频分支：pipeline未初始化\n");
        return false;
    }

    if (audioDevice.isEmpty()) {
        printf("音频设备路径为空，跳过音频分支创建\n");
        return true; // 不是错误，只是没有音频
    }

    printf("正在创建音频分支，设备: %s\n", qPrintable(audioDevice));

    // 创建音频元素
    m_audio_source = gst_element_factory_make("alsasrc", "audio_source");
    m_audio_queue = gst_element_factory_make("queue", "audio_queue");
    m_audio_convert = gst_element_factory_make("audioconvert", "audio_convert");
    m_audio_encoder = gst_element_factory_make("voaacenc", "audio_encoder");
    m_audio_parser = gst_element_factory_make("aacparse", "audio_parser");

    if (!m_audio_source || !m_audio_queue || !m_audio_convert || !m_audio_encoder || !m_audio_parser) {
        printf("无法创建音频分支的元素\n");
        destroyAudioBranch();
        return false;
    }

    // 配置音频源
    g_object_set(m_audio_source,
                 "device", qPrintable(audioDevice),
                 NULL);

    // 配置音频队列
    g_object_set(m_audio_queue,
                 "max-size-buffers", 200,
                 "max-size-time", 2 * GST_SECOND,
                 "max-size-bytes", 0,
                 "leaky", 2, // 下游丢帧
                 NULL);

    // 配置音频编码器
    g_object_set(m_audio_encoder,
                 "bitrate", 128000, // 128kbps
                 NULL);

    // 将音频元素添加到管道
    gst_bin_add_many(GST_BIN(pipeline), m_audio_source, m_audio_queue,
                     m_audio_convert, m_audio_encoder, m_audio_parser, NULL);

    // 简化音频链接，让GStreamer自动协商格式
    if (!gst_element_link_many(m_audio_source, m_audio_queue, m_audio_convert,
                               m_audio_encoder, m_audio_parser, NULL)) {
        printf("无法链接音频分支元素\n");
        destroyAudioBranch();
        return false;
    }

    // 同步元素状态
    gst_element_sync_state_with_parent(m_audio_source);
    gst_element_sync_state_with_parent(m_audio_queue);
    gst_element_sync_state_with_parent(m_audio_convert);
    gst_element_sync_state_with_parent(m_audio_encoder);
    gst_element_sync_state_with_parent(m_audio_parser);

    m_audio_device_path = audioDevice;
    printf("音频分支创建成功\n");

    return true;
}

// 销毁音频分支
void CameraStream::destroyAudioBranch()
{
    if (!m_audio_source && !m_audio_queue && !m_audio_convert && !m_audio_encoder && !m_audio_parser) {
        return; // 没有音频分支需要销毁
    }

    printf("正在销毁音频分支...\n");

    // 停止音频元素
    if (m_audio_source) {
        gst_element_set_state(m_audio_source, GST_STATE_NULL);
    }
    if (m_audio_queue) {
        gst_element_set_state(m_audio_queue, GST_STATE_NULL);
    }
    if (m_audio_convert) {
        gst_element_set_state(m_audio_convert, GST_STATE_NULL);
    }
    if (m_audio_encoder) {
        gst_element_set_state(m_audio_encoder, GST_STATE_NULL);
    }
    if (m_audio_parser) {
        gst_element_set_state(m_audio_parser, GST_STATE_NULL);
    }

    // 从管道中移除音频元素
    if (pipeline) {
        if (m_audio_source) gst_bin_remove(GST_BIN(pipeline), m_audio_source);
        if (m_audio_queue) gst_bin_remove(GST_BIN(pipeline), m_audio_queue);
        if (m_audio_convert) gst_bin_remove(GST_BIN(pipeline), m_audio_convert);
        if (m_audio_encoder) gst_bin_remove(GST_BIN(pipeline), m_audio_encoder);
        if (m_audio_parser) gst_bin_remove(GST_BIN(pipeline), m_audio_parser);
    }

    // 清空引用
    m_audio_source = nullptr;
    m_audio_queue = nullptr;
    m_audio_convert = nullptr;
    m_audio_encoder = nullptr;
    m_audio_parser = nullptr;
    m_audio_device_path.clear();

    printf("音频分支已销毁\n");
}

// 激活音频分支（连接到splitmuxsink）
bool CameraStream::activateAudioBranch()
{
    if (!m_audio_parser || !m_cached_record_branch) {
        printf("无法激活音频分支：音频parser或录像分支未初始化\n");
        return false;
    }

    // 等待音频元素准备就绪
    gst_element_get_state(m_audio_parser, NULL, NULL, 1 * GST_SECOND);

    // 获取音频parser的src pad
    GstPad *audio_src_pad = gst_element_get_static_pad(m_audio_parser, "src");
    if (!audio_src_pad) {
        printf("无法获取音频parser的src pad\n");
        return false;
    }

    // 获取splitmuxsink的audio sink pad
    GstPad *mux_audio_pad = gst_element_get_request_pad(m_cached_record_branch, "audio_%u");
    if (!mux_audio_pad) {
        printf("无法获取splitmuxsink的audio pad\n");
        gst_object_unref(audio_src_pad);
        return false;
    }

    // 链接音频到muxer
    GstPadLinkReturn link_result = gst_pad_link(audio_src_pad, mux_audio_pad);
    if (link_result != GST_PAD_LINK_OK) {
        printf("无法链接音频到muxer，错误代码: %d\n", link_result);

        // 打印更详细的错误信息
        switch (link_result) {
        case GST_PAD_LINK_WRONG_HIERARCHY:
            printf("错误：pad不在同一管道中\n");
            break;
        case GST_PAD_LINK_WAS_LINKED:
            printf("错误：pad已经连接\n");
            break;
        case GST_PAD_LINK_WRONG_DIRECTION:
            printf("错误：pad方向错误\n");
            break;
        case GST_PAD_LINK_NOFORMAT:
            printf("错误：pad格式不兼容\n");
            break;
        case GST_PAD_LINK_NOSCHED:
            printf("错误：调度器问题\n");
            break;
        case GST_PAD_LINK_REFUSED:
            printf("错误：连接被拒绝\n");
            break;
        default:
            printf("错误：未知连接错误\n");
            break;
        }

        gst_object_unref(audio_src_pad);
        gst_object_unref(mux_audio_pad);
        return false;
    }

    gst_object_unref(audio_src_pad);
    gst_object_unref(mux_audio_pad);

    printf("音频分支已激活\n");
    return true;
}

// 插入水印隔离元素到预览分支
bool CameraStream::insertWatermarkIsolationElements()
{
    // 不再需要水印隔离，因为时间水印在tee之前添加，预览和录像都会显示
    printf("水印隔离已禁用：时间水印在解码后统一添加\n");
    return true;
}

// 移除水印隔离元素（恢复原始链路）
bool CameraStream::removeWatermarkIsolationElements()
{
    // 不再需要水印隔离，因为时间水印在tee之前添加
    printf("水印隔离已禁用：时间水印在解码后统一添加\n");
    return true;
}

// 动态插入时间水印元素
bool CameraStream::insertTimeWatermark()
{
    if (!pipeline || !m_convert || !tee) {
        printf("无法插入时间水印：必要元素未初始化\n");
        return false;
    }

    if (m_timeoverlay) {
        printf("时间水印已存在，无需重复插入\n");
        return true;
    }

    printf("开始动态插入时间水印元素...\n");

    // 创建时间水印元素
    m_timeoverlay = gst_element_factory_make("clockoverlay", "global_timeoverlay");
    if (!m_timeoverlay) {
        printf("无法创建时间水印元素\n");
        return false;
    }

    // 配置时间水印
    g_object_set(m_timeoverlay,
                 "halignment", 2, // 右对齐
                 "time-format", "%Y-%m-%d %H:%M:%S",
                 nullptr);

    // 暂停管道以进行重新链接
    GstState current_state;
    gst_element_get_state(pipeline, &current_state, NULL, 0);
    gst_element_set_state(pipeline, GST_STATE_PAUSED);

    // 等待状态变化完成
    gst_element_get_state(pipeline, NULL, NULL, GST_CLOCK_TIME_NONE);

    // 安全地断开m_convert和tee的连接
    GstPad *convert_src = gst_element_get_static_pad(m_convert, "src");
    GstPad *tee_sink = gst_element_get_static_pad(tee, "sink");

    if (convert_src && tee_sink && gst_pad_is_linked(convert_src)) {
        gst_pad_unlink(convert_src, tee_sink);
    }

    if (convert_src) gst_object_unref(convert_src);
    if (tee_sink) gst_object_unref(tee_sink);

    // 将时间水印元素添加到管道
    gst_bin_add(GST_BIN(pipeline), m_timeoverlay);

    // 重新链接：m_convert -> timeoverlay -> tee
    if (!gst_element_link_many(m_convert, m_timeoverlay, tee, nullptr)) {
        printf("无法链接时间水印元素\n");

        // 清理失败的元素
        gst_element_set_state(m_timeoverlay, GST_STATE_NULL);
        gst_bin_remove(GST_BIN(pipeline), m_timeoverlay);
        m_timeoverlay = nullptr;

        // 恢复原始连接
        gst_element_link(m_convert, tee);
        gst_element_set_state(pipeline, current_state);
        return false;
    }

    // 同步元素状态
    gst_element_sync_state_with_parent(m_timeoverlay);

    // 恢复管道状态
    gst_element_set_state(pipeline, current_state);

    printf("时间水印元素插入成功\n");
    return true;
}

// 动态移除时间水印元素
bool CameraStream::removeTimeWatermark()
{
    if (!pipeline || !m_convert || !tee) {
        printf("无法移除时间水印：必要元素未初始化\n");
        return false;
    }

    if (!m_timeoverlay) {
        printf("时间水印不存在，无需移除\n");
        return true;
    }

    printf("开始动态移除时间水印元素...\n");

    // 暂停管道以进行重新链接
    GstState current_state;
    gst_element_get_state(pipeline, &current_state, NULL, 0);
    gst_element_set_state(pipeline, GST_STATE_PAUSED);

    // 等待状态变化完成
    gst_element_get_state(pipeline, NULL, NULL, GST_CLOCK_TIME_NONE);

    // 安全地断开连接（检查连接是否存在）
    GstPad *convert_src = gst_element_get_static_pad(m_convert, "src");
    GstPad *timeoverlay_sink = gst_element_get_static_pad(m_timeoverlay, "sink");
    GstPad *timeoverlay_src = gst_element_get_static_pad(m_timeoverlay, "src");
    GstPad *tee_sink = gst_element_get_static_pad(tee, "sink");

    if (convert_src && timeoverlay_sink && gst_pad_is_linked(convert_src)) {
        gst_pad_unlink(convert_src, timeoverlay_sink);
    }
    if (timeoverlay_src && tee_sink && gst_pad_is_linked(timeoverlay_src)) {
        gst_pad_unlink(timeoverlay_src, tee_sink);
    }

    // 释放pad引用
    if (convert_src) gst_object_unref(convert_src);
    if (timeoverlay_sink) gst_object_unref(timeoverlay_sink);
    if (timeoverlay_src) gst_object_unref(timeoverlay_src);
    if (tee_sink) gst_object_unref(tee_sink);

    // 设置时间水印元素状态为NULL并移除
    gst_element_set_state(m_timeoverlay, GST_STATE_NULL);
    gst_element_get_state(m_timeoverlay, NULL, NULL, GST_CLOCK_TIME_NONE);

    // 从bin中移除（这会自动管理引用计数）
    gst_bin_remove(GST_BIN(pipeline), m_timeoverlay);
    m_timeoverlay = nullptr;

    // 直接连接m_convert和tee
    if (!gst_element_link(m_convert, tee)) {
        printf("无法重新连接m_convert和tee\n");
        gst_element_set_state(pipeline, current_state);
        return false;
    }

    // 恢复管道状态
    gst_element_set_state(pipeline, current_state);

    printf("时间水印元素移除成功\n");
    return true;
}

// 根据timewatermark状态动态更新时间水印
void CameraStream::updateTimeWatermark()
{
    if (!pipeline) {
        printf("管道未初始化，无法更新时间水印\n");
        return;
    }

    // 检查管道状态，避免在清理过程中操作
    GstState current_state;
    GstStateChangeReturn ret = gst_element_get_state(pipeline, &current_state, NULL, 0);
    if (ret == GST_STATE_CHANGE_FAILURE || current_state == GST_STATE_NULL) {
        printf("管道状态异常，跳过时间水印更新\n");
        return;
    }

    MainWindow* m_mmainWindow = qobject_cast<MainWindow*>(parent());
    if (!m_mmainWindow) {
        printf("无法获取MainWindow引用\n");
        return;
    }

    int currentChannel = channelstream;
    if (currentChannel < 0 || currentChannel >= 4) {
        printf("无效的通道索引: %d\n", currentChannel);
        return;
    }

    bool enableTimeWatermark = m_mmainWindow->timewatermark[currentChannel];
    bool currentlyHasWatermark = (m_timeoverlay != nullptr);


    if (enableTimeWatermark && !currentlyHasWatermark) {
        // 需要启用但当前没有，插入时间水印
        printf("→ 执行操作：插入时间水印元素\n");
        if (insertTimeWatermark()) {
            printf("✓ 时间水印插入成功\n");
        } else {
            printf("✗ 时间水印插入失败\n");
        }
    } else if (!enableTimeWatermark && currentlyHasWatermark) {
        // 需要禁用但当前有，移除时间水印
        printf("→ 执行操作：移除时间水印元素\n");
        if (removeTimeWatermark()) {
            printf("✓ 时间水印移除成功\n");
        } else {
            printf("✗ 时间水印移除失败\n");
        }
    } else {
        // 状态一致，无需操作
        printf("→ 状态一致，无需更改\n");
    }
    printf("=== 时间水印状态检查完成 ===\n");
}

// 初始化后恢复时间水印状态
void CameraStream::restoreTimeWatermarkOnInit()
{
    printf("=== 初始化后恢复时间水印状态 ===\n");

    if (!pipeline) {
        printf("管道未初始化，延迟恢复时间水印\n");
        // 如果管道还没准备好，再延迟一点时间重试
        QTimer::singleShot(300, this, &CameraStream::restoreTimeWatermarkOnInit);
        return;
    }

    // 检查管道状态
    GstState current_state;
    GstStateChangeReturn ret = gst_element_get_state(pipeline, &current_state, NULL, 100 * GST_MSECOND);

    if (ret == GST_STATE_CHANGE_FAILURE) {
        printf("管道状态获取失败，延迟恢复时间水印\n");
        QTimer::singleShot(300, this, &CameraStream::restoreTimeWatermarkOnInit);
        return;
    }

    if (current_state != GST_STATE_PLAYING) {
        printf("管道未处于PLAYING状态(%s)，延迟恢复时间水印\n",
               gst_element_state_get_name(current_state));
        QTimer::singleShot(300, this, &CameraStream::restoreTimeWatermarkOnInit);
        return;
    }

    printf("管道状态正常，开始恢复时间水印状态\n");
    updateTimeWatermark();
}
