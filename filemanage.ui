<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FileManage</class>
 <widget class="QDialog" name="FileManage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>852</width>
    <height>673</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0" colspan="2">
    <widget class="QWidget" name="widget" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>24</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>24</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(120, 120, 120);</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLabel" name="label">
        <property name="font">
         <font>
          <family>aakar</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>文件管理</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>733</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="close1">
        <property name="minimumSize">
         <size>
          <width>25</width>
          <height>24</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>25</width>
          <height>24</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton{
	image: url(:/image/关闭.png);
border-style:none;
padding:5px;

background:transparent;
}
QPushButton:hover{
	background-color: rgb(239, 41, 41);
}</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QWidget" name="widget_2" native="true">
     <property name="minimumSize">
      <size>
       <width>100</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(90, 90, 90);</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_5">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_4">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <property name="spacing">
           <number>40</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout">
            <item>
             <widget class="QPushButton" name="picture">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>30</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>100</width>
                <height>30</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>照片</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="autoExclusive">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="video">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>30</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>100</width>
                <height>30</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>视频</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="autoExclusive">
               <bool>true</bool>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="verticalSpacer_2">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Fixed</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>60</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_2">
            <item>
             <widget class="QPushButton" name="CH1">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>通道1</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="autoExclusive">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="CH2">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>通道2</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="autoExclusive">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="CH3">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>通道3</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="autoExclusive">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="CH4">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>通道4</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="autoExclusive">
               <bool>true</bool>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>358</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="tfcard">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>40</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>100</width>
            <height>40</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}color: rgb(255, 255, 255);</string>
          </property>
          <property name="text">
           <string>T-Flash</string>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="1">
    <widget class="QWidget" name="widget_3" native="true">
     <property name="styleSheet">
      <string notr="true">background-color: rgb(90, 90, 90);</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <property name="leftMargin">
       <number>2</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="verticalSpacing">
       <number>4</number>
      </property>
      <item row="0" column="0">
       <widget class="QPushButton" name="Choosedate">
        <property name="minimumSize">
         <size>
          <width>750</width>
          <height>50</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>750</width>
          <height>50</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>26</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton::pressed,QPushButton::checked{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);

border:none;
}
QPushButton:hover{
	
	color: rgb(0, 0, 0);
	
	background-color: rgb(255, 255, 255);
}
QPushButton{border-radius:1px;

	color: rgb(255, 255, 255);
	background-color: rgb(90, 90, 90);
border: 1px solid rgb(0, 170, 255);
}color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>选    择    日    期</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <widget class="QLineEdit" name="lineEdit_search">
          <property name="minimumSize">
           <size>
            <width>630</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QLineEdit {
	color: rgb(0, 0, 0);
	background-color: rgb(255, 255, 255);
border: 0px solid #CCCCCC;
border-radius: 6px;
padding: 6px;
}
QLineEdit:disabled {
       background-color: #E0E0E0;
       color: #999999;
}</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Expanding</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>18</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <item>
           <widget class="QPushButton" name="pushButton_9">
            <property name="minimumSize">
             <size>
              <width>30</width>
              <height>30</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>30</width>
              <height>30</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton{

	image: url(:/image/icons/搜索.png);



border-radius: 6px;



}

QPushButton::pressed,QPushButton::checked{

	image: url(:/image/icons/搜索(闪).png);

}</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <property name="spacing">
             <number>0</number>
            </property>
            <item>
             <widget class="QPushButton" name="LB">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton{

	image: url(:/image/icons/列表.png);



border-radius: 6px;



}

QPushButton::pressed,QPushButton::checked{

	image: url(:/image/icons/列表(闪).png);

}</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
              <property name="autoExclusive">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="SLT">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton{

	image: url(:/image/icons/缩略图.png);



border-radius: 6px;



}

QPushButton::pressed,QPushButton::checked{

	image: url(:/image/icons/缩略图(闪).png);

}</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="autoExclusive">
               <bool>true</bool>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item row="2" column="0">
       <widget class="QWidget" name="widget_4" native="true">
        <property name="styleSheet">
         <string notr="true">background-color: rgb(136, 138, 133);</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_6">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QWidget" name="widget_5" native="true">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>22</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>22</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(120, 120, 120);</string>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_5">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>6</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="label_2">
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>日期</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_3">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>689</width>
                <height>19</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="close2">
              <property name="minimumSize">
               <size>
                <width>22</width>
                <height>22</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>22</width>
                <height>22</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton{
	image: url(:/image/关闭.png);
border-style:none;
padding:5px;

background:transparent;
}
QPushButton:hover{
	background-color: rgb(239, 41, 41);
}</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QCalendarWidget" name="calendarWidget">
           <property name="styleSheet">
            <string notr="true">QCalendarWidget QWidget {
            alternate-background-color: #f0f0f0;
        }

        QCalendarWidget QToolButton {
            
	background-color: rgb(85, 170, 255);
            color: white;
            font-size: 16px;
            icon-size: 24px, 24px;
        }

        QCalendarWidget QMenu {
            background-color: white;
            color: black;
        }

        QCalendarWidget QSpinBox {
            
	background-color: rgb(85, 170, 255);
            color: white;
	selection-background-color: rgb(85, 170, 255);
        }

        QCalendarWidget QAbstractItemView:enabled {
            background-color: white;
            color: black;
            
	selection-background-color: rgb(85, 170, 255);
            selection-color: white;
        }

        QCalendarWidget QAbstractItemView:disabled {
            background-color: #f0f0f0;
            color: #808080;
        }
QToolButton#qt_calendar_prevmonth {
    background: transparent;
    border: none;
    width: 40px;
	qproperty-icon: url(:/new/prefix1/image/icons/左箭头.png);
}
QToolButton#qt_calendar_nextmonth {
    background: transparent;
    border: none;
    width: 40px;
    qproperty-icon: url(:/new/prefix1/image/icons/右箭头.png);
}</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QListView" name="listView">
        <property name="styleSheet">
         <string notr="true">background-color: rgb(255, 255, 255);</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QTreeView" name="treeView">
        <property name="styleSheet">
         <string notr="true">background-color: rgb(255, 255, 255);</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>close1</sender>
   <signal>clicked()</signal>
   <receiver>FileManage</receiver>
   <slot>closewindow()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>839</x>
     <y>12</y>
    </hint>
    <hint type="destinationlabel">
     <x>907</x>
     <y>18</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>close2</sender>
   <signal>clicked()</signal>
   <receiver>FileManage</receiver>
   <slot>closedate()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>847</x>
     <y>128</y>
    </hint>
    <hint type="destinationlabel">
     <x>899</x>
     <y>127</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>Choosedate</sender>
   <signal>clicked()</signal>
   <receiver>FileManage</receiver>
   <slot>SelectDate()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>385</x>
     <y>44</y>
    </hint>
    <hint type="destinationlabel">
     <x>386</x>
     <y>-78</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>picture</sender>
   <signal>clicked()</signal>
   <receiver>FileManage</receiver>
   <slot>onPictureButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>60</x>
     <y>48</y>
    </hint>
    <hint type="destinationlabel">
     <x>-107</x>
     <y>40</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>video</sender>
   <signal>clicked()</signal>
   <receiver>FileManage</receiver>
   <slot>onVideoButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>28</x>
     <y>71</y>
    </hint>
    <hint type="destinationlabel">
     <x>-259</x>
     <y>67</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>CH1</sender>
   <signal>clicked()</signal>
   <receiver>FileManage</receiver>
   <slot>onChannel1ButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>54</x>
     <y>274</y>
    </hint>
    <hint type="destinationlabel">
     <x>-109</x>
     <y>161</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>CH2</sender>
   <signal>clicked()</signal>
   <receiver>FileManage</receiver>
   <slot>onChannel2ButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>48</x>
     <y>320</y>
    </hint>
    <hint type="destinationlabel">
     <x>-116</x>
     <y>196</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>CH3</sender>
   <signal>clicked()</signal>
   <receiver>FileManage</receiver>
   <slot>onChannel3ButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>64</x>
     <y>366</y>
    </hint>
    <hint type="destinationlabel">
     <x>-213</x>
     <y>248</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>CH4</sender>
   <signal>clicked()</signal>
   <receiver>FileManage</receiver>
   <slot>onChannel4ButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>34</x>
     <y>412</y>
    </hint>
    <hint type="destinationlabel">
     <x>-116</x>
     <y>302</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>calendarWidget</sender>
   <signal>clicked(QDate)</signal>
   <receiver>FileManage</receiver>
   <slot>SetSelectDate(QDate)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>774</x>
     <y>212</y>
    </hint>
    <hint type="destinationlabel">
     <x>965</x>
     <y>213</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>LB</sender>
   <signal>clicked()</signal>
   <receiver>FileManage</receiver>
   <slot>ListViewShow()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>810</x>
     <y>89</y>
    </hint>
    <hint type="destinationlabel">
     <x>939</x>
     <y>73</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>SLT</sender>
   <signal>clicked()</signal>
   <receiver>FileManage</receiver>
   <slot>TreeViewShow()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>837</x>
     <y>92</y>
    </hint>
    <hint type="destinationlabel">
     <x>922</x>
     <y>92</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButton_9</sender>
   <signal>clicked()</signal>
   <receiver>FileManage</receiver>
   <slot>SearchFile()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>768</x>
     <y>95</y>
    </hint>
    <hint type="destinationlabel">
     <x>756</x>
     <y>-36</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>tfcard</sender>
   <signal>clicked()</signal>
   <receiver>FileManage</receiver>
   <slot>onTFcardButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>19</x>
     <y>639</y>
    </hint>
    <hint type="destinationlabel">
     <x>-34</x>
     <y>640</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>closewindow()</slot>
  <slot>closedate()</slot>
  <slot>SelectDate()</slot>
  <slot>onPictureButtonClicked()</slot>
  <slot>onVideoButtonClicked()</slot>
  <slot>onChannel1ButtonClicked()</slot>
  <slot>onChannel2ButtonClicked()</slot>
  <slot>onChannel3ButtonClicked()</slot>
  <slot>onChannel4ButtonClicked()</slot>
  <slot>SetSelectDate(QDate)</slot>
  <slot>ListViewShow()</slot>
  <slot>TreeViewShow()</slot>
  <slot>SearchFile()</slot>
  <slot>onTFcardButtonClicked()</slot>
 </slots>
</ui>
