QT += core gui widgets       # 基础模块 + Widgets（如果用 QMainWindow）
# QT += qml quick             # 支持 QML
# QT += virtualkeyboard       # 虚拟键盘
QT += concurrent
# 如果你的目标平台是嵌入式设备（如 RK3568），可能需要指定平台插件
# linuxfb: Framebuffer | eglfs: OpenGL | wayland: Wayland
# qputenv("QT_QPA_PLATFORM", "linuxfb");  // 在代码中设置
greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17
CODECFORTR = UTF-8
CODECFORSRC = UTF-8
CONFIG += feature-wayland

CONFIG += xkbcommon

TRANSLATIONS += RWS_zh_CN.ts \
    RWS_ar_DZ.ts \
    RWS_de_AT.ts \
    RWS_en_AS.ts \
    RWS_es_AR.ts \
    RWS_fr_DZ.ts \
    RWS_it_IT.ts \
    RWS_ja_JP.ts \
    RWS_ko_CN.ts \
    RWS_pt_AO.ts \
    RWS_ru_BY.ts \
    RWS_tr_CY.ts \
    RWS_zh_HK.ts

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    CustomDialog.cpp \
    CustomFileSystemModel.cpp \
    CustomFilterProxyModel.cpp \
    CustomMessageBox.cpp \
    CustomProgressDialog.cpp \
    FileManage.cpp \
    aboutus.cpp \
    camera_param.cpp \
    camerastream.cpp \
    main.cpp \
    mainwindow.cpp \
    recordingset.cpp \
    systemmanager.cpp \
    systemset.cpp \
    udevmonitor.cpp \
    virtualkeyboard.cpp

HEADERS += \
    CustomDialog.h \
    CustomFileSystemModel.h \
    CustomFilterProxyModel.h \
    CustomMessageBox.h \
    CustomProgressDialog.h \
    FileManage.h \
    SysComboBoxStyle.h \
    aboutus.h \
    camera_param.h \
    camerastream.h \
    mainwindow.h \
    recordingset.h \
    systemmanager.h \
    systemset.h \
    udevmonitor.h \
    virtualkeyboard.h

FORMS += \
    aboutus.ui \
    filemanage.ui \
    mainwindow.ui \
    recordingset.ui \
    systemset.ui

# 定义 GStreamer 基路径
unix {
    GSTREAMER_DIR = /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568
    GST_LIB_DIR = /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host

    XKBCOMMON_DIR= /home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/lib

}
#/home/<USER>/rk3568_linux_release/buildroot/output/rockchip_rk3568/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore/private
# 添加头文件路径
INCLUDEPATH += $$GSTREAMER_DIR/build/gstreamer1-1.22.9
INCLUDEPATH += $$GSTREAMER_DIR/build/gstreamer1-1.22.9/build
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0
INCLUDEPATH += $$GSTREAMER_DIR/target/usr/lib/gstreamer-1.0
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10/QtGui/qpa
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.10
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.10/QtCore/qpa
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5

INCLUDEPATH += $$GST_LIB_DIR/lib/glib-2.0/include
INCLUDEPATH += $$GST_LIB_DIR/include/glib-2.0

#xkbcommon
INCLUDEPATH += $$XKBCOMMON_DIR/include/xkbcommon
LIBS += -L$$XKBCOMMON_DIR \
        -lxkbcommon \

# 添加库路径和链接选项
LIBS += -L$$GST_LIB_DIR \
        -lgstreamer-1.0 \
        -lgstbase-1.0 \
        -lgstvideo-1.0 \
        -lgstapp-1.0 \
        -lgobject-2.0 \
        -lglib-2.0 \
        -lgmodule-2.0 \
        -lgstvideo-1.0\
        -ludev

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

RESOURCES += \
    RWS.qrc

DISTFILES +=

TRANSLATIONS += \
    RWS_zh_CN.ts
